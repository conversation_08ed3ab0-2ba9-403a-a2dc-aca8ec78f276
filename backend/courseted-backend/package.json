{"name": "my-nest-project", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prepare": "husky install", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "wait-for-db": "node dist/scripts/wait-for-db.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:watch": "jest --config ./test/jest-e2e.json --watch", "test:e2e:cov": "jest --config ./test/jest-e2e.json --coverage", "test:e2e:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config ./test/jest-e2e.json --runInBand", "build:postman": "ts-node -r tsconfig-paths/register --project tsconfig.json ./src/scripts/postman.ts", "typeorm": "node ./node_modules/typeorm/cli.js -d ./dist/config/data-source.js", "migration:generate": "npm run typeorm migration:generate ./src/database/migrations/Migration", "migration:create": "ts-node ./node_modules/typeorm/cli.js migration:create", "migration:run": "yarn wait-for-db && yarn typeorm migration:run", "migration:revert": "npm run wait-for-db && npm run typeorm migration:revert", "db:drop": "yarn wait-for-db && yarn typeorm schema:drop", "seed:advanced": "ts-node -r tsconfig-paths/register --project tsconfig.json ./src/scripts/advanced-seed.ts", "seed:list": "ts-node -r tsconfig-paths/register --project tsconfig.json ./src/scripts/advanced-seed.ts -- --list", "seed:only": "ts-node -r tsconfig-paths/register --project tsconfig.json ./src/scripts/advanced-seed.ts -- --only"}, "dependencies": {"@aws-sdk/client-ses": "^3.844.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.1.6", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.11", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "bullmq": "^5.53.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "nestjs-pino": "^4.4.0", "nestjs-twilio": "^4.4.0", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-linkedin-oauth2": "^2.0.0", "passport-local": "^1.0.0", "pg": "^8.14.1", "pino": "^9.6.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "pug": "^3.0.3", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "twilio": "^5.7.0", "typeorm": "^0.3.22"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/express": "^5.0.0", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "postman-collection": "^5.0.2", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1", "^@common/(.*)$": "<rootDir>/common/$1", "^@modules/(.*)$": "<rootDir>/modules/$1", "^@entities/(.*)$": "<rootDir>/entities/$1", "^@entities$": "<rootDir>/entities/index"}}}