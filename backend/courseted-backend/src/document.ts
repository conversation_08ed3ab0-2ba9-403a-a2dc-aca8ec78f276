import { INestApplication } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

export const getDocument = (app: INestApplication) => {
  const configService = app.get(ConfigService);
  const env = configService.getOrThrow<string>("NODE_ENV");
  const PORT: number = Number(configService.getOrThrow<number>("PORT")) || 8000;
  const HOST = env === "development" ? "localhost" : "0.0.0.0";

  const swaggerConfig = new DocumentBuilder()
    .setTitle("Courseted API")
    .setDescription("Courseted API Description")
    .setVersion("1.0.0")
    .setExternalDoc("More Information", "")
    .addServer(`http://${HOST}:${PORT}`)
    .addBearerAuth()
    .build();

  return SwaggerModule.createDocument(app, swaggerConfig);
};
