import { NestFactory } from "@nestjs/core";
import { DataSource } from "typeorm";

import { AppModule } from "@/app.module";
import { seedData } from "@/database/seeds/seed-data";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const dataSource = app.get(DataSource);

  try {
    await seedData(dataSource);
    console.log("Seeding completed successfully");
  } catch (error) {
    console.error("Error during seeding:", error);
  } finally {
    await app.close();
  }
}

bootstrap();
