import { PostgresDataSource } from "@/config/data-source";

import { SeederRunner } from "../database/seeds";

async function runAdvancedSeed() {
  try {
    if (!PostgresDataSource.isInitialized) {
      await PostgresDataSource.initialize();
    }

    const seederRunner = new SeederRunner();
    const args = process.argv.slice(2);

    if (args.length === 0) {
      // Run all seeders
      await seederRunner.runAll(PostgresDataSource);
    } else if (args[0] === "--list") {
      // List available seeders
      console.log("Available seeders:");
      seederRunner.getAvailableSeederNames().forEach((name) => {
        console.log(`  - ${name}`);
      });
    } else if (args[0] === "--only") {
      // Run specific seeders
      const seederNames = args.slice(1);
      if (seederNames.length === 0) {
        console.error("Please specify seeder names after --only");
        process.exit(1);
      }
      await seederRunner.runSpecific(PostgresDataSource, seederNames);
    } else {
      console.error("Invalid arguments. Usage:");
      console.error(
        "  npm run seed:advanced                 # Run all seeders",
      );
      console.error(
        "  npm run seed:list                     # List available seeders",
      );
      console.error(
        "  npm run seed:only Countries Users     # Run specific seeders",
      );
      process.exit(1);
    }
  } catch (error) {
    console.error("Error during advanced seeding:", error);
    process.exit(1);
  } finally {
    if (PostgresDataSource.isInitialized) {
      await PostgresDataSource.destroy();
    }
  }
}

runAdvancedSeed();
