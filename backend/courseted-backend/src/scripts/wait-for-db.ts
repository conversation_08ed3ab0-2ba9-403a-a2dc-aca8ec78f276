import { Client } from "pg";

const checkConnection = () => {
  return new Promise((resolve, reject) => {
    const client = new Client({
      host: process?.env?.POSTGRES_HOST,
      port: Number(process?.env?.POSTGRES_PORT),
      user: process?.env?.POSTGRES_USER,
      password: process?.env?.POSTGRES_PASSWORD,
      database: process?.env?.POSTGRES_DB,
      connectionTimeoutMillis: 3000,
    });

    client.connect((err) => {
      if (err) {
        console.error(err);
        client.end();
        reject(err);
      } else {
        client.end();
        resolve(true);
      }
    });
  });
};

let i = 0;

const testConnection = async () => {
  try {
    await checkConnection();
    console.log("PostgreSQL connection established.");
    process.exit(0);
  } catch (e) {
    console.error(e);
    console.log(
      `PostgreSQL connection not available. Attempted ${++i} time${i !== 1 ? "s" : ""}.`,
    );
  }
};

(async () => {
  await testConnection();
  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  setInterval(testConnection, 3000);
  setTimeout(() => process.exit(1), 120000);
})();
