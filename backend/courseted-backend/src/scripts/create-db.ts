import { Client } from "pg";

(async (): Promise<void> => {
  try {
    const dbName: string = process?.env?.POSTGRES_DB || "";
    const client = new Client({
      host: process?.env?.POSTGRES_HOST,
      port: Number(process?.env?.POSTGRES_PORT),
      user: process?.env?.POSTGRES_USER,
      password: process?.env?.POSTGRES_PASSWORD,
      database: dbName,
      connectionTimeoutMillis: 3000,
    });

    await client.connect();

    const checkResult = await client.query(
      `SELECT 1 FROM pg_database WHERE datname = $1`,
      [dbName],
    );

    if (checkResult.rowCount === 0) {
      await client.query(`CREATE DATABASE "${dbName}"`);
      console.log(`Database "${dbName}" created`);
    } else {
      console.log(`Database "${dbName}" already exists`);
    }

    await client.end();
  } catch (error) {
    console.error("Error creating database:", error);
  }
})();
