import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { writeFile } from "fs/promises";
import { join } from "path";
import { Collection, Item, ItemGroup } from "postman-collection";
import { exit } from "process";

import { getApplicationInstance } from "@/app";
import { USERS_SEED } from "@/database/seeds/data/user.seed";
import { getDocument } from "@/document";

async function bootstrap() {
  console.log("Postman collection generation started....");
  const app = await getApplicationInstance();

  const configService = app.get(ConfigService);
  const httpService = app.get(HttpService);
  const PORT: number = Number(configService.getOrThrow("PORT")) || 8000;
  const Name = configService.getOrThrow("NAME") ?? "Courseted";
  const document = getDocument(app);
  const user = USERS_SEED?.[0];
  const defaultPassword = "12345678"; // Default seed password
  const { data } = await httpService.axiosRef.post(
    `http://localhost:${PORT}/v1/auth/login`,
    {
      email: user.email,
      password: defaultPassword,
    },
  );
  console.log(data.data.accessToken);

  const postmanCollection = new Collection({
    info: {
      name: Name,
      version: process.env?.VERSION || "1.0.0",
      description:
        "API collection generated from OpenAPI/Swagger documentation",
    },
    item: [],
    variable: [
      {
        key: "baseUrl",
        value: `http://localhost:${PORT}`,
        type: "string",
      },
    ],
    auth: {
      type: "bearer",
      bearer: [
        {
          key: "token",
          value: data.data.accessToken,
          type: "string",
        },
      ],
    },
  });

  // Group items by tags/folders
  const tagFolders = new Map();
  const allItems = [];

  const paths = Object.keys(document?.paths ?? {});
  let pathIndex = 0;
  let methodIndex = 0;

  while (pathIndex < paths.length) {
    const path = paths[pathIndex];
    console.log(`Processing path: ${path}...`);
    const methods = Object.keys(document.paths[path]);

    if (methodIndex < methods.length) {
      const method = methods[methodIndex];
      const route = document.paths[path][method];

      // Helper function to extract example values from schema
      const extractExampleFromSchema = (
        schema: any,
        visited = new Set(),
      ): any => {
        if (!schema) return null;

        // Prevent infinite recursion with circular references
        const schemaKey = JSON.stringify(schema);
        if (visited.has(schemaKey)) return null;
        visited.add(schemaKey);

        // If schema has a direct example, use it
        if (schema.example !== undefined) {
          return schema.example;
        }

        // If schema references another schema, resolve it
        if (schema.$ref) {
          const refPath = schema.$ref.replace("#/components/schemas/", "");
          const refSchema = document.components?.schemas?.[refPath];
          if (refSchema) {
            return extractExampleFromSchema(refSchema, visited);
          }
        }

        // Handle allOf, oneOf, anyOf schemas
        if (schema.allOf && Array.isArray(schema.allOf)) {
          const mergedExample = {};
          schema.allOf.forEach((subSchema: any) => {
            const subExample = extractExampleFromSchema(subSchema, visited);
            if (subExample && typeof subExample === "object") {
              Object.assign(mergedExample, subExample);
            }
          });
          return Object.keys(mergedExample).length > 0 ? mergedExample : null;
        }

        if (
          schema.oneOf &&
          Array.isArray(schema.oneOf) &&
          schema.oneOf.length > 0
        ) {
          return extractExampleFromSchema(schema.oneOf[0], visited);
        }

        if (
          schema.anyOf &&
          Array.isArray(schema.anyOf) &&
          schema.anyOf.length > 0
        ) {
          return extractExampleFromSchema(schema.anyOf[0], visited);
        }

        // Generate example based on schema properties
        if (schema.properties) {
          const example = {};
          Object.keys(schema.properties).forEach((prop) => {
            const propSchema = schema.properties[prop];

            if (propSchema.example !== undefined) {
              example[prop] = propSchema.example;
            } else if (propSchema.$ref) {
              // Handle nested references
              const nestedExample = extractExampleFromSchema(
                propSchema,
                visited,
              );
              if (nestedExample !== null) {
                example[prop] = nestedExample;
              }
            } else {
              // Generate example based on type
              example[prop] = generateExampleByType(propSchema, visited);
            }
          });
          return Object.keys(example).length > 0 ? example : null;
        }

        // Handle array schemas
        if (schema.type === "array" && schema.items) {
          const itemExample = extractExampleFromSchema(schema.items, visited);
          return itemExample !== null ? [itemExample] : [];
        }

        // Generate example based on primitive type
        return generateExampleByType(schema, visited);
      };

      // Helper function to generate examples based on schema type
      const generateExampleByType = (schema: any, visited = new Set()): any => {
        if (schema.example !== undefined) {
          return schema.example;
        }

        switch (schema.type) {
          case "string":
            if (schema.format === "email") return "<EMAIL>";
            if (schema.format === "password") return "password123";
            if (schema.format === "date") return "2024-01-01";
            if (schema.format === "date-time") return "2024-01-01T00:00:00Z";
            if (schema.format === "uri") return "https://example.com";
            if (schema.enum && schema.enum.length > 0) return schema.enum[0];
            return schema.default || "string";

          case "number":
          case "integer":
            if (schema.minimum !== undefined) return schema.minimum;
            if (schema.enum && schema.enum.length > 0) return schema.enum[0];
            return schema.default !== undefined ? schema.default : 0;

          case "boolean":
            return schema.default !== undefined ? schema.default : false;

          case "array":
            if (schema.items) {
              const itemExample = extractExampleFromSchema(
                schema.items,
                visited,
              );
              return itemExample !== null ? [itemExample] : [];
            }
            return [];

          case "object":
            if (schema.properties) {
              return extractExampleFromSchema(schema, visited);
            }
            return {};

          default:
            return null;
        }
      };

      // Build URL with query parameters
      let urlPath = path;
      const queryParams = [];

      // Extract query parameters from route parameters
      if (route.parameters) {
        route.parameters.forEach((param: any) => {
          if (param.in === "query") {
            const exampleValue =
              param.example || param.schema?.example || "value";
            queryParams.push({
              key: param.name,
              value: String(exampleValue),
              description: param.description || "",
            });
          } else if (param.in === "path") {
            // Replace path parameters with example values
            const exampleValue =
              param.example || param.schema?.example || "value";
            urlPath = urlPath.replace(`{${param.name}}`, String(exampleValue));
          }
        });
      }

      const requestObj = {
        method: method.toUpperCase(),
        header: [
          {
            key: "Authorization",
            value: `Bearer ${data.data.accessToken}`,
            description: "Token for authentication",
          },
          {
            key: "Content-Type",
            value: "application/json",
            description: "Content Type",
          },
        ],
        url: {
          raw: `{{baseUrl}}${urlPath}`,
          host: ["{{baseUrl}}"],
          path: urlPath.split("/").filter((segment) => segment),
          query: queryParams,
        },
        description: route.summary || route.description,
      };

      // Handle request body with better error handling
      if (
        route.requestBody &&
        route.requestBody.content &&
        route.requestBody.content["application/json"]
      ) {
        const schema = route.requestBody.content["application/json"].schema;
        const exampleBody = extractExampleFromSchema(schema);

        if (exampleBody && Object.keys(exampleBody).length > 0) {
          requestObj["body"] = {
            mode: "raw",
            raw: JSON.stringify(exampleBody, null, 2),
            options: {
              raw: {
                language: "json",
              },
            },
          };
        }
      }

      // Create a simple name for the request like 'GET /v1/users/profile'
      const requestName = `${method.toUpperCase()} ${path}`;

      const item = new Item({
        name: requestName,
        request: requestObj,
        event: [
          {
            listen: "test",
            script: {
              exec: [
                "pm.test('Status code is success', function () {",
                "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);",
                "});",
                "",
                "pm.test('Response time is less than 2000ms', function () {",
                "    pm.expect(pm.response.responseTime).to.be.below(2000);",
                "});",
              ],
            },
          },
        ],
      });

      // Organize items by tags
      const primaryTag =
        route.tags && route.tags.length > 0 ? route.tags[0] : "Uncategorized";

      if (!tagFolders.has(primaryTag)) {
        tagFolders.set(primaryTag, []);
      }
      tagFolders.get(primaryTag).push(item);

      allItems.push(item);
      methodIndex++;
    } else {
      methodIndex = 0;
      pathIndex++;
    }
  }

  // Create folders for each tag and add items
  tagFolders.forEach((items, tagName) => {
    const folder = new ItemGroup({
      name: tagName,
      item: items,
    });
    postmanCollection.items.add(folder);
  });

  await writeFile(
    join(__dirname, "../../docs/api/postman.json"),
    JSON.stringify(postmanCollection.toJSON(), null, 2),
  );
  console.log("Postman collection generated successfully!");
  await app.close();
  exit(1);
}

(async (): Promise<void> => {
  try {
    await bootstrap();
  } catch (error: unknown) {
    console.error(
      "Error generating Postman collection:",
      error instanceof Error ? error.message : "",
    );
    exit(1);
  }
})();
