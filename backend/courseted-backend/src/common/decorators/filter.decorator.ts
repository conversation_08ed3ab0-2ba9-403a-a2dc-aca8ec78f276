import { applyDecorators } from "@nestjs/common";
import { ApiQuery } from "@nestjs/swagger";

export interface FilterDecoratorOptions {
  searchDescription?: string;
  sortableFields?: string[];
  maxLimit?: number;
  defaultLimit?: number;
}

/**
 * Filter decorator that applies all common filter query parameters for Swagger documentation
 * @param options Configuration options for the filter decorator
 */
export function ApiFilter(options: FilterDecoratorOptions = {}) {
  const {
    searchDescription = "Search term for filtering results",
    sortableFields = ["createdAt", "updatedAt", "id"],
    maxLimit = 100,
    defaultLimit = 10,
  } = options;

  const sortableFieldsList = sortableFields.join(", ");

  return applyDecorators(
    ApiQuery({
      name: "search",
      required: false,
      description: searchDescription,
      example: "john",
    }),
    ApiQuery({
      name: "page",
      required: false,
      description: "Page number (1-based)",
      example: 1,
      type: Number,
      minimum: 1,
    }),
    ApiQuery({
      name: "limit",
      required: false,
      description: `Items per page (default: ${defaultLimit}, max: ${maxLimit})`,
      example: defaultLimit,
      type: Number,
      minimum: 1,
      maximum: maxLimit,
    }),
    ApiQuery({
      name: "size",
      required: false,
      description: `Alias for limit - items per page (default: ${defaultLimit}, max: ${maxLimit})`,
      example: defaultLimit,
      type: Number,
      minimum: 1,
      maximum: maxLimit,
    }),
    ApiQuery({
      name: "sortBy",
      required: false,
      description: `Sort by field (${sortableFieldsList})`,
      example: "createdAt",
    }),
    ApiQuery({
      name: "sortOrder",
      required: false,
      description: "Sort order",
      enum: ["asc", "desc"],
      example: "desc",
    }),
  );
}

/**
 * Predefined filter decorator for user-related endpoints
 */
export function ApiUserFilter() {
  return ApiFilter({
    searchDescription: "Search users by email or phone number",
    sortableFields: [
      "email",
      "phoneNumber",
      "createdAt",
      "updatedAt",
      "id",
      "role",
    ],
    maxLimit: 100,
    defaultLimit: 10,
  });
}

/**
 * Predefined filter decorator for instructor-related endpoints
 */
export function ApiInstructorFilter() {
  return ApiFilter({
    searchDescription: "Search instructors by email or phone number",
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  });
}

/**
 * Predefined filter decorator for student-related endpoints
 */
export function ApiStudentFilter() {
  return ApiFilter({
    searchDescription: "Search students by email or phone number",
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  });
}

/**
 * Basic pagination decorator without search functionality
 */
export function ApiPagination(
  options: { maxLimit?: number; defaultLimit?: number } = {},
) {
  const { maxLimit = 100, defaultLimit = 10 } = options;

  return applyDecorators(
    ApiQuery({
      name: "page",
      required: false,
      description: "Page number (1-based)",
      example: 1,
      type: Number,
      minimum: 1,
    }),
    ApiQuery({
      name: "limit",
      required: false,
      description: `Items per page (default: ${defaultLimit}, max: ${maxLimit})`,
      example: defaultLimit,
      type: Number,
      minimum: 1,
      maximum: maxLimit,
    }),
  );
}
