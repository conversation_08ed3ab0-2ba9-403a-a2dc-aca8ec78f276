import { applyDecorators } from "@nestjs/common";
import { Throttle } from "@nestjs/throttler";

/**
 * Custom throttle decorator for authentication endpoints
 * Applies stricter rate limiting for login/register endpoints
 */
export const AuthThrottle = () => {
  return applyDecorators(
    Throttle({ default: { limit: 5, ttl: 60000 } }), // 5 requests per minute
  );
};

/**
 * Custom throttle decorator for sensitive operations
 * Applies very strict rate limiting for password reset, OTP verification etc.
 */
export const SensitiveOperationThrottle = () => {
  return applyDecorators(
    Throttle({ default: { limit: 3, ttl: 60000 } }), // 3 requests per minute
  );
};

/**
 * Custom throttle decorator for general API endpoints
 * Applies moderate rate limiting for general API usage
 */
export const GeneralThrottle = () => {
  return applyDecorators(
    Throttle({ default: { limit: 100, ttl: 60000 } }), // 100 requests per minute
  );
};

/**
 * Custom throttle decorator for public endpoints
 * Applies lenient rate limiting for public endpoints
 */
export const PublicThrottle = () => {
  return applyDecorators(
    Throttle({ default: { limit: 200, ttl: 60000 } }), // 200 requests per minute
  );
};
