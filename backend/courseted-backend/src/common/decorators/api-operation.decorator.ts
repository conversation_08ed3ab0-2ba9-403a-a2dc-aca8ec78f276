import { applyDecorators } from "@nestjs/common";
import {
  ApiBody,
  ApiBodyOptions,
  ApiHeader,
  ApiHeaderOptions,
  ApiOperation,
  ApiOperationOptions,
  ApiQuery,
  ApiQueryOptions,
  ApiResponse,
  ApiResponseOptions,
  ApiTags,
} from "@nestjs/swagger";

import { FilterDecoratorOptions } from "./filter.decorator";

/**
 * Predefined filter configurations for common use cases
 */
export const FilterConfigs = {
  /** Filter configuration for user endpoints */
  users: {
    searchDescription: "Search users by email or phone number",
    sortableFields: [
      "email",
      "phoneNumber",
      "createdAt",
      "updatedAt",
      "id",
      "role",
    ],
    maxLimit: 100,
    defaultLimit: 10,
  } as FilterDecoratorOptions,

  /** Filter configuration for instructor endpoints */
  instructors: {
    searchDescription: "Search instructors by email or phone number",
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  } as FilterDecoratorOptions,

  /** Filter configuration for student endpoints */
  students: {
    searchDescription: "Search students by email or phone number",
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  } as FilterDecoratorOptions,

  /** Basic filter configuration with default fields */
  basic: {
    searchDescription: "Search term for filtering results",
    sortableFields: ["createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  } as FilterDecoratorOptions,
};

/**
 * Extended options for the Swagger decorator that includes additional API documentation features
 */
interface ApiSwaggerOptions extends ApiOperationOptions {
  /** Headers to include in the API documentation */
  headers?: ApiHeaderOptions[];
  /** Whether to include authentication header (default: true) */
  authentication?: boolean;
  /** Query parameters documentation */
  query?: ApiQueryOptions;
  /** Request body documentation */
  body?: ApiBodyOptions;
  /** Response documentation */
  response?: ApiResponseOptions;
  /** Tags for grouping endpoints (required) */
  tags: string[];
  /** API version */
  version?: string | string[];
  /** Filter options for automatic query parameter documentation */
  filter?: FilterDecoratorOptions | boolean;
}

/**
 * Comprehensive Swagger decorator that combines multiple API documentation decorators
 *
 * @param options - Configuration options for API documentation
 * @returns Combined decorators for complete API documentation
 *
 * @example
 * ```typescript
 * @Swagger({
 *   summary: 'Create a new user',
 *   description: 'Creates a new user account with the provided information',
 *   tags: ['Users'],
 *   body: { type: CreateUserDto },
 *   response: { status: 201, description: 'User created successfully' }
 * })
 *
 * // With basic filtering
 * @Swagger({
 *   summary: 'Get all users',
 *   description: 'Retrieve users with filtering, pagination, and sorting',
 *   tags: ['Users'],
 *   filter: true // Adds default filter parameters
 * })
 *
 * // With predefined user filter configuration
 * @Swagger({
 *   summary: 'Get all users',
 *   description: 'Retrieve users with user-specific filtering',
 *   tags: ['Users'],
 *   filter: FilterConfigs.users
 * })
 *
 * // With custom filter options
 * @Swagger({
 *   summary: 'Get all products',
 *   description: 'Retrieve products with custom filtering options',
 *   tags: ['Products'],
 *   filter: {
 *     searchDescription: 'Search products by name, description, or SKU',
 *     sortableFields: ['name', 'price', 'category', 'createdAt'],
 *     maxLimit: 200,
 *     defaultLimit: 25
 *   }
 * })
 * ```
 */
export function Swagger(options: ApiSwaggerOptions) {
  const opt: ApiSwaggerOptions = {
    authentication: true,
    version: "1",
    ...options,
  };

  // Validate that tags are provided
  if (!opt.tags || opt.tags.length === 0) {
    throw new Error(
      `API endpoint must have at least one tag. Please provide tags for proper API documentation and organization. Endpoint: ${opt.summary || opt.operationId || "Unknown endpoint"}`,
    );
  }

  // Initialize decorators array with the main operation decorator
  const decorators = [
    ApiOperation({
      summary: opt.summary || `${opt.operationId}`,
      description: opt.description || `${opt.operationId}`,
      operationId: opt.operationId,
    }),
  ];

  // Prepare headers array with any custom headers
  const headers = [...(opt.headers ?? [])];

  // Add authentication header if required
  if (opt.authentication) {
    headers.push({
      name: "Authorization",
      description: "Bearer JWT access token",
      required: true,
    });
  }

  // Apply header decorators
  if (headers.length > 0) {
    headers.forEach((header) => {
      decorators.push(ApiHeader(header));
    });
  }

  // Add query parameters documentation
  if (opt.query) {
    decorators.push(ApiQuery(opt.query));
  }

  // Add filter query parameters if specified
  if (opt.filter) {
    const filterOptions = typeof opt.filter === "boolean" ? {} : opt.filter;
    const {
      searchDescription = "Search term for filtering results",
      sortableFields = ["createdAt", "updatedAt", "id"],
      maxLimit = 100,
      defaultLimit = 10,
    } = filterOptions;

    const sortableFieldsList = sortableFields.join(", ");

    // Add filter-related query parameters
    decorators.push(
      ApiQuery({
        name: "search",
        required: false,
        description: searchDescription,
        example: "john",
      }),
      ApiQuery({
        name: "page",
        required: false,
        description: "Page number (1-based)",
        example: 1,
        type: Number,
        minimum: 1,
      }),
      ApiQuery({
        name: "limit",
        required: false,
        description: `Items per page (default: ${defaultLimit}, max: ${maxLimit})`,
        example: defaultLimit,
        type: Number,
        minimum: 1,
        maximum: maxLimit,
      }),
      ApiQuery({
        name: "size",
        required: false,
        description: `Alias for limit - items per page (default: ${defaultLimit}, max: ${maxLimit})`,
        example: defaultLimit,
        type: Number,
        minimum: 1,
        maximum: maxLimit,
      }),
      ApiQuery({
        name: "sortBy",
        required: false,
        description: `Sort by field (${sortableFieldsList})`,
        example: "createdAt",
      }),
      ApiQuery({
        name: "sortOrder",
        required: false,
        description: "Sort order",
        enum: ["asc", "desc"],
        example: "desc",
      }),
    );
  }

  // Add request body documentation
  if (opt.body) {
    decorators.push(ApiBody(opt.body));
  }

  // Add response documentation
  if (opt.response) {
    decorators.push(ApiResponse(opt.response));
  }

  // Add tags for endpoint grouping (required)
  if (opt.tags && opt.tags.length > 0) {
    decorators.push(ApiTags(...opt.tags));
  }

  return applyDecorators(...decorators);
}
