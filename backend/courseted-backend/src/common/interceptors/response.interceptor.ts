import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from "@nestjs/common";
import { ApiProperty } from "@nestjs/swagger";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

import { IApiResponse, IJsObject } from "../interfaces/types";

export class ApiResponse<T> implements IApiResponse<T> {
  @ApiProperty({ description: "Whether the request was successful" })
  success: boolean;

  @ApiProperty({
    description: "Metadata related to the response",
    required: false,
  })
  metadata?: Record<string, unknown>;

  @ApiProperty({
    description: "The actual data of the response",
    required: false,
  })
  data?: T;

  @ApiProperty({ description: "The status of the response" })
  status: string;

  @ApiProperty({ description: "Message describing the response" })
  message: string;

  @ApiProperty({ description: "List of errors if any", required: false })
  errors?: string[];

  constructor(partial: IJsObject) {
    this.success = true; // Default to success for successful responses

    // If the partial already has data and metadata properties (like from filter service),
    // use them directly instead of nesting
    if (partial?.data && partial?.metadata) {
      this.data = partial.data as T;
      this.metadata = partial.metadata;
    } else {
      if (partial?.metadata) {
        this.metadata = partial.metadata;
      }
      if (partial) {
        this.data = partial as T;
      }
    }
  }
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, unknown> {
  intercept(
    context: ExecutionContext,
    next: CallHandler<T>,
  ): Observable<unknown> {
    return next.handle().pipe(map((data) => new ApiResponse(data)));
  }
}
