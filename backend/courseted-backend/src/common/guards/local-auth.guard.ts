import { LoginDto } from "@modules/auth/dtos/login.dto";
import {
  BadRequestException,
  ExecutionContext,
  Injectable,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { plainToClass } from "class-transformer";
import { validate } from "class-validator";

@Injectable()
export class LocalAuthGuard extends AuthGuard("local") {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const loginDto = plainToClass(LoginDto, request.body);
    const errors = await validate(loginDto);
    if (errors.length > 0) {
      const messages = errors
        .map((error) => Object.values(error.constraints || {}).join(", "))
        .join("; ");
      throw new BadRequestException(messages);
    }
    return super.canActivate(context) as Promise<boolean>;
  }
}
