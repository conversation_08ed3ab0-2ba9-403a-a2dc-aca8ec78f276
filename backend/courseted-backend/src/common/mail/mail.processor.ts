import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { MailerService as NestMailerService } from "@nestjs-modules/mailer";
import { Job } from "bullmq";

import { MailBuilder } from "./mailables/mail-builder";
import { Mailable } from "./mailables/mailable.abstract";
import { getMailableClass } from "./mailables/mailable.registry";

interface MailJob {
  recipient: string | string[];
  mailable: {
    name: string;
    payload: any;
  };
  options?: {
    delay?: number;
    attempts?: number;
    priority?: number;
  };
}

@Processor("mail")
export class MailProcessor extends WorkerHost {
  private readonly logger = new Logger(MailProcessor.name);

  constructor(
    private readonly mailerService: NestMailerService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  async process(job: Job<MailJob>): Promise<void> {
    const { recipient, mailable: mailableInfo } = job.data;
    const recipientStr = Array.isArray(recipient)
      ? recipient.join(", ")
      : recipient;
    this.logger.log(`Processing mail job ${job.id} for ${recipientStr}`);

    try {
      const MailableClass = getMailableClass(mailableInfo.name);
      if (!MailableClass) {
        throw new Error(
          `Mailable class '${mailableInfo.name}' not found in registry.`,
        );
      }

      const mailableInstance: Mailable = new MailableClass(
        mailableInfo.payload,
      );

      // Check if mailable should be sent
      if (mailableInstance.shouldSend) {
        const shouldSend = await Promise.resolve(mailableInstance.shouldSend());
        if (!shouldSend) {
          this.logger.log(
            `Skipping mail job ${job.id} - shouldSend returned false`,
          );
          return;
        }
      }

      // Call prepare method if it exists
      if (mailableInstance.prepare) {
        await Promise.resolve(mailableInstance.prepare());
      }

      // Build and send the email
      const mailBuilder = new MailBuilder().to(recipient);
      const emailOptions = mailableInstance.configure(mailBuilder).build();

      // Add default from if not set
      if (!emailOptions.from) {
        const mailConfig = this.configService.get("mail");
        emailOptions.from = `"${mailConfig.MAIL_FROM_NAME}" <${mailConfig.MAIL_FROM_ADDRESS}>`;
      }

      await this.mailerService.sendMail(emailOptions);
      this.logger.log(
        `Mail job ${job.id} completed successfully for ${recipientStr}`,
      );
    } catch (error) {
      this.logger.error(
        `Mail job ${job.id} failed for ${recipientStr}: ${error.message}`,
        error.stack,
      );
      throw error; // Re-throw to trigger retry mechanism
    }
  }
}
