import { Body, Controller, Post } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { PasswordResetMailable, WelcomeUserMailable } from "../../mailables";
import { MailService } from "./mail.service";

interface SendWelcomeEmailDto {
  email: string;
  name?: string;
}

interface SendPasswordResetDto {
  email: string;
  name?: string;
  token: string;
}

@ApiTags("Mail")
@Controller("mail")
export class MailController {
  constructor(private readonly mailService: MailService) {}

  @Post("welcome")
  @ApiOperation({ summary: "Send welcome email (queued)" })
  @ApiResponse({
    status: 201,
    description: "Welcome email queued successfully",
  })
  async sendWelcomeEmail(@Body() dto: SendWelcomeEmailDto) {
    const user = { id: Date.now(), ...dto };

    await this.mailService
      .to(dto.email)
      .queue(new WelcomeUserMailable({ user }));

    return { message: "Welcome email queued successfully" };
  }

  @Post("password-reset")
  @ApiOperation({ summary: "Send password reset email (immediate)" })
  @ApiResponse({
    status: 201,
    description: "Password reset email sent successfully",
  })
  async sendPasswordResetEmail(@Body() dto: SendPasswordResetDto) {
    const user = { id: Date.now(), email: dto.email, name: dto.name };
    const expiresAt = new Date(Date.now() + 3600000); // 1 hour from now

    await this.mailService
      .to(dto.email)
      .send(new PasswordResetMailable({ user, token: dto.token, expiresAt }));

    return { message: "Password reset email sent successfully" };
  }

  @Post("broadcast")
  @ApiOperation({ summary: "Send broadcast email to multiple recipients" })
  @ApiResponse({
    status: 201,
    description: "Broadcast email queued successfully",
  })
  async sendBroadcastEmail(@Body() dto: { emails: string[]; message: string }) {
    const user = {
      id: 0,
      email: "<EMAIL>",
      name: "Courseted Team",
    };

    // Send to multiple recipients with CC to admin
    await this.mailService
      .to(dto.emails)
      .cc("<EMAIL>")
      .queue(new WelcomeUserMailable({ user }));

    return {
      message: `Broadcast email queued for ${dto.emails.length} recipients`,
      recipients: dto.emails.length,
    };
  }

  @Post("queue-stats")
  @ApiOperation({ summary: "Get mail queue statistics" })
  @ApiResponse({
    status: 200,
    description: "Queue statistics retrieved successfully",
  })
  async getQueueStats() {
    const stats = await this.mailService.getQueueStats();
    return {
      message: "Queue statistics retrieved successfully",
      stats,
    };
  }
}
