import { ISendMailOptions } from "@nestjs-modules/mailer";

export class MailBuilder {
  private options: ISendMailOptions & { ses?: Record<string, any> } = {};
  private attachmentsArray: any[] = [];

  to(address: string | string[]): this {
    this.options.to = address;
    return this;
  }

  cc(address: string | string[]): this {
    this.options.cc = address;
    return this;
  }

  bcc(address: string | string[]): this {
    this.options.bcc = address;
    return this;
  }

  from(address: string): this {
    this.options.from = address;
    return this;
  }

  subject(subject: string): this {
    this.options.subject = subject;
    return this;
  }

  template(template: string): this {
    this.options.template = template;
    return this;
  }

  context(context: Record<string, any>): this {
    this.options.context = context;
    return this;
  }

  html(html: string): this {
    this.options.html = html;
    return this;
  }

  text(text: string): this {
    this.options.text = text;
    return this;
  }

  attach(attachment: any): this {
    this.attachmentsArray.push(attachment);
    return this;
  }

  ses(options: Record<string, any>): this {
    this.options.ses = options;
    return this;
  }

  priority(priority: "high" | "normal" | "low"): this {
    this.options.priority = priority;
    return this;
  }

  build(): ISendMailOptions {
    if (this.attachmentsArray.length > 0) {
      this.options.attachments = this.attachmentsArray;
    }
    return this.options;
  }
}
