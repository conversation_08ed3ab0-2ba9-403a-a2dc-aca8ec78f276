import { Mailable } from "./mailable.abstract";

export const mailableRegistry = new Map<
  string,
  new (...args: any[]) => Mailable
>();

export function RegisterMailable() {
  return function (constructor: new (...args: any[]) => Mailable) {
    mailableRegistry.set(constructor.name, constructor);
  };
}

export function getMailableClass(
  name: string,
): new (...args: any[]) => Mailable | undefined {
  return mailableRegistry.get(name);
}

export function getRegisteredMailables(): string[] {
  return Array.from(mailableRegistry.keys());
}
