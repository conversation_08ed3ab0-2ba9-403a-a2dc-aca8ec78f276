import { MailBuilder } from "./mail-builder";

export abstract class Mailable<T = any> {
  public readonly payload: T;

  constructor(payload: T) {
    this.payload = payload;
  }

  // This property will be used by the processor to identify the class
  public get constructorName(): string {
    return this.constructor.name;
  }

  // Abstract method that must be implemented by concrete mailable classes
  public abstract configure(builder: MailBuilder): MailBuilder;

  // Optional method for handling custom logic before sending
  public prepare?(): void | Promise<void>;

  // Optional method to determine if the mailable should be sent
  public shouldSend?(): boolean | Promise<boolean>;
}
