import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { MailerService as NestMailerService } from "@nestjs-modules/mailer";
import { Queue } from "bullmq";

import { MailBuilder } from "./mailables/mail-builder";
import { Mailable } from "./mailables/mailable.abstract";

interface QueueOptions {
  delay?: number;
  attempts?: number;
  priority?: number;
  backoff?: {
    type: "exponential" | "fixed";
    delay: number;
  };
}

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);
  private recipient: string | string[];
  private ccRecipients?: string | string[];
  private bccRecipients?: string | string[];

  constructor(
    private readonly mailerService: NestMailerService,
    @InjectQueue("mail")
    private readonly mailQueue: Queue,
    private readonly configService: ConfigService,
  ) {}

  to(recipient: string | string[]): this {
    this.recipient = recipient;
    return this;
  }

  cc(recipients: string | string[]): this {
    this.ccRecipients = recipients;
    return this;
  }

  bcc(recipients: string | string[]): this {
    this.bccRecipients = recipients;
    return this;
  }

  async send(mailable: Mailable<any>): Promise<void> {
    if (!this.recipient) {
      throw new Error("Recipient must be set using the .to() method.");
    }

    try {
      // Check if mailable should be sent
      if (mailable.shouldSend) {
        const shouldSend = await Promise.resolve(mailable.shouldSend());
        if (!shouldSend) {
          this.logger.log("Skipping email - shouldSend returned false");
          this.resetRecipients();
          return;
        }
      }

      // Call prepare method if it exists
      if (mailable.prepare) {
        await Promise.resolve(mailable.prepare());
      }

      const mailBuilder = new MailBuilder().to(this.recipient);

      // Add CC and BCC if set
      if (this.ccRecipients) {
        mailBuilder.cc(this.ccRecipients);
      }
      if (this.bccRecipients) {
        mailBuilder.bcc(this.bccRecipients);
      }

      const emailOptions = mailable.configure(mailBuilder).build();

      // Add default from if not set
      if (!emailOptions.from) {
        const mailConfig = this.configService.get("mail");
        emailOptions.from = `"${mailConfig.MAIL_FROM_NAME}" <${mailConfig.MAIL_FROM_ADDRESS}>`;
      }

      await this.mailerService.sendMail(emailOptions);

      const recipientStr = Array.isArray(this.recipient)
        ? this.recipient.join(", ")
        : this.recipient;
      this.logger.log(`Email sent successfully to ${recipientStr}`);
    } catch (error) {
      const recipientStr = Array.isArray(this.recipient)
        ? this.recipient.join(", ")
        : this.recipient;
      this.logger.error(`Failed to send email to ${recipientStr}`, error.stack);
      throw error;
    } finally {
      this.resetRecipients();
    }
  }

  async queue(mailable: Mailable<any>, options?: QueueOptions): Promise<void> {
    if (!this.recipient) {
      throw new Error("Recipient must be set using the .to() method.");
    }

    const job = {
      recipient: this.recipient,
      ccRecipients: this.ccRecipients,
      bccRecipients: this.bccRecipients,
      mailable: {
        name: mailable.constructorName,
        payload: mailable.payload,
      },
    };

    const defaultOptions: QueueOptions = {
      attempts: 3,
      backoff: {
        type: "exponential",
        delay: 1000,
      },
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
      await this.mailQueue.add("send-mail-job", job, finalOptions);

      const recipientStr = Array.isArray(this.recipient)
        ? this.recipient.join(", ")
        : this.recipient;
      this.logger.log(`Email job queued for ${recipientStr}`);
    } catch (error) {
      const recipientStr = Array.isArray(this.recipient)
        ? this.recipient.join(", ")
        : this.recipient;
      this.logger.error(
        `Failed to queue email for ${recipientStr}`,
        error.stack,
      );
      throw error;
    } finally {
      this.resetRecipients();
    }
  }

  async later(mailable: Mailable<any>, delay: number): Promise<void> {
    return this.queue(mailable, { delay });
  }

  private resetRecipients(): void {
    this.recipient = undefined;
    this.ccRecipients = undefined;
    this.bccRecipients = undefined;
  }

  // Utility method to get queue statistics
  async getQueueStats() {
    const waiting = await this.mailQueue.getWaiting();
    const active = await this.mailQueue.getActive();
    const completed = await this.mailQueue.getCompleted();
    const failed = await this.mailQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  // Utility method to clear all jobs
  async clearQueue() {
    await this.mailQueue.obliterate();
    this.logger.log("Mail queue cleared");
  }
}
