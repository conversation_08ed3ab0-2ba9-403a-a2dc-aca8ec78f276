import { Injectable } from "@nestjs/common";

import { Mailable } from "./mailables/mailable.abstract";

interface SentMail {
  recipient: string | string[];
  ccRecipients?: string | string[];
  bccRecipients?: string | string[];
  mailable: Mailable<any>;
}

@Injectable()
export class MockMailService {
  public sentMail: SentMail[] = [];
  public queuedMail: SentMail[] = [];

  private recipient: string | string[];
  private ccRecipients?: string | string[];
  private bccRecipients?: string | string[];

  to(recipient: string | string[]): this {
    this.recipient = recipient;
    return this;
  }

  cc(recipients: string | string[]): this {
    this.ccRecipients = recipients;
    return this;
  }

  bcc(recipients: string | string[]): this {
    this.bccRecipients = recipients;
    return this;
  }

  send(mailable: Mailable<any>): Promise<void> {
    if (!this.recipient) {
      throw new Error("Recipient must be set using the .to() method.");
    }

    // Check shouldSend method like the real service
    if (mailable.shouldSend && !mailable.shouldSend()) {
      this.resetRecipients();
      return Promise.resolve();
    }

    this.sentMail.push({
      recipient: this.recipient,
      ccRecipients: this.ccRecipients,
      bccRecipients: this.bccRecipients,
      mailable,
    });

    this.resetRecipients();
    return Promise.resolve();
  }

  queue(mailable: Mailable<any>): Promise<void> {
    if (!this.recipient) {
      throw new Error("Recipient must be set using the .to() method.");
    }

    // Check shouldSend method like the real service
    if (mailable.shouldSend && !mailable.shouldSend()) {
      this.resetRecipients();
      return Promise.resolve();
    }

    this.queuedMail.push({
      recipient: this.recipient,
      ccRecipients: this.ccRecipients,
      bccRecipients: this.bccRecipients,
      mailable,
    });

    this.resetRecipients();
    return Promise.resolve();
  }

  later(mailable: Mailable<any>, _delay: number): Promise<void> {
    return this.queue(mailable);
  }

  // Assertion methods for testing
  assertSent(
    MailableClass: new (...args: any[]) => Mailable<any>,
    count = 1,
  ): void {
    const matching = this.sentMail.filter(
      (item) => item.mailable instanceof MailableClass,
    );
    if (matching.length !== count) {
      throw new Error(
        `Expected ${count} email(s) of type ${MailableClass.name} to be sent, but found ${matching.length}.`,
      );
    }
  }

  assertQueued(
    MailableClass: new (...args: any[]) => Mailable<any>,
    count = 1,
  ): void {
    const matching = this.queuedMail.filter(
      (item) => item.mailable instanceof MailableClass,
    );
    if (matching.length !== count) {
      throw new Error(
        `Expected ${count} email(s) of type ${MailableClass.name} to be queued, but found ${matching.length}.`,
      );
    }
  }

  assertSentTo(
    MailableClass: new (...args: any[]) => Mailable<any>,
    recipient: string,
  ): void {
    const matching = this.sentMail.filter(
      (item) =>
        item.mailable instanceof MailableClass &&
        (item.recipient === recipient ||
          (Array.isArray(item.recipient) &&
            item.recipient.includes(recipient))),
    );
    if (matching.length === 0) {
      throw new Error(
        `Expected email of type ${MailableClass.name} to be sent to ${recipient}, but none found.`,
      );
    }
  }

  assertQueuedTo(
    MailableClass: new (...args: any[]) => Mailable<any>,
    recipient: string,
  ): void {
    const matching = this.queuedMail.filter(
      (item) =>
        item.mailable instanceof MailableClass &&
        (item.recipient === recipient ||
          (Array.isArray(item.recipient) &&
            item.recipient.includes(recipient))),
    );
    if (matching.length === 0) {
      throw new Error(
        `Expected email of type ${MailableClass.name} to be queued to ${recipient}, but none found.`,
      );
    }
  }

  assertNothingSent(): void {
    if (this.sentMail.length > 0) {
      throw new Error(
        `Expected no emails to be sent, but found ${this.sentMail.length}.`,
      );
    }
  }

  assertNothingQueued(): void {
    if (this.queuedMail.length > 0) {
      throw new Error(
        `Expected no emails to be queued, but found ${this.queuedMail.length}.`,
      );
    }
  }

  // Get methods for detailed inspection
  getSentMail(): SentMail[] {
    return [...this.sentMail];
  }

  getQueuedMail(): SentMail[] {
    return [...this.queuedMail];
  }

  getSentMailOfType(
    MailableClass: new (...args: any[]) => Mailable<any>,
  ): SentMail[] {
    return this.sentMail.filter(
      (item) => item.mailable instanceof MailableClass,
    );
  }

  getQueuedMailOfType(
    MailableClass: new (...args: any[]) => Mailable<any>,
  ): SentMail[] {
    return this.queuedMail.filter(
      (item) => item.mailable instanceof MailableClass,
    );
  }

  // Clear methods
  clearSent(): void {
    this.sentMail = [];
  }

  clearQueued(): void {
    this.queuedMail = [];
  }

  clearAll(): void {
    this.sentMail = [];
    this.queuedMail = [];
  }

  private resetRecipients(): void {
    this.recipient = undefined;
    this.ccRecipients = undefined;
    this.bccRecipients = undefined;
  }

  // Mock implementations of utility methods
  getQueueStats() {
    return Promise.resolve({
      waiting: this.queuedMail.length,
      active: 0,
      completed: 0,
      failed: 0,
    });
  }

  clearQueue() {
    this.clearQueued();
    return Promise.resolve();
  }
}
