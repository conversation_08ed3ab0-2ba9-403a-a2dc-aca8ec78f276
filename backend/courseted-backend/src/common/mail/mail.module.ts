// Import mailable classes to register them
import "../../mailables/password-reset.mailable";
import "../../mailables/welcome-user.mailable";

import { SES } from "@aws-sdk/client-ses";
import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MailerModule } from "@nestjs-modules/mailer";
import { PugAdapter } from "@nestjs-modules/mailer/dist/adapters/pug.adapter";
import * as path from "path";

import { MailConfig } from "../../config/mail.config";
import { MailController } from "./mail.controller";
import { MailProcessor } from "./mail.processor";
import { MailService } from "./mail.service";

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const mailConfig: MailConfig = configService.get("mail");
        let transport;

        switch (mailConfig.MAIL_DRIVER) {
          case "ses": {
            const ses = new SES({
              region: mailConfig.AWS_REGION,
              credentials: {
                accessKeyId: mailConfig.AWS_ACCESS_KEY_ID,
                secretAccessKey: mailConfig.AWS_SECRET_ACCESS_KEY,
              },
            });
            transport = {
              SES: { ses, aws: SES },
            };
            break;
          }

          case "smtp": {
            transport = {
              host: mailConfig.MAIL_HOST,
              port: mailConfig.MAIL_PORT,
              secure: mailConfig.MAIL_PORT === 465, // true for 465, false for other ports
              auth: {
                user: mailConfig.MAIL_USER,
                pass: mailConfig.MAIL_PASS,
              },
            };
            break;
          }

          case "log":
          default: {
            // Use console transport for development/testing
            transport = {
              streamTransport: true,
              newline: "unix",
              buffer: true,
            };
            break;
          }
        }

        // Template directory resolution for both dev and prod
        const getTemplateDir = () => {
          // In development: from src/common/mail to src/mailables/templates = ../../mailables/templates
          // In production: from dist/src/common/mail to dist/src/mailables/templates = ../../mailables/templates
          // Check if we're in compiled environment by looking for source files
          const isDev = __filename.endsWith(".ts");
          return isDev
            ? path.join(__dirname, "../../mailables/templates")
            : path.join(__dirname, "../../mailables/templates");
        };

        return {
          transport,
          defaults: {
            from: `"${mailConfig.MAIL_FROM_NAME}" <${mailConfig.MAIL_FROM_ADDRESS}>`,
          },
          template: {
            dir: getTemplateDir(),
            adapter: new PugAdapter(),
            options: {
              strict: true,
            },
          },
          // For development, preview emails in browser instead of sending when using non-production drivers
          preview:
            mailConfig.NODE_ENV === "development" &&
            mailConfig.MAIL_DRIVER === "log",
        };
      },
    }),
    BullModule.registerQueueAsync({
      name: "mail",
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const mailConfig: MailConfig = configService.get("mail");
        return {
          connection: {
            host: mailConfig.REDIS_HOST,
            port: mailConfig.REDIS_PORT,
          },
        };
      },
    }),
  ],
  controllers: [MailController],
  providers: [MailService, MailProcessor],
  exports: [MailService],
})
export class MailModule {}
