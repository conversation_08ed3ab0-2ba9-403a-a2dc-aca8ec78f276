import { ApiProperty } from "@nestjs/swagger";
import { CreateDateColumn, UpdateDateColumn } from "typeorm";

export class BaseEntity {
  @ApiProperty({
    description: "The date when the profile was created",
    example: "2023-01-01T00:00:00Z",
  })
  @CreateDateColumn()
  createdAt?: Date;

  @ApiProperty({
    description: "The date when the profile was last updated",
    example: "2023-01-02T00:00:00Z",
  })
  @UpdateDateColumn()
  updatedAt?: Date;
}
