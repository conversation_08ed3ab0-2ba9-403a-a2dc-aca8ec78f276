import { Injectable } from "@nestjs/common";
import { Repository, SelectQueryBuilder } from "typeorm";

import { IMetadata } from "../../interfaces/types";
import { BaseFilterDto, SortOrder } from "../dtos/filter.dto";
import {
  IFilterOptions,
  IFilterResult,
  IFilterService,
} from "../interfaces/filter.interface";

@Injectable()
export class FilterService implements IFilterService {
  /**
   * Apply filters, search, pagination, and sorting to a TypeORM query
   */
  async applyFilters<T>(
    query: SelectQueryBuilder<T> | Repository<T>,
    filters: BaseFilterDto,
    options: IFilterOptions = {},
  ): Promise<IFilterResult<T>> {
    const {
      searchFields = [],
      defaultSortField = "createdAt",
      allowedSortFields = [],
      maxLimit = 100,
    } = options;

    let queryBuilder: SelectQueryBuilder<T>;

    // Handle both QueryBuilder and Repository
    if (query instanceof Repository) {
      queryBuilder = query.createQueryBuilder();
    } else {
      queryBuilder = query;
    }

    // Apply search conditions
    if (filters.search && searchFields.length > 0) {
      const searchCondition = this.buildSearchCondition(
        filters.search,
        searchFields,
      );
      queryBuilder.andWhere(
        searchCondition.condition,
        searchCondition.parameters,
      );
    }

    // Apply sorting
    const sortCondition = this.buildSortCondition(
      filters.sortBy,
      filters.sortOrder,
      allowedSortFields,
      defaultSortField,
    );

    if (sortCondition.field && sortCondition.order) {
      queryBuilder.orderBy(sortCondition.field, sortCondition.order);
    }

    // Get total count before applying pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const limit = Math.min(filters.pageSize, maxLimit);
    queryBuilder.skip(filters.offset).take(limit);

    // Execute query
    const data = await queryBuilder.getMany();

    // Create metadata
    const metadata = this.createMetadata(total, filters.page || 1, limit);

    return {
      data,
      metadata,
    };
  }

  /**
   * Build search condition for multiple fields
   */
  buildSearchCondition(
    searchTerm: string,
    searchFields: string[],
  ): { condition: string; parameters: Record<string, any> } {
    if (!searchTerm || searchFields.length === 0) {
      return { condition: "1=1", parameters: {} };
    }

    const conditions = searchFields.map(
      (field, index) => `${field} ILIKE :search${index}`,
    );
    const parameters: Record<string, any> = {};

    searchFields.forEach((_, index) => {
      parameters[`search${index}`] = `%${searchTerm}%`;
    });

    return {
      condition: `(${conditions.join(" OR ")})`,
      parameters,
    };
  }

  /**
   * Build sort condition with validation
   */
  buildSortCondition(
    sortBy?: string,
    sortOrder?: string,
    allowedFields: string[] = [],
    defaultField = "createdAt",
  ): { field: string; order: "ASC" | "DESC" } {
    // Validate sort field
    let field = defaultField;
    if (sortBy) {
      if (allowedFields.length === 0 || allowedFields.includes(sortBy)) {
        field = sortBy;
      }
    }

    // Validate sort order
    const order =
      sortOrder && Object.values(SortOrder).includes(sortOrder as SortOrder)
        ? (sortOrder.toUpperCase() as "ASC" | "DESC")
        : "DESC";

    return { field, order };
  }

  /**
   * Create metadata object for pagination
   */
  createMetadata(
    total: number,
    page: number,
    limit: number,
    path?: string,
  ): IMetadata {
    const pages = Math.ceil(total / limit);

    return {
      timestamp: new Date().toISOString(),
      path,
      pages,
      perPage: limit,
      total,
      page,
      hasNextPage: page < pages,
      hasPrevPage: page > 1,
      nextPage: page < pages ? page + 1 : null,
      prevPage: page > 1 ? page - 1 : null,
    };
  }

  /**
   * Apply filters to a simple array (for in-memory filtering)
   */
  filterArray<T>(
    data: T[],
    filters: BaseFilterDto,
    searchCallback?: (item: T, searchTerm: string) => boolean,
    sortCallback?: (a: T, b: T) => number,
  ): IFilterResult<T> {
    let filteredData = [...data];

    // Apply search filter
    if (filters.search && searchCallback) {
      filteredData = filteredData.filter((item) =>
        searchCallback(item, filters.search),
      );
    }

    // Apply sorting
    if (sortCallback) {
      filteredData.sort(sortCallback);
    }

    // Apply pagination
    const total = filteredData.length;
    const startIndex = filters.offset;
    const endIndex = startIndex + filters.pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    // Create metadata
    const metadata = this.createMetadata(
      total,
      filters.page || 1,
      filters.pageSize,
    );

    return {
      data: paginatedData,
      metadata,
    };
  }

  /**
   * Create pagination-only result (when no filtering is needed)
   */
  async paginateQuery<T>(
    query: SelectQueryBuilder<T> | Repository<T>,
    page = 1,
    limit = 10,
  ): Promise<IFilterResult<T>> {
    const filters = new BaseFilterDto();
    filters.page = page;
    filters.limit = limit;

    return this.applyFilters(query, filters);
  }
}
