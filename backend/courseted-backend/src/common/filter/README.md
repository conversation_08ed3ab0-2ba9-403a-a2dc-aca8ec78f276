# Filter Service

A comprehensive filtering, pagination, and search service for NestJS applications using TypeORM.

## Features

- **Search**: Full-text search across multiple fields
- **Pagination**: Page-based pagination with metadata
- **Sorting**: Configurable sorting with validation
- **Flexible Limits**: Support for both `limit` and `size` parameters
- **Metadata**: Rich pagination metadata including navigation info
- **TypeORM Integration**: Direct integration with TypeORM QueryBuilder and Repository
- **Array Filtering**: In-memory filtering for arrays

## Installation

Import the `FilterModule` in your module:

```typescript
import { Module } from '@nestjs/common';
import { FilterModule } from '../common/filter';

@Module({
  imports: [FilterModule],
  // ...
})
export class YourModule {}
```

## Usage

### Basic Usage with Repository

```typescript
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FilterService, BaseFilterDto } from '../common/filter';
import { User } from './user.entity';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private filterService: FilterService,
  ) {}

  async findAllUsers(filters: BaseFilterDto) {
    return this.filterService.applyFilters(
      this.userRepository,
      filters,
      {
        searchFields: ['name', 'email'],
        defaultSortField: 'createdAt',
        allowedSortFields: ['name', 'email', 'createdAt'],
        maxLimit: 50,
      }
    );
  }
}
```

### Usage with QueryBuilder

```typescript
async findUsersWithRelations(filters: BaseFilterDto) {
  const queryBuilder = this.userRepository
    .createQueryBuilder('user')
    .leftJoinAndSelect('user.profile', 'profile')
    .where('user.isActive = :isActive', { isActive: true });

  return this.filterService.applyFilters(
    queryBuilder,
    filters,
    {
      searchFields: ['user.name', 'user.email', 'profile.bio'],
      defaultSortField: 'user.createdAt',
      allowedSortFields: ['user.name', 'user.email', 'user.createdAt'],
    }
  );
}
```

### Controller Implementation

```typescript
import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { BaseFilterDto } from '../common/filter';
import { UserService } from './user.service';

@ApiTags('users')
@Controller('users')
export class UserController {
  constructor(private userService: UserService) {}

  @Get()
  @ApiOperation({ summary: 'Get all users with filtering' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order' })
  async findAll(@Query() filters: BaseFilterDto) {
    return this.userService.findAllUsers(filters);
  }
}
```

### Array Filtering

```typescript
// For in-memory filtering of arrays
const users = [/* your array data */];
const result = this.filterService.filterArray(
  users,
  filters,
  (user, searchTerm) => user.name.toLowerCase().includes(searchTerm.toLowerCase()),
  (a, b) => a.name.localeCompare(b.name)
);
```

## API Parameters

### BaseFilterDto Parameters

| Parameter | Type | Description | Default | Example |
|-----------|------|-------------|---------|---------|
| `search` | string | Search term for filtering | - | "john" |
| `page` | number | Page number (1-based) | 1 | 2 |
| `limit` | number | Items per page | 10 | 20 |
| `size` | number | Alias for limit | - | 20 |
| `sortBy` | string | Field to sort by | - | "createdAt" |
| `sortOrder` | enum | Sort order (asc/desc) | desc | "asc" |

### Example API Calls

```bash
# Basic pagination
GET /users?page=2&limit=20

# Search with pagination
GET /users?search=john&page=1&limit=10

# Sorting
GET /users?sortBy=name&sortOrder=asc

# Combined filters
GET /users?search=admin&page=1&limit=5&sortBy=createdAt&sortOrder=desc
```

## Response Format

```typescript
{
  "data": [
    // your filtered data
  ],
  "metadata": {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "path": "/users",
    "pages": 5,
    "perPage": 10,
    "total": 50,
    "page": 1,
    "hasNextPage": true,
    "hasPrevPage": false,
    "nextPage": 2,
    "prevPage": null
  }
}
```

## Configuration Options

### IFilterOptions

```typescript
interface IFilterOptions {
  searchFields?: string[];      // Fields to search in
  defaultSortField?: string;    // Default sort field
  allowedSortFields?: string[]; // Allowed sort fields for security
  maxLimit?: number;           // Maximum items per page
}
```

## Utilities

### FilterUtils

The `FilterUtils` class provides helpful utilities:

```typescript
import { FilterUtils } from '../common/filter';

// Extract filters from Express request
const filters = FilterUtils.extractFiltersFromRequest(req);

// Build metadata with request path
const metadata = FilterUtils.buildMetadataWithPath(req, total, page, limit);

// Sanitize search terms
const safeTerm = FilterUtils.sanitizeSearchTerm(userInput);

// Get pagination links
const links = FilterUtils.getPaginationLinks(req, page, totalPages);
```

## Security Considerations

1. **SQL Injection Prevention**: Search terms are properly escaped
2. **Field Validation**: Only allowed sort fields are accepted
3. **Limit Constraints**: Maximum limits prevent resource exhaustion
4. **Input Sanitization**: Search terms are sanitized and limited in length

## Best Practices

1. **Define Search Fields**: Always specify which fields can be searched
2. **Limit Sort Fields**: Use `allowedSortFields` to prevent sorting by sensitive fields
3. **Set Maximum Limits**: Prevent large result sets with `maxLimit`
4. **Use Indexes**: Ensure database indexes exist for searchable and sortable fields
5. **Cache Results**: Consider caching for frequently accessed data

## Examples

See the `test/` directory for comprehensive examples and integration tests.
