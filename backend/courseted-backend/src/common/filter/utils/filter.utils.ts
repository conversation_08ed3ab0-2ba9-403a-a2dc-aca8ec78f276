import { Request } from "express";

import { IMetadata } from "../../interfaces/types";
import { BaseFilterDto } from "../dtos/filter.dto";

export class FilterUtils {
  /**
   * Extract filter parameters from request query
   */
  static extractFiltersFromRequest(req: Request): BaseFilterDto {
    const filters = new BaseFilterDto();

    if (req.query.search) {
      filters.search = req.query.search as string;
    }

    if (req.query.page) {
      filters.page = parseInt(req.query.page as string, 10) || 1;
    }

    if (req.query.limit) {
      filters.limit = parseInt(req.query.limit as string, 10) || 10;
    }

    if (req.query.size) {
      filters.size = parseInt(req.query.size as string, 10) || filters.limit;
    }

    if (req.query.sortBy) {
      filters.sortBy = req.query.sortBy as string;
    }

    if (req.query.sortOrder) {
      filters.sortOrder = req.query.sortOrder as any;
    }

    return filters;
  }

  /**
   * Build metadata with request path
   */
  static buildMetadataWithPath(
    req: Request,
    total: number,
    page: number,
    limit: number,
  ): IMetadata {
    const baseUrl = `${req.protocol}://${req.get("host")}${req.baseUrl}${req.path}`;
    const pages = Math.ceil(total / limit);

    return {
      timestamp: new Date().toISOString(),
      path: baseUrl,
      pages,
      perPage: limit,
      total,
      page,
      hasNextPage: page < pages,
      hasPrevPage: page > 1,
      nextPage: page < pages ? page + 1 : null,
      prevPage: page > 1 ? page - 1 : null,
    };
  }

  /**
   * Validate and sanitize search term
   */
  static sanitizeSearchTerm(searchTerm: string): string {
    if (!searchTerm) return "";

    // Remove special characters that might cause SQL injection
    return searchTerm
      .replace(/[%_\\]/g, "\\$&") // Escape SQL wildcards
      .trim()
      .substring(0, 100); // Limit length
  }

  /**
   * Create URL for pagination links
   */
  static createPaginationUrl(
    baseUrl: string,
    query: Record<string, any>,
    page: number,
  ): string {
    const params = new URLSearchParams();

    Object.entries(query).forEach(([key, value]) => {
      if (key !== "page" && value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    params.append("page", page.toString());

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Get pagination links for API response
   */
  static getPaginationLinks(
    req: Request,
    page: number,
    totalPages: number,
  ): Record<string, string | null> {
    const baseUrl = `${req.protocol}://${req.get("host")}${req.baseUrl}${req.path}`;
    const query = { ...req.query };

    return {
      first:
        totalPages > 0 ? this.createPaginationUrl(baseUrl, query, 1) : null,
      last:
        totalPages > 0
          ? this.createPaginationUrl(baseUrl, query, totalPages)
          : null,
      next:
        page < totalPages
          ? this.createPaginationUrl(baseUrl, query, page + 1)
          : null,
      prev:
        page > 1 ? this.createPaginationUrl(baseUrl, query, page - 1) : null,
    };
  }
}
