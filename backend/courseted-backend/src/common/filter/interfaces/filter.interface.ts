import { IMetadata } from "../../interfaces/types";
import { BaseFilterDto } from "../dtos/filter.dto";

export interface IFilterResult<T> {
  data: T[];
  metadata: IMetadata;
}

export interface IFilterOptions {
  searchFields?: string[];
  defaultSortField?: string;
  allowedSortFields?: string[];
  maxLimit?: number;
}

export interface IFilterService {
  applyFilters<T>(
    query: any,
    filters: BaseFilterDto,
    options?: IFilterOptions,
  ): Promise<IFilterResult<T>>;

  buildSearchCondition(
    searchTerm: string,
    searchFields: string[],
  ): Record<string, any>;

  buildSortCondition(
    sortBy?: string,
    sortOrder?: string,
    allowedFields?: string[],
    defaultField?: string,
  ): Record<string, any>;

  createMetadata(
    total: number,
    page: number,
    limit: number,
    path?: string,
  ): IMetadata;
}
