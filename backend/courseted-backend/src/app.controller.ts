import { Controller, Get, VERSION_NEUTRAL } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { AppService } from "./app.service";

@ApiTags("Application")
@Controller({ version: VERSION_NEUTRAL })
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({
    summary: "Get application information",
    description: "Returns basic application information and health status",
  })
  @ApiResponse({
    status: 200,
    description: "Application information retrieved successfully",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: {
          type: "string",
          example: "Application is running successfully",
        },
        data: {
          type: "object",
          properties: {
            name: { type: "string", example: "Courseted Backend API" },
            version: { type: "string", example: "1.0.0" },
            environment: { type: "string", example: "development" },
            timestamp: { type: "string", example: "2025-07-16T15:21:23.000Z" },
          },
        },
      },
    },
  })
  getApplicationInfo() {
    return {
      success: true,
      message: "Application is running successfully",
      data: {
        name: "Courseted Backend API",
        version: "1.0.0",
        environment: process.env.NODE_ENV || "development",
        timestamp: new Date().toISOString(),
      },
    };
  }

  @Get("v1")
  @ApiOperation({
    summary: "Get application information (v1)",
    description:
      "Returns basic application information and health status for v1 API",
  })
  @ApiResponse({
    status: 200,
    description: "Application information retrieved successfully",
  })
  getApplicationInfoV1() {
    return this.getApplicationInfo();
  }
}
