import { HttpModule } from "@nestjs/axios";
import { <PERSON><PERSON>, Mo<PERSON><PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { APP_GUARD } from "@nestjs/core";
import { ThrottlerGuard, ThrottlerModule } from "@nestjs/throttler";
import { LoggerModule } from "nestjs-pino";

import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { MailModule } from "./common/mail/mail.module";
import { loggerOptions } from "./config/logger";
import { mailConfiguration } from "./config/mail.config";
import { TypeOrmConfig } from "./config/typeorm";
import { AuthModule } from "./modules/auth/auth.module";
import { CountryModule } from "./modules/country/country.module";
import { MeetingModule } from "./modules/meetings/meeting.module";
import { SmsModule } from "./modules/sms/sms.module";
import { UsersModule } from "./modules/users/users.module";

@Module({
  imports: [
    LoggerModule.forRoot(loggerOptions),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [mailConfiguration]
    }),
    ThrottlerModule.forRoot(
      process.env.NODE_ENV === "test"
        ? [] // Disable throttling in test environment
        : [
            {
              name: "short",
              ttl: 1000, // 1 second
              limit: 3, // 3 requests per second
            },
            {
              name: "medium",
              ttl: 10000, // 10 seconds
              limit: 20, // 20 requests per 10 seconds
            },
            {
              name: "long",
              ttl: 60000, // 1 minute
              limit: 100, // 100 requests per minute
            },
          ],
    ),
    TypeOrmConfig,
    UsersModule,
    AuthModule,
    SmsModule,
    CountryModule,
    MeetingModule,
    MailModule,
    HttpModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    Logger,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
