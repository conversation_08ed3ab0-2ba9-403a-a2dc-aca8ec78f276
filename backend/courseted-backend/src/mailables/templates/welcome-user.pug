doctype html
html
  head
    meta(name="viewport", content="width=device-width, initial-scale=1.0")
    meta(http-equiv="Content-Type", content="text/html; charset=UTF-8")
    title Welcome to Courseted!
    style
      include ./styles/main.css
  body
    table(role="presentation", border="0", cellpadding="0", cellspacing="0", style="width: 100%; background-color: #f8f9fa;")
      tr
        td
          //- Include the header partial
          include partials/_header.pug

          //- Main content area
          table(width="100%", cellpadding="0", cellspacing="0")
            tr
              td(align="center")
                table(width="600", cellpadding="0", cellspacing="0", style="background-color: #ffffff;")
                  tr
                    td(style="padding: 40px 30px;")
                      h2(style="color: #333; margin-top: 0; font-size: 24px;") Welcome to Courseted, #{user.name || user.email}!
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | Thank you for joining Courseted! We're excited to have you on board and can't wait to help you on your learning journey.
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | Here's what you can do next:
                      
                      ul(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0; padding-left: 20px;")
                        li Complete your profile setup
                        li Browse our course catalog
                        li Join our community discussions
                        li Start your first course
                      
                      .highlight(style="background-color: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 6px; padding: 20px; margin: 25px 0;")
                        p(style="margin: 0; font-size: 16px; color: #0066cc;")
                          strong Pro Tip: 
                          | Complete your profile in the first 48 hours to unlock exclusive welcome bonuses!
                      
                      table(cellpadding="0", cellspacing="0", style="margin: 30px auto;")
                        tr
                          td
                            a(href=loginUrl, style="display: inline-block; padding: 16px 32px; background-color: #1a73e8; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px;") Get Started Now
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | If you have any questions, our support team is here to help. Just reply to this email or reach out to us anytime.
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | Happy learning!
                        br
                        | The Courseted Team

          //- Include the footer partial
          include partials/_footer.pug
