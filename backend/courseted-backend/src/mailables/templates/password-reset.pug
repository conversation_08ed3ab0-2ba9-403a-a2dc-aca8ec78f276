doctype html
html
  head
    meta(name="viewport", content="width=device-width, initial-scale=1.0")
    meta(http-equiv="Content-Type", content="text/html; charset=UTF-8")
    title Reset Your Password
    style
      include ./styles/main.css
  body
    table(role="presentation", border="0", cellpadding="0", cellspacing="0", style="width: 100%; background-color: #f8f9fa;")
      tr
        td
          //- Include the header partial
          include partials/_header.pug

          //- Main content area
          table(width="100%", cellpadding="0", cellspacing="0")
            tr
              td(align="center")
                table(width="600", cellpadding="0", cellspacing="0", style="background-color: #ffffff;")
                  tr
                    td(style="padding: 40px 30px;")
                      h2(style="color: #333; margin-top: 0; font-size: 24px;") Reset Your Password
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | Hi #{user.name || user.email},
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | We received a request to reset your password for your Courseted account. If you didn't make this request, you can safely ignore this email.
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | To reset your password, click the button below:
                      
                      table(cellpadding="0", cellspacing="0", style="margin: 30px auto;")
                        tr
                          td
                            a(href=resetUrl, style="display: inline-block; padding: 16px 32px; background-color: #dc3545; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px;") Reset My Password
                      
                      .warning(style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 25px 0; color: #856404;")
                        p(style="margin: 0 0 10px 0; font-weight: 600;") Important Security Information:
                        ul(style="margin: 0; padding-left: 20px;")
                          li This link will expire on #{expiresAt.toLocaleDateString()} at #{expiresAt.toLocaleTimeString()}
                          li For your security, this link can only be used once
                          li If you didn't request this reset, please contact our support team immediately
                      
                      p(style="font-size: 14px; line-height: 1.6; color: #666; margin: 25px 0;")
                        | If the button above doesn't work, you can copy and paste this link into your browser:
                        br
                        a(href=resetUrl, style="color: #1a73e8; word-break: break-all;") #{resetUrl}
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | If you continue to have problems, please contact our support team.
                      
                      p(style="font-size: 16px; line-height: 1.6; color: #555; margin: 20px 0;")
                        | Best regards,
                        br
                        | The Courseted Team

          //- Include the footer partial
          include partials/_footer.pug
