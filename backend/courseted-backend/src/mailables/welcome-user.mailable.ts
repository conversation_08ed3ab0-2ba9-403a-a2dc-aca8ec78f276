import { MailBuilder } from "../common/mail/mailables/mail-builder";
import { Mailable } from "../common/mail/mailables/mailable.abstract";
import { RegisterMailable } from "../common/mail/mailables/mailable.registry";

interface WelcomeUserData {
  id?: number;
  email: string;
  name?: string;
}

@RegisterMailable()
export class WelcomeUserMailable extends Mailable<{ user: WelcomeUserData }> {
  constructor(payload: { user: WelcomeUserData }) {
    super(payload);
  }

  configure(builder: MailBuilder): MailBuilder {
    const { user } = this.payload;

    return builder
      .subject("Welcome to Courseted!")
      .template("welcome-user")
      .context({
        user,
        year: new Date().getFullYear(),
        loginUrl: `${process.env.FRONTEND_URL || "https://courseted.com"}/login`,
      });
  }
}
