import { MailBuilder } from "../common/mail/mailables/mail-builder";
import { Mailable } from "../common/mail/mailables/mailable.abstract";
import { RegisterMailable } from "../common/mail/mailables/mailable.registry";

interface PasswordResetUserData {
  id?: number;
  email: string;
  name?: string;
}

interface PasswordResetData {
  user: PasswordResetUserData;
  token: string;
  expiresAt: Date;
}

@RegisterMailable()
export class PasswordResetMailable extends Mailable<PasswordResetData> {
  constructor(payload: PasswordResetData) {
    super(payload);
  }

  configure(builder: MailBuilder): MailBuilder {
    const { user, token, expiresAt } = this.payload;
    const resetUrl = `${process.env.FRONTEND_URL || "https://courseted.com"}/reset-password?token=${token}`;

    return builder
      .subject("Reset Your Password")
      .template("password-reset")
      .context({
        user,
        resetUrl,
        token,
        expiresAt,
        year: new Date().getFullYear(),
      })
      .priority("high");
  }

  // Optional: Implement shouldSend to add business logic
  shouldSend(): boolean {
    // Only send if token hasn't expired
    return new Date() < this.payload.expiresAt;
  }
}
