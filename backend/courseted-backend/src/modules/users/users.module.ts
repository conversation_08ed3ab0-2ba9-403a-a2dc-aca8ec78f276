import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { FilterModule } from "@/common/filter";
import { MailModule } from "@/common/mail";
import { Profile, User } from "@/entities";

import { ProfileService } from "../profile/services/profile.service";
import { UsersController } from "./controllers/users.controller";
import { UsersService } from "./services/users.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Profile]),
    FilterModule,
    MailModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, ProfileService],
  exports: [UsersService, ProfileService],
})
export class UsersModule {}
