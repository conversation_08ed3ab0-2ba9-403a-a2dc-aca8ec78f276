import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Post,
  Query,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";

import { Swagger } from "@/common/decorators";
import { Auth } from "@/common/decorators/auth.decorator";
import {
  ApiInstructorFilter,
  ApiStudentFilter,
  ApiUserFilter,
} from "@/common/decorators/filter.decorator";
import { Roles } from "@/common/decorators/roles.decorator";
import { BaseFilterDto } from "@/common/filter";
import { CommonExceptionFilter } from "@/common/filter/exception.filter";
import { JwtAuthGuard } from "@/common/guards/jwt-auth.guard";
import { RolesGuard } from "@/common/guards/roles.guard";
import { ResponseInterceptor } from "@/common/interceptors/response.interceptor";
import { ProfileDto } from "@/modules/auth/dtos/profile.dto";
import { ProfileService } from "@/modules/profile/services/profile.service";
import { UsersService } from "@/modules/users/services/users.service";
import { UserRole } from "@/types";

@Controller({
  path: "users",
  version: "1",
})
@UseFilters(CommonExceptionFilter)
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(ClassSerializerInterceptor, ResponseInterceptor)
export class UsersController {
  constructor(
    private usersService: UsersService,
    private profileService: ProfileService,
  ) {}

  @Swagger({
    summary: "Get All Users with Filtering",
    operationId: "getUsers",
    description:
      "Get all users with search, pagination, and sorting capabilities",
    authentication: true,
    tags: ["Users"],
    filter: {
      searchDescription: "Search instructors by name or email",
      sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
      maxLimit: 100,
      defaultLimit: 10,
    },
  })
  @ApiUserFilter()
  @Get()
  @Roles(UserRole.ADMIN)
  async findAll(@Query() filters: BaseFilterDto) {
    return await this.usersService.findAllWithFilters(filters);
  }

  @Swagger({
    summary: "Get instructors with filtering",
    operationId: "getInstructors",
    description: "Get instructors with search, pagination, and sorting",
    authentication: true,
    tags: ["Users"],
    filter: {
      searchDescription: "Search instructors by name or email",
      sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
      maxLimit: 100,
      defaultLimit: 10,
    },
  })
  @ApiInstructorFilter()
  @Get("instructors")
  @Roles(UserRole.ADMIN)
  async findInstructors(@Query() filters: BaseFilterDto) {
    return await this.usersService.findByRoleWithFilters(
      UserRole.INSTRUCTOR,
      filters,
    );
  }

  @Swagger({
    summary: "Get students with filtering",
    operationId: "getStudents",
    description: "Get students with search, pagination, and sorting",
    authentication: true,
    tags: ["Users"],
  })
  @ApiStudentFilter()
  @Get("students")
  @Roles(UserRole.ADMIN)
  async findStudents(@Query() filters: BaseFilterDto) {
    return await this.usersService.findByRoleWithFilters(
      UserRole.STUDENT,
      filters,
    );
  }

  @Swagger({
    summary: "Get profile",
    operationId: "getProfile",
    description: "Get profile",
    authentication: true,
    tags: ["Users"],
  })
  @Get("profile")
  @Roles(UserRole.ADMIN)
  async getProfile(@Auth() user) {
    // user will contain the authenticated user object
    return this.usersService.findById(user.id);
  }

  @Swagger({
    summary: "Store profile",
    operationId: "storeProfile",
    description: "Storing profile",
    authentication: true,
    tags: ["Users"],
  })
  @Post("profile")
  async profile(@Auth() user, @Body() profile: ProfileDto) {
    return this.profileService.updateProfile(1, profile);
  }
}
