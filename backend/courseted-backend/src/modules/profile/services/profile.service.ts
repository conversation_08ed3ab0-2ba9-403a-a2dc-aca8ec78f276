import { User } from "@entities/user.entity";
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { Profile } from "@/entities";

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Profile)
    private profileRepository: Repository<Profile>,
  ) {}

  async updateProfile(userId: number, profileData: any): Promise<Profile> {
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ["profile"],
    });
    if (!user) {
      throw new Error("User not found");
    }
    if (!user.profile) {
      user.profile = new Profile();
    }
    Object.assign(user.profile, profileData);
    await this.profileRepository.save(user.profile);
    return user.profile;
  }
}
