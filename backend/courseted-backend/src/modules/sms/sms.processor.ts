import { SmsStatus } from "@entities/sms.entity";
import { OnWorkerEvent, Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { Job } from "bullmq";
import { TwilioService } from "nestjs-twilio";

import { SmsService } from "./services/sms.service";

interface ProcessSmsData {
  to: string;
  body: string;
  from: string;
  id: number; // Database ID
}

@Processor("sms")
export class SmsProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsProcessor.name);

  constructor(
    private readonly twilioService: TwilioService,
    private readonly smsService: SmsService,
  ) {
    super();
  }

  async process(job: Job<ProcessSmsData, any, string>): Promise<any> {
    this.logger.debug(
      `Processing SMS job ${job.id} to ${job.data.to}: ${job.data.body.substring(0, 20)}...`,
    );

    try {
      const result = await this.twilioService.client.messages.create({
        body: job.data.body,
        from: job.data.from,
        to: job.data.to,
      });

      this.logger.log(
        `SMS sent successfully to ${job.data.to}, SID: ${result.sid}`,
      );

      // Update status in database
      await this.smsService.updateSmsStatus(
        job.data.id,
        SmsStatus.SENT,
        result.sid,
      );

      return { success: true, sid: result.sid };
    } catch (error) {
      this.logger.error(
        `Failed to send SMS to ${job.data.to}: ${error.message}`,
        error.stack,
      );

      // Update status in database
      await this.smsService.updateSmsStatus(
        job.data.id,
        SmsStatus.FAILED,
        undefined,
        error.message,
      );

      throw error;
    }
  }

  @OnWorkerEvent("completed")
  onCompleted(job: Job) {
    this.logger.debug(`SMS job ${job.id} completed successfully`);
  }

  @OnWorkerEvent("failed")
  onFailed(job: Job, error: Error) {
    this.logger.error(
      `SMS job ${job.id} failed with error: ${error.message}`,
      error.stack,
    );
  }

  @OnWorkerEvent("stalled")
  onStalled(job: Job) {
    this.logger.warn(`SMS job ${job.id} stalled`);
  }
}
