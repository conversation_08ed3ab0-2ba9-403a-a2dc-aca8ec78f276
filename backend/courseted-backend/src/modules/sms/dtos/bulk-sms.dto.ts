import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayNotEmpty, IsArray, ValidateNested } from "class-validator";

import { SmsDto } from "./sms.message.dto";

export class BulkSmsDto {
  @ApiProperty({
    description: "Array of SMS messages to send",
    type: [SmsDto],
    example: [
      {
        to: "+1234567890",
        body: "Hello, this is a test message 1",
        from: "+1987654321",
      },
      {
        to: "+1234567891",
        body: "Hello, this is a test message 2",
        from: "+1987654321",
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty({ message: "Messages array cannot be empty" })
  @ValidateNested({ each: true })
  @Type(() => SmsDto)
  messages: SmsDto[];
}
