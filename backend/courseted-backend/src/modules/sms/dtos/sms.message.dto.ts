import { ApiProperty } from "@nestjs/swagger";
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Matches,
} from "class-validator";

import { Sms } from "../interfaces/sms";

export class SmsDto implements Sms {
  @ApiProperty({
    example: "+1234567890",
    description: "The recipient phone number",
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: "Phone number must be in international format (e.g., +1234567890)",
  })
  to: string;

  @ApiProperty({
    example: "Hello, this is a test message",
    description: "The SMS message body",
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiProperty({
    example: "+1987654321",
    description: "The sender phone number (optional)",
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message:
      "From phone number must be in international format (e.g., +1987654321)",
  })
  from?: string;

  @ApiProperty({
    example: 1,
    description: "The sender user ID (optional)",
    required: false,
  })
  @IsNumber()
  @IsOptional()
  senderId?: number;
}
