import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";

import { JwtAuthGuard } from "@/common/guards/jwt-auth.guard";
import { ResponseInterceptor } from "@/common/interceptors/response.interceptor";
import { RequestWithUser } from "@/modules/auth/types/request.type";

import { BulkSmsDto } from "../dtos/bulk-sms.dto";
import { SmsDto } from "../dtos/sms.message.dto";
import { SmsService } from "../services/sms.service";

@Controller({
  path: "sms",
  version: "1",
})
@UseGuards(JwtAuthGuard)
@UseInterceptors(ClassSerializerInterceptor, ResponseInterceptor)
export class SmsController {
  constructor(private readonly smsService: SmsService) {}

  @Post("send")
  async sendSms(@Body() message: SmsDto, @Req() req: RequestWithUser) {
    // Set the sender ID from the authenticated user
    if (req.user) {
      message.senderId = req.user.id;
    }
    const jobId = await this.smsService.queueSms(message);
    return { jobId };
  }

  @Post("bulk")
  async sendBulkSms(@Body() payload: BulkSmsDto, @Req() req: RequestWithUser) {
    // Set the sender ID for all messages from the authenticated user
    if (req.user) {
      payload.messages.forEach((message) => {
        message.senderId = req.user.id;
      });
    }
    const jobIds = await this.smsService.queueBulkSms(payload.messages);
    return { count: jobIds.length, jobIds };
  }
}
