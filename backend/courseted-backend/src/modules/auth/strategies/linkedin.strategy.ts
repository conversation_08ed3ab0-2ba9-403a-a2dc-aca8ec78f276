import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { Strategy } from "passport-linkedin-oauth2";

import { AuthService } from "../services/auth.service";
import { OAuthProfile } from "../types/oauth-profile.type";

@Injectable()
export class LinkedInStrategy extends PassportStrategy(Strategy, "linkedin") {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.getOrThrow<string>("LINKEDIN_CLIENT_ID"),
      clientSecret: configService.getOrThrow<string>("LINKEDIN_CLIENT_SECRET"),
      callbackURL: configService.getOrThrow<string>("LINKEDIN_CALLBACK_URL"),
      scope: ["r_emailaddress", "r_liteprofile"],
      profileFields: [
        "id",
        "first-name",
        "last-name",
        "email-address",
        "profile-picture",
      ],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: OAuthProfile,
    done: (err: any, user: any, info?: any) => void,
  ): Promise<any> {
    try {
      const { accessToken: jwt } = await this.authService.validateOAuthLogin(
        profile,
        "linkedin",
      );
      done(null, { jwt });
    } catch (err) {
      done(err, false);
    }
  }
}
