import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { Strategy } from "passport-facebook";

import { AuthService } from "../services/auth.service";
import { OAuthProfile } from "../types/oauth-profile.type";

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, "facebook") {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.getOrThrow<string>("FACEBOOK_APP_ID"),
      clientSecret: configService.getOrThrow<string>("FACEBOOK_APP_SECRET"),
      callbackURL: configService.getOrThrow<string>("FACEBOOK_CALLBACK_URL"),
      scope: ["email", "public_profile"],
      profileFields: ["id", "emails", "name", "photos"],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: OAuthProfile,
    done: (err: any, user: any, info?: any) => void,
  ): Promise<any> {
    try {
      const { accessToken: jwt } = await this.authService.validateOAuthLogin(
        profile,
        "facebook",
      );
      done(null, { jwt });
    } catch (err) {
      done(err, false);
    }
  }
}
