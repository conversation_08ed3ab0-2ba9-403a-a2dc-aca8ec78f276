export const OAuthProvider = {
  GOOGLE: "google",
  FACEBOOK: "facebook",
  LINKEDIN: "linkedin",
};

export type OAuthProviderType =
  (typeof OAuthProvider)[keyof typeof OAuthProvider];

export type OAuthEmail = {
  value: string;
  verified?: boolean;
};

export type OAuthName = {
  givenName: string;
  familyName: string;
  middleName?: string;
};

export type OAuthPhoto = {
  value: string;
};

export type OAuthProfile = {
  id: string;
  displayName?: string;
  name: OAuthName;
  emails: OAuthEmail[];
  photos?: OAuthPhoto[];
  provider: OAuthProviderType;
};
