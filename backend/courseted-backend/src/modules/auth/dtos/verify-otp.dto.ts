import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, MinLength } from "class-validator";

export class VerifyOtpDto {
  @ApiProperty({
    description: "Code for the user",
    example: "123567",
    required: true,
  })
  @IsString()
  @MinLength(6)
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: "The phone number of the user",
    example: "123456789",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;
}
