import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, MinLength } from "class-validator";

export class ResetPasswordDto {
  @ApiProperty({
    example: "abc123xyz789",
    description: "Password reset token",
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    example: "newSecurePassword123",
    description: "New password for the user",
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: "Password must be at least 8 characters long" })
  @IsNotEmpty()
  newPassword: string;
}
