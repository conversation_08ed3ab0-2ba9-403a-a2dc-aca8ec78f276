import { ApiProperty } from "@nestjs/swagger";
import {
  Is<PERSON><PERSON>,
  IsNotEmpty,
  IsNumber,
  IsString,
  Matches,
  MinLength,
} from "class-validator";

export class RegisterDto {
  @ApiProperty({
    description: "The email address of the user",
    example: "<EMAIL>",
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: "The password of the user",
    example: "password123",
    minLength: 6,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: "The phone number of the user",
    example: "+1234567890",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: "Phone number must be in international format (+1234567890)",
  })
  phoneNumber: string;

  @ApiProperty({
    description: "The country ID of the user",
    example: "1",
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  countryId: number;
}
