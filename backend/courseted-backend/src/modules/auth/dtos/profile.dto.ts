import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString, IsUrl } from "class-validator";

export class ProfileDto {
  @ApiProperty({
    description: "The first name of the user",
    example: "<PERSON>",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: "The last name of the user",
    example: "<PERSON><PERSON>",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: "URL to the user's profile picture",
    example: "https://courseted.com/images/profile.jpg",
    required: false,
  })
  @IsUrl()
  @IsOptional()
  profilePicture?: string;
}
