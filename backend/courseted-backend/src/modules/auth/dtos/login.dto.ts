import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsString, MinLength } from "class-validator";

export class LoginDto {
  @ApiProperty({
    example: "<EMAIL>",
    description: "The email of the user",
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: "password123",
    description: "The password of the user",
    minLength: 6,
    required: true,
  })
  @IsString()
  @MinLength(6)
  @IsNotEmpty()
  password: string;
}
