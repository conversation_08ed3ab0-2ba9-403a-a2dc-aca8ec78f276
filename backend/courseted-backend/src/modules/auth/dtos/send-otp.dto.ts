import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, Matches } from "class-validator";

export class SendOtpDto {
  @ApiProperty({
    description: "The phone number to send OTP to",
    example: "+1234567890",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: "Phone number must be in international format (+1234567890)",
  })
  phoneNumber: string;
}
