import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

import { Meeting, MeetingParticipant, MeetingRecording } from '@/entities/meeting.entity';
import { User } from '@/entities/user.entity';

import { MeetingController } from './controllers/meeting.controller';
import { ZoomWebhookController } from './controllers/webhook.controller';
import { MeetingService } from './services/meeting.service';
import { ZoomService } from './services/zoom.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Meeting, MeetingParticipant, MeetingRecording, User]),
    HttpModule,
    ConfigModule,
  ],
  controllers: [MeetingController, ZoomWebhookController],
  providers: [MeetingService, ZoomService],
  exports: [MeetingService, ZoomService],
})
export class MeetingModule {}
