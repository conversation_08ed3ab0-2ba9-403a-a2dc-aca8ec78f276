import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsInt,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';

export class UpdateMeetingDto {
  @ApiProperty({
    description: 'Meeting topic/title',
    example: 'React Advanced Concepts - Updated',
    minLength: 1,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  topic?: string;

  @ApiProperty({
    description: 'Meeting description',
    example: 'Updated description for advanced React concepts',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Meeting start time in ISO format',
    example: '2024-01-15T10:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiProperty({
    description: 'Meeting duration in minutes',
    example: 90,
    minimum: 15,
    maximum: 480,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(15)
  @Max(480)
  duration?: number;

  @ApiProperty({
    description: 'Meeting password',
    example: 'newsecret123',
    required: false,
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'Maximum number of participants',
    example: 150,
    minimum: 1,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  maxParticipants?: number;

  @ApiProperty({
    description: 'Whether recording is enabled',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isRecordingEnabled?: boolean;

  @ApiProperty({
    description: 'Whether waiting room is enabled',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isWaitingRoomEnabled?: boolean;

  @ApiProperty({
    description: 'Whether participants are muted on entry',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isMuteOnEntry?: boolean;
}
