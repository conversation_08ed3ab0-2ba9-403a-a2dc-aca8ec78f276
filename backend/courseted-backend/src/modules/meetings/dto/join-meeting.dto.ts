import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MinLength } from 'class-validator';

export class JoinMeetingDto {
  @ApiProperty({
    description: 'Meeting ID to join',
    example: '123456789',
  })
  @IsString()
  @MinLength(1)
  meetingId: string;

  @ApiProperty({
    description: 'Meeting password if required',
    example: 'secret123',
    required: false,
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'User name for the meeting',
    example: '<PERSON>',
  })
  @IsString()
  @MinLength(1)
  userName: string;

  @ApiProperty({
    description: 'User email for the meeting',
    example: '<EMAIL>',
  })
  @IsString()
  @MinLength(1)
  userEmail: string;
}
