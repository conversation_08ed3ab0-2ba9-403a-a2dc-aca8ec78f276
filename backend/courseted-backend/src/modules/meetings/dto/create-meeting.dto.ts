import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';

import { MeetingType } from '@/entities/meeting.entity';

export class CreateMeetingDto {
  @ApiProperty({
    description: 'Meeting topic/title',
    example: 'React Advanced Concepts',
    minLength: 1,
  })
  @IsString()
  @MinLength(1)
  topic: string;

  @ApiProperty({
    description: 'Meeting description',
    example: 'Advanced React concepts including hooks, context, and performance optimization',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Type of meeting',
    enum: MeetingType,
    example: MeetingType.CLASS,
  })
  @IsEnum(MeetingType)
  type: MeetingType;

  @ApiProperty({
    description: 'Meeting start time in ISO format',
    example: '2024-01-15T10:00:00Z',
  })
  @IsDateString()
  startTime: string;

  @ApiProperty({
    description: 'Meeting duration in minutes',
    example: 90,
    minimum: 15,
    maximum: 480,
  })
  @IsInt()
  @Min(15)
  @Max(480)
  duration: number;

  @ApiProperty({
    description: 'Meeting timezone',
    example: 'America/New_York',
    required: false,
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({
    description: 'Meeting password',
    example: 'secret123',
    required: false,
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'Maximum number of participants',
    example: 100,
    minimum: 1,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  maxParticipants?: number;

  @ApiProperty({
    description: 'Whether recording is enabled',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isRecordingEnabled?: boolean;

  @ApiProperty({
    description: 'Whether waiting room is enabled',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isWaitingRoomEnabled?: boolean;

  @ApiProperty({
    description: 'Whether participants are muted on entry',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isMuteOnEntry?: boolean;

  @ApiProperty({
    description: 'Course ID if this meeting is associated with a course',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  courseId?: number;

  @ApiProperty({
    description: 'Array of user IDs to invite to the meeting',
    example: [1, 2, 3],
    required: false,
  })
  @IsOptional()
  invitedUserIds?: number[];
}
