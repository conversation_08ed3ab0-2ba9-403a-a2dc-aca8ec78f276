import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { Meeting } from '@/entities/meeting.entity';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';

import { MeetingService } from '../services/meeting.service';
import { ZoomService } from '../services/zoom.service';
import { CreateMeetingDto } from '../dto/create-meeting.dto';
import { UpdateMeetingDto } from '../dto/update-meeting.dto';
import { JoinMeetingDto } from '../dto/join-meeting.dto';
import { MeetingQueryDto } from '../dto/meeting-query.dto';
import { ApiResponse as ApiResponseInterceptor } from '@/common/interceptors/response.interceptor';

@ApiTags('meetings')
@Controller('meetings')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MeetingController {
  constructor(
    private readonly meetingService: MeetingService,
    private readonly zoomService: ZoomService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new meeting' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Meeting created successfully',
    type: Meeting,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Students cannot create meetings',
  })
  async createMeeting(
    @Body() createMeetingDto: CreateMeetingDto,
    @Request() req: any,
  ): Promise<Meeting> {
    return this.meetingService.createMeeting(createMeetingDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all meetings with filters and pagination' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Meetings retrieved successfully',
  })
  async getMeetings(
    @Query() query: MeetingQueryDto,
    @Request() req: any,
  ): Promise<{ data: Meeting[]; total: number; page: number; limit: number }> {
    const result = await this.meetingService.getMeetings(query, req.user);
    return {
      ...result,
      page: query.page || 1,
      limit: query.limit || 10,
    };
  }

  @Get('upcoming')
  @ApiOperation({ summary: 'Get upcoming meetings for the current user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Upcoming meetings retrieved successfully',
    type: [Meeting],
  })
  async getUpcomingMeetings(@Request() req: any): Promise<ApiResponseInterceptor<Meeting[]>> {
    return this.meetingService.getUpcomingMeetings(req.user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get meeting by ID' })
  @ApiParam({ name: 'id', description: 'Meeting ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Meeting retrieved successfully',
    type: Meeting,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Meeting not found',
  })
  async getMeetingById(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<Meeting> {
    return this.meetingService.getMeetingById(id, req.user);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update meeting' })
  @ApiParam({ name: 'id', description: 'Meeting ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Meeting updated successfully',
    type: Meeting,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Meeting not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'You can only update your own meetings',
  })
  async updateMeeting(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateMeetingDto: UpdateMeetingDto,
    @Request() req: any,
  ): Promise<Meeting> {
    return this.meetingService.updateMeeting(id, updateMeetingDto, req.user);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete meeting' })
  @ApiParam({ name: 'id', description: 'Meeting ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Meeting deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Meeting not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'You can only delete your own meetings',
  })
  async deleteMeeting(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<void> {
    return this.meetingService.deleteMeeting(id, req.user);
  }

  @Get('signature/:id')
  @ApiOperation({ summary: 'Get meeting signature for SDK' })
  @ApiParam({ name: 'id', description: 'Meeting ID' })
  @ApiQuery({ name: 'role', description: 'User role (0=participant, 1=host)', required: false })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Meeting signature generated successfully',
  })
  async getMeetingSignature(
    @Param('id', ParseIntPipe) id: number,
    @Query('role') role: string = '0',
    @Request() req: any,
  ): Promise<{ signature: string }> {
    const meeting = await this.meetingService.getMeetingById(id, req.user);
    const userRole = meeting.hostId === req.user.id ? 1 : parseInt(role);
    const signature = this.zoomService.generateSDKSignature(meeting.meetingId, userRole);
    
    return { signature };
  }

  @Post('join')
  @ApiOperation({ summary: 'Join a meeting' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Meeting join URL generated successfully',
  })
  async joinMeeting(
    @Body() joinMeetingDto: JoinMeetingDto,
  ): Promise<{ joinUrl: string }> {
    // For now, we'll just return the standard Zoom join URL
    // In a more advanced implementation, you might want to validate the meeting
    // and generate a custom join URL with authentication
    const joinUrl = `https://zoom.us/j/${joinMeetingDto.meetingId}${
      joinMeetingDto.password ? `?pwd=${joinMeetingDto.password}` : ''
    }`;
    
    return { joinUrl };
  }

  @Get(':id/recordings')
  @ApiOperation({ summary: 'Get meeting recordings' })
  @ApiParam({ name: 'id', description: 'Meeting ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Meeting recordings retrieved successfully',
  })
  async getMeetingRecordings(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<any> {
    const meeting = await this.meetingService.getMeetingById(id, req.user);
    return this.zoomService.getMeetingRecordings(meeting.meetingId);
  }

  @Post(':id/invite')
  @ApiOperation({ summary: 'Invite participants to meeting' })
  @ApiParam({ name: 'id', description: 'Meeting ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Participants invited successfully',
  })
  async inviteParticipants(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { userIds: number[] },
    @Request() req: any,
  ): Promise<{ message: string }> {
    // Verify user has permission to invite participants
    await this.meetingService.getMeetingById(id, req.user);
    await this.meetingService.inviteParticipants(id, body.userIds);
    
    return { message: 'Participants invited successfully' };
  }
}
