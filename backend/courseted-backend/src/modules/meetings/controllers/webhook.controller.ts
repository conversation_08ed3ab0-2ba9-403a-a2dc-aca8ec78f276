import {
  Controller,
  Post,
  Body,
  Headers,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

import { MeetingService } from '../services/meeting.service';

interface ZoomWebhookEvent {
  event: string;
  payload: {
    account_id: string;
    object: {
      uuid: string;
      id: number;
      host_id: string;
      topic: string;
      type: number;
      start_time: string;
      duration: number;
      timezone: string;
      join_url: string;
      password?: string;
    };
  };
  event_ts: number;
}

@ApiTags('zoom-webhooks')
@Controller('webhooks/zoom')
export class ZoomWebhookController {
  private readonly logger = new Logger(ZoomWebhookController.name);

  constructor(
    private readonly meetingService: MeetingService,
    private readonly configService: ConfigService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Handle Zoom webhook events' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully',
  })
  async handleWebhook(
    @Body() body: ZoomWebhookEvent,
    @Headers() headers: any,
  ): Promise<{ status: string }> {
    // Verify webhook signature
    if (!this.verifyWebhookSignature(body, headers)) {
      throw new HttpException('Invalid webhook signature', HttpStatus.UNAUTHORIZED);
    }

    this.logger.log(`Received Zoom webhook event: ${body.event}`);

    try {
      switch (body.event) {
        case 'meeting.started':
          await this.handleMeetingStarted(body);
          break;
        case 'meeting.ended':
          await this.handleMeetingEnded(body);
          break;
        case 'meeting.participant_joined':
          await this.handleParticipantJoined(body);
          break;
        case 'meeting.participant_left':
          await this.handleParticipantLeft(body);
          break;
        case 'recording.completed':
          await this.handleRecordingCompleted(body);
          break;
        default:
          this.logger.log(`Unhandled webhook event: ${body.event}`);
      }

      return { status: 'success' };
    } catch (error) {
      this.logger.error(`Error processing webhook event ${body.event}:`, error);
      throw new HttpException('Error processing webhook', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private verifyWebhookSignature(body: any, headers: any): boolean {
    const webhookSecret = this.configService.get<string>('ZOOM_WEBHOOK_SECRET');
    if (!webhookSecret) {
      this.logger.warn('Zoom webhook secret not configured, skipping signature verification');
      return true; // Allow in development if secret not set
    }

    const signature = headers['authorization'];
    if (!signature) {
      return false;
    }

    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(JSON.stringify(body))
      .digest('hex');

    return signature === expectedSignature;
  }

  private async handleMeetingStarted(event: ZoomWebhookEvent): Promise<void> {
    const meetingId = event.payload.object.id.toString();
    this.logger.log(`Meeting started: ${meetingId}`);

    // Update meeting status to live
    // Note: You'll need to implement this method in MeetingService
    // await this.meetingService.updateMeetingStatus(meetingId, MeetingStatus.LIVE);
  }

  private async handleMeetingEnded(event: ZoomWebhookEvent): Promise<void> {
    const meetingId = event.payload.object.id.toString();
    this.logger.log(`Meeting ended: ${meetingId}`);

    // Update meeting status to ended
    // Note: You'll need to implement this method in MeetingService
    // await this.meetingService.updateMeetingStatus(meetingId, MeetingStatus.ENDED);
  }

  private async handleParticipantJoined(event: ZoomWebhookEvent): Promise<void> {
    const meetingId = event.payload.object.id.toString();
    this.logger.log(`Participant joined meeting: ${meetingId}`);

    // Update participant join time
    // Note: You'll need to implement this method in MeetingService
    // await this.meetingService.updateParticipantJoinTime(meetingId, participantId, new Date());
  }

  private async handleParticipantLeft(event: ZoomWebhookEvent): Promise<void> {
    const meetingId = event.payload.object.id.toString();
    this.logger.log(`Participant left meeting: ${meetingId}`);

    // Update participant leave time
    // Note: You'll need to implement this method in MeetingService
    // await this.meetingService.updateParticipantLeaveTime(meetingId, participantId, new Date());
  }

  private async handleRecordingCompleted(event: ZoomWebhookEvent): Promise<void> {
    const meetingId = event.payload.object.id.toString();
    this.logger.log(`Recording completed for meeting: ${meetingId}`);

    // Save recording information
    // Note: You'll need to implement this method in MeetingService
    // await this.meetingService.saveRecordingInfo(meetingId, recordingData);
  }
}
