import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
require("dotenv").config();


export interface ZoomMeetingRequest {
  topic: string;
  type: number;
  start_time: string;
  duration: number;
  timezone?: string;
  password?: string;
  settings: {
    host_video: boolean;
    participant_video: boolean;
    join_before_host: boolean;
    mute_upon_entry: boolean;
    watermark: boolean;
    use_pmi: boolean;
    approval_type: number;
    audio: string;
    auto_recording: string;
    waiting_room: boolean;
  };
}

export interface ZoomMeetingResponse {
  id: number;
  uuid: string;
  host_id: string;
  topic: string;
  type: number;
  status: string;
  start_time: string;
  duration: number;
  timezone: string;
  password: string;
  join_url: string;
  start_url: string;
  settings: any;
}

interface ZoomOAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
  api_url: string;
}

@Injectable()
export class ZoomService {
  private readonly logger = new Logger(ZoomService.name);
  private readonly baseUrl = 'https://api.zoom.us/v2';

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) { }

  private async oAuthToken() {
    const applicationId = this.configService.get<string>('ZOOM_ACCOUNT_ID');
    const apiKey = this.configService.get<string>('ZOOM_API_KEY');
    const apiSecret = this.configService.get<string>('ZOOM_API_SECRET');

    if (!applicationId || !apiKey || !apiSecret) {
      throw new Error('Zoom API credentials not configured');
    }

    // https://zoom.us/oauth/token?grant_type=account_credentials&account_id=lRmY8wc-QlmJDXP93963Zw

    const response = await firstValueFrom(
      this.httpService.post(
        `https://zoom.us/oauth/token?grant_type=account_credentials&account_id=${applicationId}`,
        {

        },
        {
          headers: {
            Authorization: `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`,
          },
        },
      )
    );

    return response.data;
  }

  /**
   * Generate JWT token for Zoom API authentication
   */
  private async generateJWT(): Promise<string> {
    // const apiKey = this.configService.get<string>('ZOOM_API_KEY');
    // const apiSecret = this.configService.get<string>('ZOOM_API_SECRET');

    // console.log('apiKey', apiKey);
    // console.log('apiSecret', apiSecret);


    // if (!apiKey || !apiSecret) {
    //   throw new Error('Zoom API credentials not configured');
    // }

    // const payload = {
    //   iss: apiKey,
    //   exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour expiration
    // };

    // return jwt.sign(payload, apiSecret);
    const token = await this.oAuthToken();

    // console.log('token:::', token);

    return token.access_token;
  }

  /**
   * Generate SDK signature for client-side SDK
   */
  generateSDKSignature(meetingNumber: string, role: number): string {
    const sdkKey = this.configService.get<string>('ZOOM_SDK_KEY');
    const sdkSecret = this.configService.get<string>('ZOOM_SDK_SECRET');

    if (!sdkKey || !sdkSecret) {
      throw new Error('Zoom SDK credentials not configured');
    }

    const timestamp = new Date().getTime() - 30000;
    const msg = Buffer.from(sdkKey + meetingNumber + timestamp + role).toString('base64');
    const hash = crypto.createHmac('sha256', sdkSecret).update(msg).digest('hex');
    const signature = Buffer.from(`${sdkKey}.${meetingNumber}.${timestamp}.${role}.${hash}`).toString('base64');

    return signature;
  }

  /**
   * Get authorization headers for Zoom API
   */
  private async getAuthHeaders() {
    return {
      Authorization: `Bearer ${await this.generateJWT()}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Create a new Zoom meeting
   */
  async createMeeting(meetingData: ZoomMeetingRequest): Promise<ZoomMeetingResponse> {
    try {
      // console.log('this.getAuthHeaders():::',await this.getAuthHeaders());
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/users/me/meetings`,
          meetingData,
          { headers: await this.getAuthHeaders() }
        )
      );

      this.logger.log(`Created Zoom meeting: ${response.data.id}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to create Zoom meeting', error.response?.data || error.message);
      throw new Error(`Failed to create Zoom meeting: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get Zoom meeting details
   */
  async getMeeting(meetingId: string): Promise<ZoomMeetingResponse> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/meetings/${meetingId}`,
          { headers: await this.getAuthHeaders() }
        )
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get Zoom meeting ${meetingId}`, error.response?.data || error.message);
      throw new Error(`Failed to get Zoom meeting: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Update Zoom meeting
   */
  async updateMeeting(meetingId: string, meetingData: Partial<ZoomMeetingRequest>): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.patch(
          `${this.baseUrl}/meetings/${meetingId}`,
          meetingData,
          { headers: await this.getAuthHeaders() }
        )
      );

      this.logger.log(`Updated Zoom meeting: ${meetingId}`);
    } catch (error) {
      this.logger.error(`Failed to update Zoom meeting ${meetingId}`, error.response?.data || error.message);
      throw new Error(`Failed to update Zoom meeting: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete Zoom meeting
   */
  async deleteMeeting(meetingId: string): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.delete(
          `${this.baseUrl}/meetings/${meetingId}`,
          { headers: await this.getAuthHeaders() }
        )
      );

      this.logger.log(`Deleted Zoom meeting: ${meetingId}`);
    } catch (error) {
      this.logger.error(`Failed to delete Zoom meeting ${meetingId}`, error.response?.data || error.message);
      throw new Error(`Failed to delete Zoom meeting: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get meeting recordings
   */
  async getMeetingRecordings(meetingId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/meetings/${meetingId}/recordings`,
          { headers: await this.getAuthHeaders() }
        )
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get recordings for meeting ${meetingId}`, error.response?.data || error.message);
      throw new Error(`Failed to get meeting recordings: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * List user's meetings
   */
  async listMeetings(userId: string = 'me', type: 'scheduled' | 'live' | 'upcoming' = 'scheduled'): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/users/${userId}/meetings`,
          {
            headers: await this.getAuthHeaders(),
            params: { type }
          }
        )
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to list meetings for user ${userId}`, error.response?.data || error.message);
      throw new Error(`Failed to list meetings: ${error.response?.data?.message || error.message}`);
    }
  }
}
