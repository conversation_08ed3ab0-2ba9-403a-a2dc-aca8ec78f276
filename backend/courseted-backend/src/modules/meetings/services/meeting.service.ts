import { Injectable, Logger, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Between } from 'typeorm';

import { Meeting, MeetingStatus, MeetingParticipant } from '@/entities/meeting.entity';
import { User } from '@/entities/user.entity';
import { UserRole } from '@/types';

import { CreateMeetingDto } from '../dto/create-meeting.dto';
import { UpdateMeetingDto } from '../dto/update-meeting.dto';
import { MeetingQueryDto } from '../dto/meeting-query.dto';
import { ZoomService, ZoomMeetingRequest } from './zoom.service';
import { ApiResponse } from '@/common/interceptors/response.interceptor';

@Injectable()
export class MeetingService {
  private readonly logger = new Logger(MeetingService.name);

  constructor(
    @InjectRepository(Meeting)
    private readonly meetingRepository: Repository<Meeting>,
    @InjectRepository(MeetingParticipant)
    private readonly participantRepository: Repository<MeetingParticipant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly zoomService: ZoomService,
  ) { }

  /**
   * Create a new meeting
   */
  async createMeeting(createMeetingDto: CreateMeetingDto, hostUser: User): Promise<Meeting> {
    // Check if user can create meetings
    if (hostUser.role === UserRole.STUDENT) {
      throw new ForbiddenException('Students cannot create meetings');
    }

    try {
      // Prepare Zoom meeting data
      const zoomMeetingData: ZoomMeetingRequest = {
        topic: createMeetingDto.topic,
        type: 2, // Scheduled meeting
        start_time: createMeetingDto.startTime,
        duration: createMeetingDto.duration,
        timezone: createMeetingDto.timezone || 'UTC',
        password: createMeetingDto.password,
        settings: {
          host_video: true,
          participant_video: true,
          join_before_host: false,
          mute_upon_entry: createMeetingDto.isMuteOnEntry ?? true,
          watermark: false,
          use_pmi: false,
          approval_type: createMeetingDto.isWaitingRoomEnabled ? 2 : 0,
          audio: 'both',
          auto_recording: createMeetingDto.isRecordingEnabled ? 'cloud' : 'none',
          waiting_room: createMeetingDto.isWaitingRoomEnabled ?? true,
        },
      };

      // Create meeting in Zoom
      const zoomMeeting = await this.zoomService.createMeeting(zoomMeetingData);

      // Create meeting in database
      const meeting = this.meetingRepository.create({
        meetingId: zoomMeeting.id.toString(),
        topic: createMeetingDto.topic,
        description: createMeetingDto.description,
        type: createMeetingDto.type,
        status: MeetingStatus.SCHEDULED,
        startTime: new Date(createMeetingDto.startTime),
        duration: createMeetingDto.duration,
        timezone: createMeetingDto.timezone || 'UTC',
        password: createMeetingDto.password,
        joinUrl: zoomMeeting.join_url,
        startUrl: zoomMeeting.start_url,
        maxParticipants: createMeetingDto.maxParticipants || 100,
        isRecordingEnabled: createMeetingDto.isRecordingEnabled || false,
        isWaitingRoomEnabled: createMeetingDto.isWaitingRoomEnabled ?? true,
        isMuteOnEntry: createMeetingDto.isMuteOnEntry ?? true,
        hostId: hostUser.id,
        hostEmail: hostUser.email,
        courseId: createMeetingDto.courseId,
      });

      const savedMeeting = await this.meetingRepository.save(meeting);

      // Invite users if specified
      if (createMeetingDto.invitedUserIds?.length) {
        await this.inviteParticipants(savedMeeting.id, createMeetingDto.invitedUserIds);
      }

      this.logger.log(`Created meeting ${savedMeeting.id} for host ${hostUser.id}`);
      return savedMeeting;
    } catch (error) {
      this.logger.error('Failed to create meeting', error);
      throw new BadRequestException(`Failed to create meeting: ${error.message}`);
    }
  }

  /**
   * Get all meetings with filters and pagination
   */
  async getMeetings(query: MeetingQueryDto, user: User): Promise<{ data: Meeting[]; total: number }> {
    const { page = 1, limit = 10, status, type, hostId, courseId, startDate, endDate } = query;

    const queryBuilder = this.meetingRepository
      .createQueryBuilder('meeting')
      .leftJoinAndSelect('meeting.host', 'host')
      .leftJoinAndSelect('host.profile', 'profile');

    // Apply filters based on user role
    if (user.role === UserRole.STUDENT) {
      // Students can only see meetings they're invited to or public meetings
      queryBuilder
        .leftJoin('meeting.participants', 'participant')
        .where('participant.userId = :userId', { userId: user.id });
    } else if (user.role === UserRole.INSTRUCTOR) {
      // Instructors can see their own meetings
      queryBuilder.where('meeting.hostId = :hostId', { hostId: user.id });
    }
    // Admins can see all meetings (no additional filter)

    // Apply additional filters
    if (status) {
      queryBuilder.andWhere('meeting.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('meeting.type = :type', { type });
    }

    if (hostId && user.role === UserRole.ADMIN) {
      queryBuilder.andWhere('meeting.hostId = :hostId', { hostId });
    }

    if (courseId) {
      queryBuilder.andWhere('meeting.courseId = :courseId', { courseId });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('meeting.startTime BETWEEN :startDate AND :endDate', {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
    }

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by start time
    queryBuilder.orderBy('meeting.startTime', 'DESC');

    const [meetings, total] = await queryBuilder.getManyAndCount();

    console.log('meetings', meetings);


    return { data: meetings, total };
  }

  /**
   * Get meeting by ID
   */
  async getMeetingById(id: number, user: User): Promise<Meeting> {
    const meeting = await this.meetingRepository.findOne({
      where: { id },
      relations: ['host', 'host.profile', 'participants', 'participants.user'],
    });

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    // Check access permissions
    if (user.role === UserRole.STUDENT) {
      const isParticipant = meeting.participants?.some(p => p.userId === user.id);
      if (!isParticipant) {
        throw new ForbiddenException('You do not have access to this meeting');
      }
    } else if (user.role === UserRole.INSTRUCTOR && meeting.hostId !== user.id) {
      throw new ForbiddenException('You can only access your own meetings');
    }

    return meeting;
  }

  /**
   * Update meeting
   */
  async updateMeeting(id: number, updateMeetingDto: UpdateMeetingDto, user: User): Promise<Meeting> {
    const meeting = await this.getMeetingById(id, user);

    // Check if user can update this meeting
    if (user.role !== UserRole.ADMIN && meeting.hostId !== user.id) {
      throw new ForbiddenException('You can only update your own meetings');
    }

    try {
      // Update Zoom meeting if necessary
      const zoomUpdateData: any = {};
      if (updateMeetingDto.topic) zoomUpdateData.topic = updateMeetingDto.topic;
      if (updateMeetingDto.startTime) zoomUpdateData.start_time = updateMeetingDto.startTime;
      if (updateMeetingDto.duration) zoomUpdateData.duration = updateMeetingDto.duration;
      if (updateMeetingDto.password !== undefined) zoomUpdateData.password = updateMeetingDto.password;

      if (Object.keys(zoomUpdateData).length > 0) {
        await this.zoomService.updateMeeting(meeting.meetingId, zoomUpdateData);
      }

      // Update database record
      Object.assign(meeting, updateMeetingDto);
      if (updateMeetingDto.startTime) {
        meeting.startTime = new Date(updateMeetingDto.startTime);
      }

      const updatedMeeting = await this.meetingRepository.save(meeting);
      this.logger.log(`Updated meeting ${id}`);

      return updatedMeeting;
    } catch (error) {
      this.logger.error(`Failed to update meeting ${id}`, error);
      throw new BadRequestException(`Failed to update meeting: ${error.message}`);
    }
  }

  /**
   * Delete meeting
   */
  async deleteMeeting(id: number, user: User): Promise<void> {
    const meeting = await this.getMeetingById(id, user);

    // Check if user can delete this meeting
    if (user.role !== UserRole.ADMIN && meeting.hostId !== user.id) {
      throw new ForbiddenException('You can only delete your own meetings');
    }

    try {
      // Delete from Zoom
      await this.zoomService.deleteMeeting(meeting.meetingId);

      // Delete from database
      await this.meetingRepository.remove(meeting);
      this.logger.log(`Deleted meeting ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete meeting ${id}`, error);
      throw new BadRequestException(`Failed to delete meeting: ${error.message}`);
    }
  }

  /**
   * Get upcoming meetings for a user
   */
  async getUpcomingMeetings(user: User): Promise<ApiResponse<Meeting[]>> {
    const now = new Date();
    const queryBuilder = this.meetingRepository
      .createQueryBuilder('meeting')
      .leftJoinAndSelect('meeting.host', 'host')
      .leftJoinAndSelect('host.profile', 'profile')
      .where('meeting.startTime > :now', { now })
      .andWhere('meeting.status = :status', { status: MeetingStatus.SCHEDULED });

    if (user.role === UserRole.STUDENT) {
      queryBuilder
        .leftJoin('meeting.participants', 'participant')
        .andWhere('participant.userId = :userId', { userId: user.id });
    } else if (user.role === UserRole.INSTRUCTOR) {
      queryBuilder.andWhere('meeting.hostId = :hostId', { hostId: user.id });
    }

    queryBuilder.orderBy('meeting.startTime', 'ASC').limit(10);

    const meetings = await queryBuilder.getMany();

    if (!meetings.length) {
      return {
        data: [],
        message: 'No upcoming meetings found',
        success: true,
        status: 'success'
      };
    }

    return {
      data: meetings,
      message: 'Upcoming meetings retrieved successfully',
      success: true,
      status: 'success'
    };
  }

  /**
   * Invite participants to a meeting
   */
  async inviteParticipants(meetingId: number, userIds: number[]): Promise<void> {
    const meeting = await this.meetingRepository.findOne({ where: { id: meetingId } });
    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    const participants = userIds.map(userId =>
      this.participantRepository.create({
        meetingId,
        userId,
        isInvited: true,
        invitationStatus: 'pending',
      })
    );

    await this.participantRepository.save(participants);
    this.logger.log(`Invited ${userIds.length} participants to meeting ${meetingId}`);
  }
}
