import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { Country } from "@/entities/country.entity";

@Injectable()
export class CountryService {
  constructor(
    @InjectRepository(Country)
    private countryRepository: Repository<Country>,
  ) {}

  async findAll(): Promise<Country[]> {
    return this.countryRepository.find({
      order: {
        name: "ASC",
      },
    });
  }

  async findByCode(code: string): Promise<Country | null> {
    return this.countryRepository.findOne({
      where: { code },
    });
  }

  async findById(id: number): Promise<Country | null> {
    return this.countryRepository.findOne({
      where: { id },
    });
  }
}
