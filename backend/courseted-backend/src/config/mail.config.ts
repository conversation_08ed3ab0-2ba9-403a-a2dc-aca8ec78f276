import { registerAs } from "@nestjs/config";
import { plainToInstance } from "class-transformer";
import { IsNumber, IsOptional, IsString } from "class-validator";

export enum MailDriver {
  SES = "ses",
  SMTP = "smtp",
  LOG = "log",
}

class MailConfigValidator {
  @IsString()
  @IsOptional()
  NODE_ENV: string = "development";

  @IsString()
  MAIL_DRIVER: string;

  @IsString()
  @IsOptional()
  MAIL_HOST: string;

  @IsNumber()
  @IsOptional()
  MAIL_PORT: number;

  @IsString()
  @IsOptional()
  MAIL_USER: string;

  @IsString()
  @IsOptional()
  MAIL_PASS: string;

  @IsString()
  MAIL_FROM_ADDRESS: string;

  @IsString()
  MAIL_FROM_NAME: string;

  @IsString()
  @IsOptional()
  AWS_ACCESS_KEY_ID: string;

  @IsString()
  @IsOptional()
  AWS_SECRET_ACCESS_KEY: string;

  @IsString()
  @IsOptional()
  AWS_REGION: string;

  @IsString()
  @IsOptional()
  REDIS_HOST: string;

  @IsNumber()
  @IsOptional()
  REDIS_PORT: number;

  @IsString()
  @IsOptional()
  MAIL_QUEUE_NAME: string;
}

export const mailConfiguration = registerAs("mail", () => {
  const config = {
    NODE_ENV: process.env.NODE_ENV || "development",
    MAIL_DRIVER: process.env.MAIL_DRIVER || "log",
    MAIL_HOST: process.env.MAIL_HOST,
    MAIL_PORT: parseInt(process.env.MAIL_PORT, 10) || 587,
    MAIL_USER: process.env.MAIL_USER,
    MAIL_PASS: process.env.MAIL_PASS,
    MAIL_FROM_ADDRESS: process.env.MAIL_FROM_ADDRESS || "<EMAIL>",
    MAIL_FROM_NAME: process.env.MAIL_FROM_NAME || "Courseted",
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    AWS_REGION: process.env.AWS_REGION || "us-east-1",
    REDIS_HOST: process.env.REDIS_HOST || "localhost",
    REDIS_PORT: parseInt(process.env.REDIS_PORT, 10) || 6379,
    MAIL_QUEUE_NAME: process.env.MAIL_QUEUE_NAME || "mail",
  };

  const validatedConfig = plainToInstance(MailConfigValidator, config, {
    enableImplicitConversion: true,
  });

  return validatedConfig;
});

export type MailConfig = ReturnType<typeof mailConfiguration>;
