export default {
  origin: [
    "http://localhost:3000",
    "http://localhost:80",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:80",
    "http://localhost",
    "http://dev.courseted.com",
    "https://dev.courseted.com",
    process.env.FRONTEND_URL,
  ].filter(Boolean),
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
  ],
  credentials: true,
};
