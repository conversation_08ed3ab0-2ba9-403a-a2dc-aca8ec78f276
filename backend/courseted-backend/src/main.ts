import { Logger as NestLogger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { SwaggerModule } from "@nestjs/swagger";
import * as compression from "compression";
import { writeFile } from "fs/promises";
import helmet from "helmet";
import { join } from "path";

import { getApplicationInstance } from "./app";
import { getDocument } from "./document";

async function bootstrap() {
  const app = await getApplicationInstance();

  // Security middleware
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }),
  );

  // Compression middleware
  app.use(compression());

  const configService = app.get(ConfigService);
  const env = configService.getOrThrow<string>("NODE_ENV");
  
  const PORT: number = Number(configService.getOrThrow<number>("PORT")) || 8000;

  console.log("PORT", PORT);
  

  if (env === "development") {
    const document = getDocument(app);
    SwaggerModule.setup("api-docs", app, document);
    await writeFile(
      join(__dirname, "..", "./docs/api/swagger.json"),
      JSON.stringify(document, null, 2),
    );

    NestLogger.log("API code generated successfully", "Bootstrap");
  }
  await app.listen(PORT, () => NestLogger.log(`Server Port at ${PORT}`));

  return app.getUrl();
}

(async (): Promise<void> => {
  try {
    const url = await bootstrap();
    NestLogger.log(url, "Bootstrap");
  } catch (error) {
    NestLogger.error(error, "Bootstrap");
  }
})();
