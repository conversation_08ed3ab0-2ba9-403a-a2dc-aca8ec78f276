import { ApiProperty } from "@nestjs/swagger";
import {
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";

import { BaseEntity } from "@/common/base/base.entity";

import { User } from "./user.entity";

export enum SmsStatus {
  QUEUED = "queued",
  SENT = "sent",
  FAILED = "failed",
  DELIVERED = "delivered",
}

@Entity("sms_queued")
export class SmsQueued extends BaseEntity {
  @ApiProperty({
    description: "The unique identifier of the SMS message",
    example: "1",
  })
  @PrimaryGeneratedColumn("increment")
  id?: number;

  @ApiProperty({
    description: "The phone number the message is sent to",
    example: "+12345678901",
  })
  @Column()
  to: string;

  @ApiProperty({
    description: "The phone number the message is sent from",
    example: "+19876543210",
  })
  @Column()
  from: string;

  @ApiProperty({
    description: "The content of the SMS message",
    example: "Your verification code is 123456",
  })
  @Column("text")
  body: string;

  @ApiProperty({
    description: "The status of the SMS message",
    enum: SmsStatus,
    enumName: "SmsStatus",
    example: SmsStatus.QUEUED,
    default: SmsStatus.QUEUED,
  })
  @Index("idx_sms_status")
  @Column({
    type: process.env.NODE_ENV === "test" ? "varchar" : "enum",
    enum: process.env.NODE_ENV === "test" ? undefined : SmsStatus,
    default: SmsStatus.QUEUED,
    length: process.env.NODE_ENV === "test" ? 20 : undefined,
  })
  status: SmsStatus;

  @ApiProperty({
    description: "The Twilio SID for the message",
    example: "SM123456789abcdef",
    required: false,
  })
  @Column({ nullable: true })
  twilioSid?: string;

  @ApiProperty({
    description: "The Bull MQ job ID for the message",
    example: "123456789",
    required: false,
  })
  @Column({ nullable: true })
  jobId?: string;

  @ApiProperty({
    description: "Error message if sending failed",
    example: "Invalid phone number format",
    required: false,
  })
  @Column({ nullable: true })
  errorMessage?: string;

  @ApiProperty({
    description: "The user who sent the message (if applicable)",
    required: false,
  })
  @ManyToOne(() => User, { nullable: true })
  sender?: User;

  @ApiProperty({
    description: "The ID of the user who sent the message",
    required: false,
  })
  @Column({ nullable: true })
  senderId?: number;
}
