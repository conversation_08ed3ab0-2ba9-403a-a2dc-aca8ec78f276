import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from "typeorm";

import { BaseEntity } from "@/common/base/base.entity";

@Entity("countries")
export class Country extends BaseEntity {
  @ApiProperty({
    description: "The unique identifier of the country",
    example: "1",
  })
  @PrimaryGeneratedColumn("increment")
  id: number;

  @ApiProperty({
    description: "The name of the country",
    example: "United States",
  })
  @Column({ unique: true })
  name: string;

  @ApiProperty({
    description: "The ISO 3166-1 alpha-2 country code",
    example: "US",
  })
  @Column({ unique: true, length: 2 })
  code: string;

  @ApiProperty({
    description: "The ISO 3166-1 alpha-3 country code",
    example: "USA",
  })
  @Column({ unique: true, length: 3 })
  iso3: string;

  @ApiProperty({
    description: "The international dialing code",
    example: "+1",
  })
  @Column({ nullable: true })
  phoneCode: string;

  @ApiProperty({
    description: "The emoji flag of the country",
    example: "🇺🇸",
    required: false,
  })
  @Column({ nullable: true })
  flag: string;

  @ApiProperty({
    description: "Profiles associated with this country",
    type: () => ["Profile"],
  })
  @OneToMany("Profile", "country")
  profiles: any[];
}
