import { ApiProperty } from "@nestjs/swagger";
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from "typeorm";

import { BaseEntity } from "@/common/base/base.entity";

import { Country } from "./country.entity";
import { User } from "./user.entity";

@Entity("profiles")
export class Profile extends BaseEntity {
  @ApiProperty({
    description: "The unique identifier of the profile",
    example: "1",
  })
  @PrimaryGeneratedColumn("increment")
  id: number;

  @ApiProperty({
    description: "The first name of the user",
    example: "<PERSON>",
    required: false,
  })
  @Column({ nullable: true })
  firstName: string;

  @ApiProperty({
    description: "The last name of the user",
    example: "Doe",
    required: false,
  })
  @Column({ nullable: true })
  lastName: string;

  @ApiProperty({
    description: "URL to the user's profile picture",
    example: "https://courseted.com/images/profile.jpg",
    required: false,
  })
  @Column({ nullable: true })
  profilePicture: string;

  @ApiProperty({
    description: "The country of the user",
    type: () => Country,
    required: false,
  })
  @ManyToOne(() => Country, { nullable: true })
  @JoinColumn({ name: "countryId" })
  @Index("idx_profile_country_id")
  country: Country;

  @ApiProperty({
    description: "The user this profile belongs to",
    type: () => User,
  })
  @OneToOne(() => User, (user) => user.profile, { onDelete: "CASCADE" })
  @JoinColumn()
  @Index("idx_profile_user_id")
  user: User;
}
