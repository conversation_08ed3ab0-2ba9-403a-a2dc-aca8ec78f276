import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { BaseEntity } from '@/common/base/base.entity';

import { User } from './user.entity';

export enum MeetingStatus {
  SCHEDULED = 'scheduled',
  LIVE = 'live',
  ENDED = 'ended',
  CANCELLED = 'cancelled',
}

export enum MeetingType {
  CLASS = 'class',
  WEBINAR = 'webinar',
  OFFICE_HOURS = 'office_hours',
  GROUP_STUDY = 'group_study',
}

@Entity('meetings')
export class Meeting extends BaseEntity {
  @ApiProperty({
    description: 'The unique identifier of the meeting',
    example: '1',
  })
  @PrimaryGeneratedColumn('increment')
  id?: number;

  @ApiProperty({
    description: 'Zoom meeting ID',
    example: '123456789',
  })
  @Index('idx_meeting_zoom_id', { unique: true })
  @Column({ unique: true })
  meetingId: string;

  @ApiProperty({
    description: 'Meeting topic/title',
    example: 'React Advanced Concepts',
  })
  @Column()
  topic: string;

  @ApiProperty({
    description: 'Meeting description',
    example: 'Advanced React concepts including hooks, context, and performance optimization',
    required: false,
  })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({
    description: 'Type of meeting',
    enum: MeetingType,
    example: MeetingType.CLASS,
  })
  @Column({
    type: process.env.NODE_ENV === 'test' ? 'varchar' : 'enum',
    enum: process.env.NODE_ENV === 'test' ? undefined : MeetingType,
    default: MeetingType.CLASS,
    length: process.env.NODE_ENV === 'test' ? 20 : undefined,
  })
  type: MeetingType;

  @ApiProperty({
    description: 'Meeting status',
    enum: MeetingStatus,
    example: MeetingStatus.SCHEDULED,
  })
  @Index('idx_meeting_status')
  @Column({
    type: process.env.NODE_ENV === 'test' ? 'varchar' : 'enum',
    enum: process.env.NODE_ENV === 'test' ? undefined : MeetingStatus,
    default: MeetingStatus.SCHEDULED,
    length: process.env.NODE_ENV === 'test' ? 20 : undefined,
  })
  status: MeetingStatus;

  @ApiProperty({
    description: 'Meeting start time',
    example: '2024-01-15T10:00:00Z',
  })
  @Index('idx_meeting_start_time')
  @Column({ type: 'timestamp' })
  startTime: Date;

  @ApiProperty({
    description: 'Meeting duration in minutes',
    example: 90,
  })
  @Column({ type: 'int' })
  duration: number;

  @ApiProperty({
    description: 'Meeting timezone',
    example: 'America/New_York',
  })
  @Column({ default: 'UTC' })
  timezone: string;

  @ApiProperty({
    description: 'Meeting password',
    example: 'secret123',
    required: false,
  })
  @Column({ nullable: true })
  password?: string;

  @ApiProperty({
    description: 'Zoom join URL',
    example: 'https://zoom.us/j/123456789',
  })
  @Column()
  joinUrl: string;

  @ApiProperty({
    description: 'Zoom start URL for host',
    example: 'https://zoom.us/s/123456789',
  })
  @Column()
  startUrl: string;

  @ApiProperty({
    description: 'Maximum number of participants',
    example: 100,
  })
  @Column({ type: 'int', default: 100 })
  maxParticipants: number;

  @ApiProperty({
    description: 'Whether recording is enabled',
    example: true,
  })
  @Column({ default: false })
  isRecordingEnabled: boolean;

  @ApiProperty({
    description: 'Whether waiting room is enabled',
    example: true,
  })
  @Column({ default: true })
  isWaitingRoomEnabled: boolean;

  @ApiProperty({
    description: 'Whether participants are muted on entry',
    example: true,
  })
  @Column({ default: true })
  isMuteOnEntry: boolean;

  @ApiProperty({
    description: 'Meeting host',
    type: () => User,
  })
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'hostId' })
  @Index('idx_meeting_host_id')
  host: User;

  @ApiProperty({
    description: 'Host ID',
    example: 1,
  })
  @Column()
  hostId: number;

  @ApiProperty({
    description: 'Host email address',
    example: '<EMAIL>',
  })
  @Column()
  hostEmail: string;

  @ApiProperty({
    description: 'Course ID if this meeting is associated with a course',
    example: 1,
    required: false,
  })
  @Column({ nullable: true })
  courseId?: number;

  @OneToMany(() => MeetingParticipant, (participant) => participant.meeting, {
    cascade: true,
  })
  participants?: MeetingParticipant[];

  @OneToMany(() => MeetingRecording, (recording) => recording.meeting, {
    cascade: true,
  })
  recordings?: MeetingRecording[];
}

@Entity('meeting_participants')
export class MeetingParticipant extends BaseEntity {
  @PrimaryGeneratedColumn('increment')
  id?: number;

  @ManyToOne(() => Meeting, (meeting) => meeting.participants, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'meetingId' })
  meeting: Meeting;

  @Column()
  meetingId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: number;

  @Column({ type: 'timestamp', nullable: true })
  joinedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  leftAt?: Date;

  @Column({ default: false })
  isInvited: boolean;

  @Column({ default: 'pending' })
  invitationStatus: 'pending' | 'accepted' | 'declined';
}

@Entity('meeting_recordings')
export class MeetingRecording extends BaseEntity {
  @PrimaryGeneratedColumn('increment')
  id?: number;

  @ManyToOne(() => Meeting, (meeting) => meeting.recordings, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'meetingId' })
  meeting: Meeting;

  @Column()
  meetingId: number;

  @Column()
  recordingId: string;

  @Column()
  fileName: string;

  @Column()
  fileType: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column()
  downloadUrl: string;

  @Column({ nullable: true })
  playUrl?: string;

  @Column({ type: 'int' })
  duration: number;

  @Column({ default: 'cloud' })
  recordingType: 'cloud' | 'local';

  @Column({ default: 'completed' })
  status: 'recording' | 'processing' | 'completed' | 'failed';
}
