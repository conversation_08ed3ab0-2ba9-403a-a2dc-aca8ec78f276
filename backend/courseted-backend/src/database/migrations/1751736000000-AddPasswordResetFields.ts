import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddPasswordResetFields1751736000000 implements MigrationInterface {
  name = "AddPasswordResetFields1751736000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "resetPasswordToken",
        type: "varchar",
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "resetPasswordExpiresAt",
        type: "timestamp",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("users", "resetPasswordExpiresAt");
    await queryRunner.dropColumn("users", "resetPasswordToken");
  }
}
