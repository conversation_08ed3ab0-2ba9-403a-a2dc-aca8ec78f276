import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMeetingTables1704067200000 implements MigrationInterface {
  name = 'CreateMeetingTables1704067200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create meeting type enum
    await queryRunner.query(
      `CREATE TYPE "public"."meetings_type_enum" AS ENUM('class', 'webinar', 'office_hours', 'group_study')`
    );

    // Create meeting status enum
    await queryRunner.query(
      `CREATE TYPE "public"."meetings_status_enum" AS ENUM('scheduled', 'live', 'ended', 'cancelled')`
    );

    // Create meetings table
    await queryRunner.query(
      `CREATE TABLE "meetings" (
        "id" SERIAL NOT NULL,
        "meetingId" character varying NOT NULL,
        "topic" character varying NOT NULL,
        "description" text,
        "type" "public"."meetings_type_enum" NOT NULL DEFAULT 'class',
        "status" "public"."meetings_status_enum" NOT NULL DEFAULT 'scheduled',
        "startTime" TIMESTAMP NOT NULL,
        "duration" integer NOT NULL,
        "timezone" character varying NOT NULL DEFAULT 'UTC',
        "password" character varying,
        "joinUrl" character varying NOT NULL,
        "startUrl" character varying NOT NULL,
        "maxParticipants" integer NOT NULL DEFAULT 100,
        "isRecordingEnabled" boolean NOT NULL DEFAULT false,
        "isWaitingRoomEnabled" boolean NOT NULL DEFAULT true,
        "isMuteOnEntry" boolean NOT NULL DEFAULT true,
        "hostId" integer NOT NULL,
        "hostEmail" character varying NOT NULL,
        "courseId" integer,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_meetings_meetingId" UNIQUE ("meetingId"),
        CONSTRAINT "PK_meetings" PRIMARY KEY ("id")
      )`
    );

    // Create meeting_participants table
    await queryRunner.query(
      `CREATE TABLE "meeting_participants" (
        "id" SERIAL NOT NULL,
        "meetingId" integer NOT NULL,
        "userId" integer NOT NULL,
        "joinedAt" TIMESTAMP,
        "leftAt" TIMESTAMP,
        "isInvited" boolean NOT NULL DEFAULT false,
        "invitationStatus" character varying NOT NULL DEFAULT 'pending',
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_meeting_participants" PRIMARY KEY ("id")
      )`
    );

    // Create meeting_recordings table
    await queryRunner.query(
      `CREATE TABLE "meeting_recordings" (
        "id" SERIAL NOT NULL,
        "meetingId" integer NOT NULL,
        "recordingId" character varying NOT NULL,
        "fileName" character varying NOT NULL,
        "fileType" character varying NOT NULL,
        "fileSize" bigint NOT NULL,
        "downloadUrl" character varying NOT NULL,
        "playUrl" character varying,
        "duration" integer NOT NULL,
        "recordingType" character varying NOT NULL DEFAULT 'cloud',
        "status" character varying NOT NULL DEFAULT 'completed',
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_meeting_recordings" PRIMARY KEY ("id")
      )`
    );

    // Create indexes
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_meeting_zoom_id" ON "meetings" ("meetingId")`
    );
    await queryRunner.query(
      `CREATE INDEX "idx_meeting_status" ON "meetings" ("status")`
    );
    await queryRunner.query(
      `CREATE INDEX "idx_meeting_start_time" ON "meetings" ("startTime")`
    );
    await queryRunner.query(
      `CREATE INDEX "idx_meeting_host_id" ON "meetings" ("hostId")`
    );

    // Create foreign keys
    await queryRunner.query(
      `ALTER TABLE "meetings" ADD CONSTRAINT "FK_meetings_hostId" FOREIGN KEY ("hostId") REFERENCES "users"("id") ON DELETE CASCADE`
    );

    await queryRunner.query(
      `ALTER TABLE "meeting_participants" ADD CONSTRAINT "FK_meeting_participants_meetingId" FOREIGN KEY ("meetingId") REFERENCES "meetings"("id") ON DELETE CASCADE`
    );

    await queryRunner.query(
      `ALTER TABLE "meeting_participants" ADD CONSTRAINT "FK_meeting_participants_userId" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE`
    );

    await queryRunner.query(
      `ALTER TABLE "meeting_recordings" ADD CONSTRAINT "FK_meeting_recordings_meetingId" FOREIGN KEY ("meetingId") REFERENCES "meetings"("id") ON DELETE CASCADE`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys
    await queryRunner.query(`ALTER TABLE "meeting_recordings" DROP CONSTRAINT "FK_meeting_recordings_meetingId"`);
    await queryRunner.query(`ALTER TABLE "meeting_participants" DROP CONSTRAINT "FK_meeting_participants_userId"`);
    await queryRunner.query(`ALTER TABLE "meeting_participants" DROP CONSTRAINT "FK_meeting_participants_meetingId"`);
    await queryRunner.query(`ALTER TABLE "meetings" DROP CONSTRAINT "FK_meetings_hostId"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "meeting_recordings"`);
    await queryRunner.query(`DROP TABLE "meeting_participants"`);
    await queryRunner.query(`DROP TABLE "meetings"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "public"."meetings_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."meetings_type_enum"`);
  }
}
