CREATE TYPE "meetings_type_enum" AS ENUM('class', 'webinar', 'office_hours', 'group_study');
CREATE TYPE "meetings_status_enum" AS ENUM('scheduled', 'live', 'ended', 'cancelled');
CREATE TABLE "meetings" (
	"id" SERIAL NOT NULL,
	"meetingId" character varying NOT NULL,
	"topic" character varying NOT NULL,
	"description" text,
	"type" meetings_type_enum NOT NULL DEFAULT 'class',
	"status" meetings_status_enum NOT NULL DEFAULT 'scheduled',
	"startTime" TIMESTAMP NOT NULL,
	"duration" integer NOT NULL,
	"timezone" character varying NOT NULL DEFAULT 'UTC',
	"password" character varying,
	"joinUrl" character varying NOT NULL,
	"startUrl" character varying NOT NULL,
	"maxParticipants" integer NOT NULL DEFAULT 100,
	"isRecordingEnabled" boolean NOT NULL DEFAULT false,
	"isWaitingRoomEnabled" boolean NOT NULL DEFAULT true,
	"isMuteOnEntry" boolean NOT NULL DEFAULT true,
	"hostId" integer NOT NULL,
	"hostEmail" character varying NOT NULL,
	"courseId" integer,
	"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
	"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
	CONSTRAINT "UQ_meetings_meetingId" UNIQUE ("meetingId"),
	CONSTRAINT "PK_meetings" PRIMARY KEY ("id")
);
CREATE TABLE "meeting_participants" (
	"id" SERIAL NOT NULL,
	"meetingId" integer NOT NULL,
	"userId" integer NOT NULL,
	"joinedAt" TIMESTAMP,
	"leftAt" TIMESTAMP,
	"isInvited" boolean NOT NULL DEFAULT false,
	"invitationStatus" character varying NOT NULL DEFAULT 'pending',
	"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
	"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
	CONSTRAINT "PK_meeting_participants" PRIMARY KEY ("id")
);
CREATE TABLE "meeting_recordings" (
	"id" SERIAL NOT NULL,
	"meetingId" integer NOT NULL,
	"recordingId" character varying NOT NULL,
	"fileName" character varying NOT NULL,
	"fileType" character varying NOT NULL,
	"fileSize" bigint NOT NULL,
	"downloadUrl" character varying NOT NULL,
	"playUrl" character varying,
	"duration" integer NOT NULL,
	"recordingType" character varying NOT NULL DEFAULT 'cloud',
	"status" character varying NOT NULL DEFAULT 'completed',
	"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
	"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
	CONSTRAINT "PK_meeting_recordings" PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX "idx_meeting_zoom_id" ON "meetings" ("meetingId");
CREATE INDEX "idx_meeting_status" ON "meetings" ("status");
CREATE INDEX "idx_meeting_start_time" ON "meetings" ("startTime");
CREATE INDEX "idx_meeting_host_id" ON "meetings" ("hostId");
ALTER TABLE "meetings" ADD CONSTRAINT "FK_meetings_hostId" FOREIGN KEY ("hostId") REFERENCES "users"("id") ON DELETE CASCADE;
ALTER TABLE "meeting_participants" ADD CONSTRAINT "FK_meeting_participants_meetingId" FOREIGN KEY ("meetingId") REFERENCES "meetings"("id") ON DELETE CASCADE;
ALTER TABLE "meeting_participants" ADD CONSTRAINT "FK_meeting_participants_userId" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
ALTER TABLE "meeting_recordings" ADD CONSTRAINT "FK_meeting_recordings_meetingId" FOREIGN KEY ("meetingId") REFERENCES "meetings"("id") ON DELETE CASCADE;