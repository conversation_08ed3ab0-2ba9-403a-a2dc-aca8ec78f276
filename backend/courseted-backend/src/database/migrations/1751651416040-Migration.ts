import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1751651416040 implements MigrationInterface {
  name = "Migration1751651416040";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "countries" ("createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "name" character varying NOT NULL, "code" character varying(2) NOT NULL, "iso3" character varying(3) NOT NULL, "phoneCode" character varying, "flag" character varying, CONSTRAINT "UQ_fa1376321185575cf2226b1491d" UNIQUE ("name"), CONSTRAINT "UQ_b47cbb5311bad9c9ae17b8c1eda" UNIQUE ("code"), CONSTRAINT "UQ_b29f9172f8b660e7834000c4246" UNIQUE ("iso3"), CONSTRAINT "PK_b2d7006793e8697ab3ae2deff18" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_role_enum" AS ENUM('admin', 'student', 'instructor')`,
    );
    await queryRunner.query(
      `CREATE TABLE "users" ("createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "email" character varying NOT NULL, "password" character varying, "isEmailVerified" boolean NOT NULL DEFAULT false, "role" "public"."users_role_enum" NOT NULL DEFAULT 'student', "googleId" character varying, "facebookId" character varying, "linkedinId" character varying, "phoneNumber" character varying NOT NULL, "isPhoneVerified" boolean NOT NULL DEFAULT false, "otpCode" character varying, "otpExpiresAt" TIMESTAMP, "countryId" integer NOT NULL, CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "UQ_1e3d0240b49c40521aaeb953293" UNIQUE ("phoneNumber"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_user_email" ON "users" ("email") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_user_email_verified" ON "users" ("isEmailVerified") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_user_role" ON "users" ("role") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_user_google_id" ON "users" ("googleId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_user_facebook_id" ON "users" ("facebookId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_user_linkedin_id" ON "users" ("linkedinId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_user_country_id" ON "users" ("countryId") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_user_phone" ON "users" ("phoneNumber") `,
    );
    await queryRunner.query(
      `CREATE TABLE "profiles" ("createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "firstName" character varying, "lastName" character varying, "profilePicture" character varying, "countryId" integer, "userId" integer, CONSTRAINT "REL_315ecd98bd1a42dcf2ec4e2e98" UNIQUE ("userId"), CONSTRAINT "PK_8e520eb4da7dc01d0e190447c8e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_profile_country_id" ON "profiles" ("countryId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_profile_user_id" ON "profiles" ("userId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."sms_queued_status_enum" AS ENUM('queued', 'sent', 'failed', 'delivered')`,
    );
    await queryRunner.query(
      `CREATE TABLE "sms_queued" ("createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "to" character varying NOT NULL, "from" character varying NOT NULL, "body" text NOT NULL, "status" "public"."sms_queued_status_enum" NOT NULL DEFAULT 'queued', "twilioSid" character varying, "jobId" character varying, "errorMessage" character varying, "senderId" integer, CONSTRAINT "PK_2b69ea2656b2bf7a1ee62330644" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_sms_status" ON "sms_queued" ("status") `,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_cc0dc7234854a65964f1a268275" FOREIGN KEY ("countryId") REFERENCES "countries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "profiles" ADD CONSTRAINT "FK_41f1fa9217a69dccd306bd3995a" FOREIGN KEY ("countryId") REFERENCES "countries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "profiles" ADD CONSTRAINT "FK_315ecd98bd1a42dcf2ec4e2e985" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sms_queued" ADD CONSTRAINT "FK_f3f350a4c1ac09135120b21a2e0" FOREIGN KEY ("senderId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "sms_queued" DROP CONSTRAINT "FK_f3f350a4c1ac09135120b21a2e0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "profiles" DROP CONSTRAINT "FK_315ecd98bd1a42dcf2ec4e2e985"`,
    );
    await queryRunner.query(
      `ALTER TABLE "profiles" DROP CONSTRAINT "FK_41f1fa9217a69dccd306bd3995a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_cc0dc7234854a65964f1a268275"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_sms_status"`);
    await queryRunner.query(`DROP TABLE "sms_queued"`);
    await queryRunner.query(`DROP TYPE "public"."sms_queued_status_enum"`);
    await queryRunner.query(`DROP INDEX "public"."idx_profile_user_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_profile_country_id"`);
    await queryRunner.query(`DROP TABLE "profiles"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_phone"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_country_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_linkedin_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_facebook_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_google_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_role"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_email_verified"`);
    await queryRunner.query(`DROP INDEX "public"."idx_user_email"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    await queryRunner.query(`DROP TABLE "countries"`);
  }
}
