import { DataSource } from "typeorm";

import { ISeeder } from "../interfaces/seeder.interface";
import { CountrySeeder } from "../seeders/country.seeder";
import { ProfileSeeder } from "../seeders/profile.seeder";
import { SmsSeeder } from "../seeders/sms.seeder";
import { UserSeeder } from "../seeders/user.seeder";

export class SeederRunner {
  private seeders: ISeeder[] = [];
  private seededData: Map<string, any[]> = new Map();

  constructor() {
    // Order matters! Dependencies should be seeded first
    this.seeders = [
      new CountrySeeder(),
      new UserSeeder(),
      new ProfileSeeder(),
      new SmsSeeder(),
    ];
  }

  async runAll(dataSource: DataSource): Promise<void> {
    console.log("Seeding database...");

    for (const seeder of this.seeders) {
      try {
        const existingData = this.buildExistingDataContext();
        const seededEntities = await seeder.run(dataSource, existingData);

        // Store seeded data for future seeders to reference
        const seederName = seeder.getName().toLowerCase();
        this.seededData.set(seederName, seededEntities);
      } catch (error) {
        console.error(`Error seeding ${seeder.getName()}:`, error);
        throw error;
      }
    }

    console.log("Database seeding completed successfully");
  }

  async runSpecific(
    dataSource: DataSource,
    seederNames: string[],
  ): Promise<void> {
    console.log(`Running specific seeders: ${seederNames.join(", ")}`);

    for (const seederName of seederNames) {
      const seeder = this.seeders.find(
        (s) => s.getName().toLowerCase() === seederName.toLowerCase(),
      );

      if (!seeder) {
        console.warn(
          `Seeder "${seederName}" not found. Available seeders: ${this.getAvailableSeederNames().join(", ")}`,
        );
        continue;
      }

      try {
        const existingData = this.buildExistingDataContext();
        const seededEntities = await seeder.run(dataSource, existingData);

        // Store seeded data for future seeders to reference
        this.seededData.set(seederName.toLowerCase(), seededEntities);
      } catch (error) {
        console.error(`Error seeding ${seeder.getName()}:`, error);
        throw error;
      }
    }

    console.log("Specific seeding completed successfully");
  }

  getAvailableSeederNames(): string[] {
    return this.seeders.map((seeder) => seeder.getName());
  }

  private buildExistingDataContext(): any {
    const context: any = {};

    // Convert seeded data to a format that seeders can use
    for (const [key, value] of this.seededData.entries()) {
      context[key] = value;
    }

    return context;
  }
}
