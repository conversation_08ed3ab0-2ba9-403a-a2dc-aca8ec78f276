import { User } from "@/entities";
import { hashPassword } from "@/utils";

import { BaseSeeder } from "../base/base.seeder";
import { USERS_SEED } from "../data/user.seed";

export class UserSeeder extends BaseSeeder<User> {
  protected entityClass = User;
  protected seedData = USERS_SEED;

  getName(): string {
    return "Users";
  }

  protected async processData(
    data: any,
    _existingData?: any,
  ): Promise<Partial<User>> {
    const processedData: Partial<User> = {
      ...data,
      password: data.password ? await hashPassword(data.password) : null,
    };

    // Handle country relationship
    if (data.countryId) {
      processedData.country = { id: data.countryId } as any;
    }

    return processedData;
  }
}
