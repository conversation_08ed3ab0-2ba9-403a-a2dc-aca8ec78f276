import { Profile, User } from "@/entities";

import { BaseSeeder } from "../base/base.seeder";
import { PROFILES_SEED } from "../data/profile.seed";

export class ProfileSeeder extends BaseSeeder<Profile> {
  protected entityClass = Profile;
  protected seedData = PROFILES_SEED;

  getName(): string {
    return "Profiles";
  }

  protected processData(data: any, existingData?: { users: User[] }): any {
    const { userIndex, ...profileInfo } = data;
    return {
      ...profileInfo,
      user: existingData?.users[userIndex],
    };
  }
}
