import { SmsQueued, User } from "@/entities";

import { BaseSeeder } from "../base/base.seeder";
import { SMS_SEED } from "../data/sms.seed";

export class SmsSeeder extends BaseSeeder<SmsQueued> {
  protected entityClass = SmsQueued;
  protected seedData = SMS_SEED;

  getName(): string {
    return "SMS messages";
  }

  protected processData(data: any, existingData?: { users: User[] }): any {
    const { senderId, ...smsInfo } = data;
    return {
      ...smsInfo,
      sender: senderId ? existingData?.users[senderId - 1] : null, // senderId is 1-based, array is 0-based
    };
  }
}
