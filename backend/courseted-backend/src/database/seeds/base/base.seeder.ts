import { DataSource, Repository } from "typeorm";

import { ISeeder } from "../interfaces/seeder.interface";

export abstract class BaseSeeder<T> implements ISeeder {
  protected abstract entityClass: new () => T;
  protected abstract seedData: any[];

  abstract getName(): string;

  protected getRepository(dataSource: DataSource): Repository<T> {
    return dataSource.getRepository(this.entityClass);
  }

  async run(dataSource: DataSource, existingData?: any): Promise<T[]> {
    const repository = this.getRepository(dataSource);
    const entityName = this.getName();

    const existingCount = await repository.count();
    if (existingCount > 0) {
      console.log(
        `${entityName} already exist, skipping ${entityName.toLowerCase()} seeding`,
      );
      return await repository.find();
    }

    const entities: T[] = [];
    for (const data of this.seedData) {
      const processedData = await this.processData(data, existingData);
      const entity = repository.create(processedData);
      const savedEntity = await repository.save(entity);
      entities.push(savedEntity as T);
    }

    console.log(`Seeded ${entities.length} ${entityName.toLowerCase()}`);
    return entities;
  }

  protected processData(data: any, _existingData?: any): any {
    // Override this method in child classes to process data before saving
    return data;
  }
}
