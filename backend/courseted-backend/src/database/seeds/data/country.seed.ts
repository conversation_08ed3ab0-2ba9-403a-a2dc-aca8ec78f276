export const COUNTRIES_SEED = [
  {
    name: "Afghanistan",
    code: "AF",
    iso3: "AFG",
    phoneCode: "+93",
    flag: "🇦🇫",
  },
  {
    name: "Albania",
    code: "AL",
    iso3: "ALB",
    phoneCode: "+355",
    flag: "🇦🇱",
  },
  {
    name: "Algeria",
    code: "DZ",
    iso3: "DZA",
    phoneCode: "+213",
    flag: "🇩🇿",
  },
  {
    name: "Andorra",
    code: "AD",
    iso3: "AND",
    phoneCode: "+376",
    flag: "🇦🇩",
  },
  {
    name: "Angola",
    code: "AO",
    iso3: "AGO",
    phoneCode: "+244",
    flag: "🇦🇴",
  },
  {
    name: "Antigua and Barbuda",
    code: "AG",
    iso3: "ATG",
    phoneCode: "+1",
    flag: "🇦🇬",
  },
  {
    name: "Argentina",
    code: "AR",
    iso3: "ARG",
    phoneCode: "+54",
    flag: "🇦🇷",
  },
  {
    name: "Armenia",
    code: "AM",
    iso3: "ARM",
    phoneCode: "+374",
    flag: "🇦🇲",
  },
  {
    name: "Australia",
    code: "AU",
    iso3: "AUS",
    phoneCode: "+61",
    flag: "🇦🇺",
  },
  {
    name: "Austria",
    code: "AT",
    iso3: "AUT",
    phoneCode: "+43",
    flag: "🇦🇹",
  },
  {
    name: "Azerbaijan",
    code: "AZ",
    iso3: "AZE",
    phoneCode: "+994",
    flag: "🇦🇿",
  },
  {
    name: "Bahamas",
    code: "BS",
    iso3: "BHS",
    phoneCode: "+1",
    flag: "🇧🇸",
  },
  {
    name: "Bahrain",
    code: "BH",
    iso3: "BHR",
    phoneCode: "+973",
    flag: "🇧🇭",
  },
  {
    name: "Bangladesh",
    code: "BD",
    iso3: "BGD",
    phoneCode: "+880",
    flag: "🇧🇩",
  },
  {
    name: "Barbados",
    code: "BB",
    iso3: "BRB",
    phoneCode: "+1",
    flag: "🇧🇧",
  },
  { name: "Belarus", code: "BY", iso3: "BLR", phoneCode: "+375", flag: "🇧🇾" },
  { name: "Belgium", code: "BE", iso3: "BEL", phoneCode: "+32", flag: "🇧🇪" },
  { name: "Belize", code: "BZ", iso3: "BLZ", phoneCode: "+501", flag: "🇧🇿" },
  { name: "Benin", code: "BJ", iso3: "BEN", phoneCode: "+229", flag: "🇧🇯" },
  { name: "Bhutan", code: "BT", iso3: "BTN", phoneCode: "+975", flag: "🇧🇹" },
  { name: "Bolivia", code: "BO", iso3: "BOL", phoneCode: "+591", flag: "🇧🇴" },
  {
    name: "Bosnia and Herzegovina",
    code: "BA",
    iso3: "BIH",
    phoneCode: "+387",
    flag: "🇧🇦",
  },
  { name: "Botswana", code: "BW", iso3: "BWA", phoneCode: "+267", flag: "🇧🇼" },
  { name: "Brazil", code: "BR", iso3: "BRA", phoneCode: "+55", flag: "🇧🇷" },
  { name: "Brunei", code: "BN", iso3: "BRN", phoneCode: "+673", flag: "🇧🇳" },
  { name: "Bulgaria", code: "BG", iso3: "BGR", phoneCode: "+359", flag: "🇧🇬" },
  {
    name: "Burkina Faso",
    code: "BF",
    iso3: "BFA",
    phoneCode: "+226",
    flag: "🇧🇫",
  },
  { name: "Burundi", code: "BI", iso3: "BDI", phoneCode: "+257", flag: "🇧🇮" },
  {
    name: "Cabo Verde",
    code: "CV",
    iso3: "CPV",
    phoneCode: "+238",
    flag: "🇨🇻",
  },
  { name: "Cambodia", code: "KH", iso3: "KHM", phoneCode: "+855", flag: "🇰🇭" },
  { name: "Cameroon", code: "CM", iso3: "CMR", phoneCode: "+237", flag: "🇨🇲" },
  { name: "Canada", code: "CA", iso3: "CAN", phoneCode: "+1", flag: "🇨🇦" },
  {
    name: "Central African Republic",
    code: "CF",
    iso3: "CAF",
    phoneCode: "+236",
    flag: "🇨🇫",
  },
  { name: "Chad", code: "TD", iso3: "TCD", phoneCode: "+235", flag: "🇹🇩" },
  { name: "Chile", code: "CL", iso3: "CHL", phoneCode: "+56", flag: "🇨🇱" },
  { name: "China", code: "CN", iso3: "CHN", phoneCode: "+86", flag: "🇨🇳" },
  { name: "Colombia", code: "CO", iso3: "COL", phoneCode: "+57", flag: "🇨🇴" },
  { name: "Comoros", code: "KM", iso3: "COM", phoneCode: "+269", flag: "🇰🇲" },
  { name: "Congo", code: "CG", iso3: "COG", phoneCode: "+242", flag: "🇨🇬" },
  {
    name: "Costa Rica",
    code: "CR",
    iso3: "CRI",
    phoneCode: "+506",
    flag: "🇨🇷",
  },
  { name: "Croatia", code: "HR", iso3: "HRV", phoneCode: "+385", flag: "🇭🇷" },
  { name: "Cuba", code: "CU", iso3: "CUB", phoneCode: "+53", flag: "🇨🇺" },
  { name: "Cyprus", code: "CY", iso3: "CYP", phoneCode: "+357", flag: "🇨🇾" },
  {
    name: "Czech Republic",
    code: "CZ",
    iso3: "CZE",
    phoneCode: "+420",
    flag: "🇨🇿",
  },
  {
    name: "Democratic Republic of the Congo",
    code: "CD",
    iso3: "COD",
    phoneCode: "+243",
    flag: "🇨🇩",
  },
  { name: "Denmark", code: "DK", iso3: "DNK", phoneCode: "+45", flag: "🇩🇰" },
  { name: "Djibouti", code: "DJ", iso3: "DJI", phoneCode: "+253", flag: "🇩🇯" },
  { name: "Dominica", code: "DM", iso3: "DMA", phoneCode: "+1", flag: "🇩🇲" },
  {
    name: "Dominican Republic",
    code: "DO",
    iso3: "DOM",
    phoneCode: "+1",
    flag: "🇩🇴",
  },
  { name: "Ecuador", code: "EC", iso3: "ECU", phoneCode: "+593", flag: "🇪🇨" },
  { name: "Egypt", code: "EG", iso3: "EGY", phoneCode: "+20", flag: "🇪🇬" },
  {
    name: "El Salvador",
    code: "SV",
    iso3: "SLV",
    phoneCode: "+503",
    flag: "🇸🇻",
  },
  {
    name: "Equatorial Guinea",
    code: "GQ",
    iso3: "GNQ",
    phoneCode: "+240",
    flag: "🇬🇶",
  },
  { name: "Eritrea", code: "ER", iso3: "ERI", phoneCode: "+291", flag: "🇪🇷" },
  { name: "Estonia", code: "EE", iso3: "EST", phoneCode: "+372", flag: "🇪🇪" },
  { name: "Eswatini", code: "SZ", iso3: "SWZ", phoneCode: "+268", flag: "🇸🇿" },
  { name: "Ethiopia", code: "ET", iso3: "ETH", phoneCode: "+251", flag: "🇪🇹" },
  { name: "Fiji", code: "FJ", iso3: "FJI", phoneCode: "+679", flag: "🇫🇯" },
  { name: "Finland", code: "FI", iso3: "FIN", phoneCode: "+358", flag: "🇫🇮" },
  { name: "France", code: "FR", iso3: "FRA", phoneCode: "+33", flag: "🇫🇷" },
  { name: "Gabon", code: "GA", iso3: "GAB", phoneCode: "+241", flag: "🇬🇦" },
  { name: "Gambia", code: "GM", iso3: "GMB", phoneCode: "+220", flag: "🇬🇲" },
  { name: "Georgia", code: "GE", iso3: "GEO", phoneCode: "+995", flag: "🇬🇪" },
  { name: "Germany", code: "DE", iso3: "DEU", phoneCode: "+49", flag: "🇩🇪" },
  { name: "Ghana", code: "GH", iso3: "GHA", phoneCode: "+233", flag: "🇬🇭" },
  { name: "Greece", code: "GR", iso3: "GRC", phoneCode: "+30", flag: "🇬🇷" },
  { name: "Grenada", code: "GD", iso3: "GRD", phoneCode: "+1", flag: "🇬🇩" },
  { name: "Guatemala", code: "GT", iso3: "GTM", phoneCode: "+502", flag: "🇬🇹" },
  { name: "Guinea", code: "GN", iso3: "GIN", phoneCode: "+224", flag: "🇬🇳" },
  {
    name: "Guinea-Bissau",
    code: "GW",
    iso3: "GNB",
    phoneCode: "+245",
    flag: "🇬🇼",
  },
  { name: "Guyana", code: "GY", iso3: "GUY", phoneCode: "+592", flag: "🇬🇾" },
  { name: "Haiti", code: "HT", iso3: "HTI", phoneCode: "+509", flag: "🇭🇹" },
  { name: "Honduras", code: "HN", iso3: "HND", phoneCode: "+504", flag: "🇭🇳" },
  { name: "Hungary", code: "HU", iso3: "HUN", phoneCode: "+36", flag: "🇭🇺" },
  { name: "Iceland", code: "IS", iso3: "ISL", phoneCode: "+354", flag: "🇮🇸" },
  { name: "India", code: "IN", iso3: "IND", phoneCode: "+91", flag: "🇮🇳" },
  { name: "Indonesia", code: "ID", iso3: "IDN", phoneCode: "+62", flag: "🇮🇩" },
  { name: "Iran", code: "IR", iso3: "IRN", phoneCode: "+98", flag: "🇮🇷" },
  { name: "Iraq", code: "IQ", iso3: "IRQ", phoneCode: "+964", flag: "🇮🇶" },
  { name: "Ireland", code: "IE", iso3: "IRL", phoneCode: "+353", flag: "🇮🇪" },
  { name: "Israel", code: "IL", iso3: "ISR", phoneCode: "+972", flag: "🇮🇱" },
  { name: "Italy", code: "IT", iso3: "ITA", phoneCode: "+39", flag: "🇮🇹" },
  {
    name: "Ivory Coast",
    code: "CI",
    iso3: "CIV",
    phoneCode: "+225",
    flag: "🇨🇮",
  },
  { name: "Jamaica", code: "JM", iso3: "JAM", phoneCode: "+1", flag: "🇯🇲" },
  { name: "Japan", code: "JP", iso3: "JPN", phoneCode: "+81", flag: "🇯🇵" },
  { name: "Jordan", code: "JO", iso3: "JOR", phoneCode: "+962", flag: "🇯🇴" },
  { name: "Kazakhstan", code: "KZ", iso3: "KAZ", phoneCode: "+7", flag: "🇰🇿" },
  { name: "Kenya", code: "KE", iso3: "KEN", phoneCode: "+254", flag: "🇰🇪" },
  { name: "Kiribati", code: "KI", iso3: "KIR", phoneCode: "+686", flag: "🇰🇮" },
  { name: "Kuwait", code: "KW", iso3: "KWT", phoneCode: "+965", flag: "🇰🇼" },
  {
    name: "Kyrgyzstan",
    code: "KG",
    iso3: "KGZ",
    phoneCode: "+996",
    flag: "🇰🇬",
  },
  { name: "Laos", code: "LA", iso3: "LAO", phoneCode: "+856", flag: "🇱🇦" },
  { name: "Latvia", code: "LV", iso3: "LVA", phoneCode: "+371", flag: "🇱🇻" },
  { name: "Lebanon", code: "LB", iso3: "LBN", phoneCode: "+961", flag: "🇱🇧" },
  { name: "Lesotho", code: "LS", iso3: "LSO", phoneCode: "+266", flag: "🇱🇸" },
  { name: "Liberia", code: "LR", iso3: "LBR", phoneCode: "+231", flag: "🇱🇷" },
  { name: "Libya", code: "LY", iso3: "LBY", phoneCode: "+218", flag: "🇱🇾" },
  {
    name: "Liechtenstein",
    code: "LI",
    iso3: "LIE",
    phoneCode: "+423",
    flag: "🇱🇮",
  },
  { name: "Lithuania", code: "LT", iso3: "LTU", phoneCode: "+370", flag: "🇱🇹" },
  {
    name: "Luxembourg",
    code: "LU",
    iso3: "LUX",
    phoneCode: "+352",
    flag: "🇱🇺",
  },
  {
    name: "Madagascar",
    code: "MG",
    iso3: "MDG",
    phoneCode: "+261",
    flag: "🇲🇬",
  },
  { name: "Malawi", code: "MW", iso3: "MWI", phoneCode: "+265", flag: "🇲🇼" },
  { name: "Malaysia", code: "MY", iso3: "MYS", phoneCode: "+60", flag: "🇲🇾" },
  { name: "Maldives", code: "MV", iso3: "MDV", phoneCode: "+960", flag: "🇲🇻" },
  { name: "Mali", code: "ML", iso3: "MLI", phoneCode: "+223", flag: "🇲🇱" },
  { name: "Malta", code: "MT", iso3: "MLT", phoneCode: "+356", flag: "🇲🇹" },
  {
    name: "Marshall Islands",
    code: "MH",
    iso3: "MHL",
    phoneCode: "+692",
    flag: "🇲🇭",
  },
  {
    name: "Mauritania",
    code: "MR",
    iso3: "MRT",
    phoneCode: "+222",
    flag: "🇲🇷",
  },
  { name: "Mauritius", code: "MU", iso3: "MUS", phoneCode: "+230", flag: "🇲🇺" },
  { name: "Mexico", code: "MX", iso3: "MEX", phoneCode: "+52", flag: "🇲🇽" },
  {
    name: "Micronesia",
    code: "FM",
    iso3: "FSM",
    phoneCode: "+691",
    flag: "🇫🇲",
  },
  { name: "Moldova", code: "MD", iso3: "MDA", phoneCode: "+373", flag: "🇲🇩" },
  { name: "Monaco", code: "MC", iso3: "MCO", phoneCode: "+377", flag: "🇲🇨" },
  { name: "Mongolia", code: "MN", iso3: "MNG", phoneCode: "+976", flag: "🇲🇳" },
  {
    name: "Montenegro",
    code: "ME",
    iso3: "MNE",
    phoneCode: "+382",
    flag: "🇲🇪",
  },
  { name: "Morocco", code: "MA", iso3: "MAR", phoneCode: "+212", flag: "🇲🇦" },
  {
    name: "Mozambique",
    code: "MZ",
    iso3: "MOZ",
    phoneCode: "+258",
    flag: "🇲🇿",
  },
  { name: "Myanmar", code: "MM", iso3: "MMR", phoneCode: "+95", flag: "🇲🇲" },
  { name: "Namibia", code: "NA", iso3: "NAM", phoneCode: "+264", flag: "🇳🇦" },
  { name: "Nauru", code: "NR", iso3: "NRU", phoneCode: "+674", flag: "🇳🇷" },
  { name: "Nepal", code: "NP", iso3: "NPL", phoneCode: "+977", flag: "🇳🇵" },
  {
    name: "Netherlands",
    code: "NL",
    iso3: "NLD",
    phoneCode: "+31",
    flag: "🇳🇱",
  },
  {
    name: "New Zealand",
    code: "NZ",
    iso3: "NZL",
    phoneCode: "+64",
    flag: "🇳🇿",
  },
  { name: "Nicaragua", code: "NI", iso3: "NIC", phoneCode: "+505", flag: "🇳🇮" },
  { name: "Niger", code: "NE", iso3: "NER", phoneCode: "+227", flag: "🇳🇪" },
  { name: "Nigeria", code: "NG", iso3: "NGA", phoneCode: "+234", flag: "🇳🇬" },
  {
    name: "North Korea",
    code: "KP",
    iso3: "PRK",
    phoneCode: "+850",
    flag: "🇰🇵",
  },
  {
    name: "North Macedonia",
    code: "MK",
    iso3: "MKD",
    phoneCode: "+389",
    flag: "🇲🇰",
  },
  { name: "Norway", code: "NO", iso3: "NOR", phoneCode: "+47", flag: "🇳🇴" },
  { name: "Oman", code: "OM", iso3: "OMN", phoneCode: "+968", flag: "🇴🇲" },
  { name: "Pakistan", code: "PK", iso3: "PAK", phoneCode: "+92", flag: "🇵🇰" },
  { name: "Palau", code: "PW", iso3: "PLW", phoneCode: "+680", flag: "🇵🇼" },
  { name: "Palestine", code: "PS", iso3: "PSE", phoneCode: "+970", flag: "🇵🇸" },
  { name: "Panama", code: "PA", iso3: "PAN", phoneCode: "+507", flag: "🇵🇦" },
  {
    name: "Papua New Guinea",
    code: "PG",
    iso3: "PNG",
    phoneCode: "+675",
    flag: "🇵🇬",
  },
  { name: "Paraguay", code: "PY", iso3: "PRY", phoneCode: "+595", flag: "🇵🇾" },
  { name: "Peru", code: "PE", iso3: "PER", phoneCode: "+51", flag: "🇵🇪" },
  {
    name: "Philippines",
    code: "PH",
    iso3: "PHL",
    phoneCode: "+63",
    flag: "🇵🇭",
  },
  { name: "Poland", code: "PL", iso3: "POL", phoneCode: "+48", flag: "🇵🇱" },
  { name: "Portugal", code: "PT", iso3: "PRT", phoneCode: "+351", flag: "🇵🇹" },
  { name: "Qatar", code: "QA", iso3: "QAT", phoneCode: "+974", flag: "🇶🇦" },
  { name: "Romania", code: "RO", iso3: "ROU", phoneCode: "+40", flag: "🇷🇴" },
  { name: "Russia", code: "RU", iso3: "RUS", phoneCode: "+7", flag: "🇷🇺" },
  { name: "Rwanda", code: "RW", iso3: "RWA", phoneCode: "+250", flag: "🇷🇼" },
  {
    name: "Saint Kitts and Nevis",
    code: "KN",
    iso3: "KNA",
    phoneCode: "+1",
    flag: "🇰🇳",
  },
  { name: "Saint Lucia", code: "LC", iso3: "LCA", phoneCode: "+1", flag: "🇱🇨" },
  {
    name: "Saint Vincent and the Grenadines",
    code: "VC",
    iso3: "VCT",
    phoneCode: "+1",
    flag: "🇻🇨",
  },
  { name: "Samoa", code: "WS", iso3: "WSM", phoneCode: "+685", flag: "🇼🇸" },
  {
    name: "San Marino",
    code: "SM",
    iso3: "SMR",
    phoneCode: "+378",
    flag: "🇸🇲",
  },
  {
    name: "Sao Tome and Principe",
    code: "ST",
    iso3: "STP",
    phoneCode: "+239",
    flag: "🇸🇹",
  },
  {
    name: "Saudi Arabia",
    code: "SA",
    iso3: "SAU",
    phoneCode: "+966",
    flag: "🇸🇦",
  },
  { name: "Senegal", code: "SN", iso3: "SEN", phoneCode: "+221", flag: "🇸🇳" },
  { name: "Serbia", code: "RS", iso3: "SRB", phoneCode: "+381", flag: "🇷🇸" },
  {
    name: "Seychelles",
    code: "SC",
    iso3: "SYC",
    phoneCode: "+248",
    flag: "🇸🇨",
  },
  {
    name: "Sierra Leone",
    code: "SL",
    iso3: "SLE",
    phoneCode: "+232",
    flag: "🇸🇱",
  },
  { name: "Singapore", code: "SG", iso3: "SGP", phoneCode: "+65", flag: "🇸🇬" },
  { name: "Slovakia", code: "SK", iso3: "SVK", phoneCode: "+421", flag: "🇸🇰" },
  { name: "Slovenia", code: "SI", iso3: "SVN", phoneCode: "+386", flag: "🇸🇮" },
  {
    name: "Solomon Islands",
    code: "SB",
    iso3: "SLB",
    phoneCode: "+677",
    flag: "🇸🇧",
  },
  { name: "Somalia", code: "SO", iso3: "SOM", phoneCode: "+252", flag: "🇸🇴" },
  {
    name: "South Africa",
    code: "ZA",
    iso3: "ZAF",
    phoneCode: "+27",
    flag: "🇿🇦",
  },
  {
    name: "South Korea",
    code: "KR",
    iso3: "KOR",
    phoneCode: "+82",
    flag: "🇰🇷",
  },
  {
    name: "South Sudan",
    code: "SS",
    iso3: "SSD",
    phoneCode: "+211",
    flag: "🇸🇸",
  },
  { name: "Spain", code: "ES", iso3: "ESP", phoneCode: "+34", flag: "🇪🇸" },
  { name: "Sri Lanka", code: "LK", iso3: "LKA", phoneCode: "+94", flag: "🇱🇰" },
  { name: "Sudan", code: "SD", iso3: "SDN", phoneCode: "+249", flag: "🇸🇩" },
  { name: "Suriname", code: "SR", iso3: "SUR", phoneCode: "+597", flag: "🇸🇷" },
  { name: "Sweden", code: "SE", iso3: "SWE", phoneCode: "+46", flag: "🇸🇪" },
  {
    name: "Switzerland",
    code: "CH",
    iso3: "CHE",
    phoneCode: "+41",
    flag: "🇨🇭",
  },
  { name: "Syria", code: "SY", iso3: "SYR", phoneCode: "+963", flag: "🇸🇾" },
  { name: "Taiwan", code: "TW", iso3: "TWN", phoneCode: "+886", flag: "🇹🇼" },
  {
    name: "Tajikistan",
    code: "TJ",
    iso3: "TJK",
    phoneCode: "+992",
    flag: "🇹🇯",
  },
  { name: "Tanzania", code: "TZ", iso3: "TZA", phoneCode: "+255", flag: "🇹🇿" },
  { name: "Thailand", code: "TH", iso3: "THA", phoneCode: "+66", flag: "🇹🇭" },
  {
    name: "Timor-Leste",
    code: "TL",
    iso3: "TLS",
    phoneCode: "+670",
    flag: "🇹🇱",
  },
  { name: "Togo", code: "TG", iso3: "TGO", phoneCode: "+228", flag: "🇹🇬" },
  { name: "Tonga", code: "TO", iso3: "TON", phoneCode: "+676", flag: "🇹🇴" },
  {
    name: "Trinidad and Tobago",
    code: "TT",
    iso3: "TTO",
    phoneCode: "+1",
    flag: "🇹🇹",
  },
  { name: "Tunisia", code: "TN", iso3: "TUN", phoneCode: "+216", flag: "🇹🇳" },
  { name: "Turkey", code: "TR", iso3: "TUR", phoneCode: "+90", flag: "🇹🇷" },
  {
    name: "Turkmenistan",
    code: "TM",
    iso3: "TKM",
    phoneCode: "+993",
    flag: "🇹🇲",
  },
  { name: "Tuvalu", code: "TV", iso3: "TUV", phoneCode: "+688", flag: "🇹🇻" },
  { name: "Uganda", code: "UG", iso3: "UGA", phoneCode: "+256", flag: "🇺🇬" },
  { name: "Ukraine", code: "UA", iso3: "UKR", phoneCode: "+380", flag: "🇺🇦" },
  {
    name: "United Arab Emirates",
    code: "AE",
    iso3: "ARE",
    phoneCode: "+971",
    flag: "🇦🇪",
  },
  {
    name: "United Kingdom",
    code: "GB",
    iso3: "GBR",
    phoneCode: "+44",
    flag: "🇬🇧",
  },
  {
    name: "United States",
    code: "US",
    iso3: "USA",
    phoneCode: "+1",
    flag: "🇺🇸",
  },
  { name: "Uruguay", code: "UY", iso3: "URY", phoneCode: "+598", flag: "🇺🇾" },
  {
    name: "Uzbekistan",
    code: "UZ",
    iso3: "UZB",
    phoneCode: "+998",
    flag: "🇺🇿",
  },
  { name: "Vanuatu", code: "VU", iso3: "VUT", phoneCode: "+678", flag: "🇻🇺" },
  {
    name: "Vatican City",
    code: "VA",
    iso3: "VAT",
    phoneCode: "+39",
    flag: "🇻🇦",
  },
  { name: "Venezuela", code: "VE", iso3: "VEN", phoneCode: "+58", flag: "🇻🇪" },
  { name: "Vietnam", code: "VN", iso3: "VNM", phoneCode: "+84", flag: "🇻🇳" },
  { name: "Yemen", code: "YE", iso3: "YEM", phoneCode: "+967", flag: "🇾🇪" },
  { name: "Zambia", code: "ZM", iso3: "ZMB", phoneCode: "+260", flag: "🇿🇲" },
  { name: "Zimbabwe", code: "ZW", iso3: "ZWE", phoneCode: "+263", flag: "🇿🇼" },
].map((country, idx) => ({ id: idx + 1, ...country }));
