import { SmsStatus } from "@/entities/sms.entity";

export const SMS_SEED = [
  {
    to: "+***********",
    from: "+***********",
    body: "Welcome to Courseted! Your account has been created.",
    status: SmsStatus.SENT,
    twilioSid: "SM123456789abcdef",
    senderId: 1, // Admin user (index 0)
  },
  {
    to: "+***********",
    from: "+***********",
    body: "Your course enrollment has been confirmed.",
    status: SmsStatus.SENT,
    twilioSid: "SM123456789abcdef2",
    senderId: 3, // Student user (index 2)
  },
  {
    to: "+***********",
    from: "+***********",
    body: "Your instructor account has been approved.",
    status: SmsStatus.SENT,
    twilioSid: "SM123456789abcdef3",
    senderId: 2, // Instructor user (index 1)
  },
];
