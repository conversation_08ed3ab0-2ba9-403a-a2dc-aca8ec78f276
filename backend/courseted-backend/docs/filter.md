# Filter System Usage Guide

This guide shows how to use the filtering system that includes both the `@Swagger` decorator with filter options and standalone filter decorators for Swagger documentation.

## Available Filter Methods

### 1. Using `@Swagger` Decorator with Filter Options (Recommended)

The `@Swagger` decorator supports inline filter configuration:

```typescript
@Swagger({
  summary: "Get all users",
  description: "Retrieve users with filtering capabilities", 
  tags: ["Users"],
  filter: {
    searchDescription: "Search users by email or phone number",
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  }
})
```

### 2. Standalone Filter Decorators

#### `@ApiFilter(options)`
Generic filter decorator with customizable options.

```typescript
@ApiFilter({
  searchDescription: "Custom search description",
  sortableFields: ["name", "email", "createdAt"],
  maxLimit: 50,
  defaultLimit: 20,
})
```

#### `@<PERSON>piU<PERSON>Filter()`
Predefined filter for user endpoints with search on email/phone and sorting by user fields.

#### `@ApiInstructorFilter()`
Predefined filter for instructor endpoints.

#### `@ApiStudentFilter()`
Predefined filter for student endpoints.

#### `@ApiPagination(options)`
Simple pagination decorator without search functionality.

## Usage Examples

### Method 1: Using @Swagger Decorator with Inline Filter (Recommended)

```typescript
@Swagger({
  summary: "Get all users",
  description: "Retrieve users with filtering capabilities",
  tags: ["Users"],
  authentication: true,
  filter: {
    searchDescription: "Search users by email or phone number", 
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  }
})
@Get()
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

### Method 2: Using Standalone Filter Decorators

```typescript
// Import the decorators
import { ApiUserFilter } from '@common/decorators/filter.decorator';

@Swagger({
  summary: "Get all users",
  description: "Retrieve users with filtering capabilities", 
  tags: ["Users"],
  authentication: true,
})
@ApiUserFilter()
@Get()
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

### Method 3: Using Basic Filter (Boolean)

```typescript
@Swagger({
  summary: "Get all items",
  description: "Retrieve items with basic filtering",
  tags: ["Items"],
  filter: true // Uses default filter configuration
})
@Get() 
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

## Before vs After Comparison

### Before (Manual ApiQuery decorators):
```typescript
@ApiQuery({
  name: "search",
  required: false,
  description: "Search users by email or phone number",
})
@ApiQuery({
  name: "page", 
  required: false,
  description: "Page number (default: 1)",
})
@ApiQuery({
  name: "limit",
  required: false,
  description: "Items per page (default: 10, max: 100)",
})
@ApiQuery({
  name: "sortBy",
  required: false,
  description: "Sort by field (email, phoneNumber, createdAt, role, etc.)",
})
@ApiQuery({
  name: "sortOrder",
  required: false,
  description: "Sort order (asc or desc)",
})
@Get()
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

### After (Using Filter Options):
```typescript
@Swagger({
  summary: "Get all users",
  description: "Retrieve users with filtering capabilities",
  tags: ["Users"],
  filter: {
    searchDescription: "Search users by email or phone number",
    sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
    maxLimit: 100,
    defaultLimit: 10,
  }
})
@Get()
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

## Custom Filter Examples

### Products Endpoint
```typescript
@Swagger({
  summary: "Get all products",
  description: "Retrieve products with custom filtering options",
  tags: ["Products"],
  filter: {
    searchDescription: "Search products by name, description, or SKU",
    sortableFields: ["name", "price", "category", "createdAt"],
    maxLimit: 200,
    defaultLimit: 25,
  }
})
@Get("products")
async findProducts(@Query() filters: BaseFilterDto) {
  return this.productsService.findAllWithFilters(filters);
}
```

### Using Predefined Configurations
```typescript
// Import predefined filter configs
import { FilterConfigs } from '@common/decorators/api-operation.decorator';

@Swagger({
  summary: "Get all users",
  description: "Retrieve users with predefined user filter",
  tags: ["Users"],
  filter: FilterConfigs.users
})
@Get()
async findUsers(@Query() filters: BaseFilterDto) {
  return this.usersService.findAllWithFilters(filters);
}
```

### Simple Pagination Only
```typescript
@Swagger({
  summary: "Get categories",
  description: "Retrieve categories with pagination only",
  tags: ["Categories"],
})
@ApiPagination({ maxLimit: 50, defaultLimit: 15 })
@Get("categories")
async findCategories(@Query() pagination: PaginationDto) {
  return this.categoriesService.findAll(pagination);
}
```

## Available Query Parameters

The filter system automatically generates the following query parameters:

### Search & Pagination
- `search` (string, optional): Search term for filtering results
- `page` (number, optional): Page number (1-based, default: 1)
- `limit` (number, optional): Items per page (default: 10, max: configurable)
- `size` (number, optional): Alias for limit parameter

### Sorting
- `sortBy` (string, optional): Field to sort by (from allowed sortable fields)
- `sortOrder` (enum, optional): Sort order (`asc` or `desc`, default: `desc`)

## Filter Configuration Options

```typescript
interface FilterDecoratorOptions {
  searchDescription?: string;        // Description for search parameter
  sortableFields?: string[];         // Array of allowed sort fields
  maxLimit?: number;                // Maximum items per page
  defaultLimit?: number;            // Default items per page
}
```

## Predefined Filter Configurations

The system includes several predefined configurations:

```typescript
// Available in FilterConfigs object
FilterConfigs.users = {
  searchDescription: "Search users by email or phone number",
  sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id", "role"],
  maxLimit: 100,
  defaultLimit: 10,
}

FilterConfigs.instructors = {
  searchDescription: "Search instructors by email or phone number", 
  sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
  maxLimit: 100,
  defaultLimit: 10,
}

FilterConfigs.students = {
  searchDescription: "Search students by email or phone number",
  sortableFields: ["email", "phoneNumber", "createdAt", "updatedAt", "id"],
  maxLimit: 100,
  defaultLimit: 10,
}

FilterConfigs.basic = {
  searchDescription: "Search term for filtering results",
  sortableFields: ["createdAt", "updatedAt", "id"],
  maxLimit: 100,
  defaultLimit: 10,
}
```

## Benefits

1. **DRY Principle**: Reduces code duplication across endpoints
2. **Consistency**: Ensures all filter endpoints have the same parameter structure
3. **Maintainability**: Easy to update filter documentation in one place
4. **Type Safety**: Built-in TypeScript support with proper interfaces
5. **Customizable**: Flexible options for different use cases
6. **Multiple Approaches**: Choose between inline config or standalone decorators
7. **Predefined Configs**: Ready-to-use configurations for common scenarios

## Usage Recommendations

### When to use each approach:

1. **@Swagger with inline filter config** (Recommended):
   - When you need complete control over the endpoint documentation
   - When filter config is specific to that endpoint
   - For new endpoints being developed

2. **Standalone filter decorators**:
   - When you want to separate concerns
   - When multiple endpoints share the same filter configuration
   - For quick implementation without customization

3. **Predefined filter configs**:
   - For standard entity types (users, instructors, students)
   - When you want consistency across similar endpoints
   - For rapid development

### ⚠️ Important Note: Avoid Redundancy
**Do not use both approaches together** - choose either the `@Swagger` decorator with filter options OR the standalone filter decorators, but not both. Using both creates duplicate query parameters in your Swagger documentation.

```typescript
// ❌ WRONG - This creates duplicate parameters
@Swagger({
  // ... other options
  filter: { /* config */ }
})
@ApiUserFilter()  // Don't use both!
@Get()

// ✅ CORRECT - Choose one approach
@Swagger({
  // ... other options  
  filter: { /* config */ }
})
@Get()

// OR

@Swagger({
  // ... other options
  // No filter option
})
@ApiUserFilter()
@Get()
```

## Implementation Steps

1. **Import required decorators and types**:
```typescript
import { Swagger } from '@common/decorators';
import { BaseFilterDto } from '@common/filter';
```

2. **Choose your approach** and apply the appropriate decorator

3. **Use BaseFilterDto** for query parameter validation:
```typescript
async findAll(@Query() filters: BaseFilterDto) {
  // filters.search, filters.page, filters.limit, etc. are now available
  return this.service.findAllWithFilters(filters);
}
```

4. **Implement filtering in your service**:
```typescript
async findAllWithFilters(filters: BaseFilterDto) {
  return this.filterService.applyFilters(
    this.repository.createQueryBuilder(),
    filters,
    {
      searchFields: ['email', 'phoneNumber'],
      defaultSortField: 'createdAt',
      allowedSortFields: ['email', 'phoneNumber', 'createdAt', 'updatedAt'],
    }
  );
}
```

## Generated Swagger Documentation

The filter system automatically generates complete Swagger documentation including:
- Parameter descriptions with examples
- Data types and validation constraints
- Enum values for sortOrder (asc/desc)
- Min/max values for pagination parameters
- Clear field descriptions for search functionality

This ensures your API documentation is always complete and consistent across all filtered endpoints, making it easier for frontend developers and API consumers to understand and use your endpoints effectively.

## Real-World Examples

### Complete User Controller Example
```typescript
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { Swagger, Roles } from '@common/decorators';
import { BaseFilterDto } from '@common/filter';
import { JwtAuthGuard } from '@common/guards';
import { UserRole } from '@entities';

@Controller({
  path: 'users',
  version: '1',
})
@UseGuards(JwtAuthGuard)
export class UsersController {
  
  @Swagger({
    summary: 'Get all users with filtering',
    description: 'Retrieve users with search, pagination, and sorting capabilities',
    tags: ['Users'],
    authentication: true,
    filter: {
      searchDescription: 'Search users by email or phone number',
      sortableFields: ['email', 'phoneNumber', 'createdAt', 'updatedAt', 'id', 'role'],
      maxLimit: 100,
      defaultLimit: 10,
    }
  })
  @Get()
  @Roles(UserRole.ADMIN)
  async findAll(@Query() filters: BaseFilterDto) {
    return await this.usersService.findAllWithFilters(filters);
  }
}
```

### Service Implementation Example
```typescript
import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseFilterDto } from '@common/filter';
import { FilterService } from '@common/filter';
import { User } from '@entities';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private filterService: FilterService,
  ) {}

  async findAllWithFilters(filters: BaseFilterDto) {
    const queryBuilder = this.userRepository.createQueryBuilder('user');
    
    return this.filterService.applyFilters(
      queryBuilder,
      filters,
      {
        searchFields: ['email', 'phoneNumber'],
        defaultSortField: 'createdAt',
        allowedSortFields: ['email', 'phoneNumber', 'createdAt', 'updatedAt', 'id', 'role'],
      }
    );
  }
}
```

## Troubleshooting

### Common Issues

1. **Duplicate query parameters in Swagger**
   - **Cause**: Using both `@Swagger` with filter option AND standalone filter decorators
   - **Solution**: Choose one approach only

2. **Filter decorators not found**
   - **Cause**: Missing import from decorators index
   - **Solution**: Add exports to `src/common/decorators/index.ts`

3. **BaseFilterDto not working**
   - **Cause**: Missing import or wrong import path
   - **Solution**: Import from `@common/filter` or adjust path

4. **Search not working**
   - **Cause**: Search fields not configured in service
   - **Solution**: Add `searchFields` option in `applyFilters` call

### Best Practices

1. **Always use BaseFilterDto** for query parameter validation
2. **Configure search fields** in your service implementation
3. **Set appropriate limits** to prevent performance issues
4. **Use descriptive search descriptions** for better API documentation
5. **Test your filter endpoints** to ensure they work as expected

## Migration Guide

If you're migrating from manual `@ApiQuery` decorators:

1. **Identify endpoints** that use manual query parameter decorators
2. **Choose your approach** - inline filter config or standalone decorators
3. **Replace manual decorators** with the chosen approach
4. **Update service methods** to use `FilterService.applyFilters()`
5. **Test the endpoints** to ensure functionality is preserved
6. **Update documentation** if needed

This systematic approach ensures a smooth transition to the new filter system while maintaining API compatibility.
