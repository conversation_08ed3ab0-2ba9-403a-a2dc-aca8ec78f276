# Zoom Integration Backend API

## Overview

This backend provides a comprehensive Zoom integration API that allows administrators and instructors to create and manage meetings, while students can join these meetings. The integration uses Zoom's REST API for meeting management and provides SDK signatures for client-side integration.

## Features

### Meeting Management
- ✅ Create scheduled meetings/classes
- ✅ Update meeting details and settings
- ✅ Delete meetings
- ✅ List meetings with filters and pagination
- ✅ Get upcoming meetings
- ✅ Invite participants to meetings

### Zoom SDK Integration
- ✅ Generate SDK signatures for client-side integration
- ✅ Support for both Meeting SDK and Video SDK
- ✅ Secure signature generation with role-based access

### Recording Management
- ✅ Get meeting recordings
- ✅ Support for cloud and local recordings
- ✅ Recording metadata storage

### Webhook Support
- ✅ Handle Zoom webhook events
- ✅ Real-time meeting status updates
- ✅ Participant tracking
- ✅ Recording completion notifications

## API Endpoints

### Meeting Management

#### Create Meeting
```http
POST /api/meetings
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "topic": "React Advanced Concepts",
  "description": "Advanced React concepts including hooks, context, and performance optimization",
  "type": "class",
  "startTime": "2024-01-15T10:00:00Z",
  "duration": 90,
  "timezone": "America/New_York",
  "password": "secret123",
  "maxParticipants": 100,
  "isRecordingEnabled": true,
  "isWaitingRoomEnabled": true,
  "isMuteOnEntry": true,
  "courseId": 1,
  "invitedUserIds": [1, 2, 3]
}
```

#### Get All Meetings
```http
GET /api/meetings?page=1&limit=10&status=scheduled&type=class
Authorization: Bearer <jwt_token>
```

#### Get Meeting by ID
```http
GET /api/meetings/:id
Authorization: Bearer <jwt_token>
```

#### Update Meeting
```http
PUT /api/meetings/:id
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "topic": "Updated Meeting Topic",
  "duration": 120
}
```

#### Delete Meeting
```http
DELETE /api/meetings/:id
Authorization: Bearer <jwt_token>
```

#### Get Upcoming Meetings
```http
GET /api/meetings/upcoming
Authorization: Bearer <jwt_token>
```

### SDK Integration

#### Get Meeting Signature
```http
GET /api/meetings/signature/:id?role=0
Authorization: Bearer <jwt_token>
```

Response:
```json
{
  "signature": "base64_encoded_signature"
}
```

### Join Meeting

#### Join Meeting
```http
POST /api/meetings/join
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "meetingId": "*********",
  "password": "secret123",
  "userName": "John Doe",
  "userEmail": "<EMAIL>"
}
```

### Recording Management

#### Get Meeting Recordings
```http
GET /api/meetings/:id/recordings
Authorization: Bearer <jwt_token>
```

### Participant Management

#### Invite Participants
```http
POST /api/meetings/:id/invite
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "userIds": [1, 2, 3]
}
```

### Webhooks

#### Zoom Webhook Endpoint
```http
POST /api/webhooks/zoom
Content-Type: application/json
Authorization: <webhook_signature>

{
  "event": "meeting.started",
  "payload": {
    "account_id": "account_id",
    "object": {
      "uuid": "meeting_uuid",
      "id": *********,
      "host_id": "host_id",
      "topic": "Meeting Topic",
      "type": 2,
      "start_time": "2024-01-15T10:00:00Z",
      "duration": 90,
      "timezone": "UTC",
      "join_url": "https://zoom.us/j/*********"
    }
  },
  "event_ts": *************
}
```

## Database Schema

### Meetings Table
```sql
CREATE TABLE meetings (
  id SERIAL PRIMARY KEY,
  meetingId VARCHAR UNIQUE NOT NULL,
  topic VARCHAR NOT NULL,
  description TEXT,
  type ENUM('class', 'webinar', 'office_hours', 'group_study') DEFAULT 'class',
  status ENUM('scheduled', 'live', 'ended', 'cancelled') DEFAULT 'scheduled',
  startTime TIMESTAMP NOT NULL,
  duration INTEGER NOT NULL,
  timezone VARCHAR DEFAULT 'UTC',
  password VARCHAR,
  joinUrl VARCHAR NOT NULL,
  startUrl VARCHAR NOT NULL,
  maxParticipants INTEGER DEFAULT 100,
  isRecordingEnabled BOOLEAN DEFAULT FALSE,
  isWaitingRoomEnabled BOOLEAN DEFAULT TRUE,
  isMuteOnEntry BOOLEAN DEFAULT TRUE,
  hostId INTEGER NOT NULL,
  hostEmail VARCHAR NOT NULL,
  courseId INTEGER,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (hostId) REFERENCES users(id) ON DELETE CASCADE
);
```

### Meeting Participants Table
```sql
CREATE TABLE meeting_participants (
  id SERIAL PRIMARY KEY,
  meetingId INTEGER NOT NULL,
  userId INTEGER NOT NULL,
  joinedAt TIMESTAMP,
  leftAt TIMESTAMP,
  isInvited BOOLEAN DEFAULT FALSE,
  invitationStatus ENUM('pending', 'accepted', 'declined') DEFAULT 'pending',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (meetingId) REFERENCES meetings(id) ON DELETE CASCADE,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);
```

### Meeting Recordings Table
```sql
CREATE TABLE meeting_recordings (
  id SERIAL PRIMARY KEY,
  meetingId INTEGER NOT NULL,
  recordingId VARCHAR NOT NULL,
  fileName VARCHAR NOT NULL,
  fileType VARCHAR NOT NULL,
  fileSize BIGINT NOT NULL,
  downloadUrl VARCHAR NOT NULL,
  playUrl VARCHAR,
  duration INTEGER NOT NULL,
  recordingType ENUM('cloud', 'local') DEFAULT 'cloud',
  status ENUM('recording', 'processing', 'completed', 'failed') DEFAULT 'completed',
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (meetingId) REFERENCES meetings(id) ON DELETE CASCADE
);
```

## Environment Configuration

Add these environment variables to your `.env` file:

```env
# Zoom API Configuration
ZOOM_API_KEY=your_zoom_api_key_here
ZOOM_API_SECRET=your_zoom_api_secret_here
ZOOM_SDK_KEY=your_zoom_sdk_key_here
ZOOM_SDK_SECRET=your_zoom_sdk_secret_here
ZOOM_WEBHOOK_SECRET=your_zoom_webhook_secret_here
```

## Setup Instructions

### 1. Zoom App Configuration

1. Create a Zoom App at [Zoom Marketplace](https://marketplace.zoom.us/)
2. Choose "Server-to-Server OAuth" for API access
3. Get your API Key and Secret
4. Configure webhook endpoints in your Zoom app settings

### 2. Database Migration

Run the database migration to create the meeting tables:

```bash
npm run migration:run
```

### 3. Environment Variables

Configure your environment variables as shown above.

### 4. Webhook Configuration

Configure your Zoom app to send webhooks to:
```
https://your-domain.com/api/webhooks/zoom
```

Enable these webhook events:
- meeting.started
- meeting.ended
- meeting.participant_joined
- meeting.participant_left
- recording.completed

## Role-Based Access Control

### Admin
- Can create, read, update, and delete any meeting
- Can view all meetings across the platform
- Can invite any user to meetings

### Instructor
- Can create, read, update, and delete their own meetings
- Can view only their own meetings
- Can invite students to their meetings

### Student
- Can only view meetings they're invited to
- Can join meetings they have access to
- Cannot create or manage meetings

## Error Handling

The API provides comprehensive error handling with appropriate HTTP status codes:

- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Invalid or missing authentication
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

## Security Considerations

1. **JWT Authentication**: All endpoints require valid JWT tokens
2. **Role-based Access**: Proper role checking for all operations
3. **Webhook Verification**: Webhook signatures are verified for security
4. **Input Validation**: All input data is validated using DTOs
5. **SQL Injection Protection**: TypeORM provides protection against SQL injection

## Testing

Run the test suite:

```bash
npm run test
npm run test:e2e
```

## Monitoring and Logging

The integration includes comprehensive logging for:
- Meeting creation and management
- Zoom API interactions
- Webhook events
- Error tracking

Monitor logs for:
- Failed Zoom API calls
- Webhook processing errors
- Authentication failures
- Database errors
