# Mail System Documentation

A Laravel-inspired, production-ready mail system for NestJS with support for multiple drivers, queuing, and comprehensive testing.

## Features

- **Multiple Mail Drivers**: Support for AWS SES, SMTP, and LOG (for development)
- **Queue Support**: Asynchronous email delivery using BullMQ and Redis
- **Template Engine**: Beautiful emails using Pug templates
- **Testing Support**: Mock mail service for testing with Laravel-style assertions
- **Type Safety**: Full TypeScript support with proper typing
- **Flexible Recipients**: Support for to, cc, bcc, and multiple recipients

## Installation

The required dependencies are already installed:

```bash
npm install @nestjs-modules/mailer nodemailer pug @aws-sdk/client-ses ioredis @types/nodemailer
```

## Configuration

Add the following environment variables to your `.env` file:

```env
# Mail Configuration
MAIL_DRIVER=log                              # ses, smtp, log
MAIL_FROM_ADDRESS=<EMAIL>      # Default from address
MAIL_FROM_NAME=Courseted                     # Default from name

# SMTP Configuration (when MAIL_DRIVER=smtp)
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=587
MAIL_USER=your-smtp-user
MAIL_PASS=your-smtp-password

# AWS SES Configuration (when MAIL_DRIVER=ses)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Redis Configuration (for queuing)
REDIS_HOST=localhost
REDIS_PORT=6379
MAIL_QUEUE_NAME=mail
```

## Usage

### Basic Email Sending

```typescript
import { MailService, WelcomeUserMailable } from '@/modules/mail';

@Injectable()
export class UserService {
  constructor(private readonly mailService: MailService) {}

  async registerUser(userData: { email: string; name?: string }) {
    const user = { id: 1, ...userData };
    
    // Send email immediately
    await this.mailService
      .to(user.email)
      .send(new WelcomeUserMailable({ user }));
    
    // Or queue for background processing
    await this.mailService
      .to(user.email)
      .queue(new WelcomeUserMailable({ user }));
  }
}
```

### Multiple Recipients and CC/BCC

```typescript
// Send to multiple recipients
await this.mailService
  .to(['<EMAIL>', '<EMAIL>'])
  .cc('<EMAIL>')
  .bcc(['<EMAIL>', '<EMAIL>'])
  .queue(new WelcomeUserMailable({ user }));
```

### Creating Custom Mailables

```typescript
import { Mailable, MailBuilder, RegisterMailable } from '@/modules/mail';

@RegisterMailable()
export class OrderConfirmationMailable extends Mailable<{ order: Order; customer: User }> {
  configure(builder: MailBuilder): MailBuilder {
    const { order, customer } = this.payload;

    return builder
      .subject(`Order Confirmation #${order.id}`)
      .template('./order-confirmation')
      .context({
        order,
        customer,
        year: new Date().getFullYear(),
      })
      .attach({
        filename: 'invoice.pdf',
        content: order.invoicePdf,
        contentType: 'application/pdf',
      });
  }

  // Optional: Add business logic
  shouldSend(): boolean {
    return this.payload.order.status === 'confirmed';
  }
}
```

### Testing

```typescript
import { Test } from '@nestjs/testing';
import { MailService, MockMailService, WelcomeUserMailable } from '@/modules/mail';

describe('UserService', () => {
  let userService: UserService;
  let mailService: MockMailService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: MailService,
          useClass: MockMailService,
        },
      ],
    }).compile();

    userService = module.get<UserService>(UserService);
    mailService = module.get<MailService>(MailService) as unknown as MockMailService;
  });

  it('should send welcome email on registration', async () => {
    const user = { email: '<EMAIL>', name: 'John Doe' };
    
    await userService.registerUser(user);

    // Laravel-style assertions
    mailService.assertSent(WelcomeUserMailable, 1);
    mailService.assertSentTo(WelcomeUserMailable, '<EMAIL>');
    
    // Get sent mails for detailed inspection
    const sentMails = mailService.getSentMailOfType(WelcomeUserMailable);
    expect(sentMails[0].mailable.payload.user.name).toBe('John Doe');
  });
});
```

## Mail Drivers

### LOG Driver (Development)
- Logs email content to console
- Optionally opens emails in browser (when preview: true)
- Perfect for local development

### SMTP Driver
- Standard SMTP support
- Works with services like Mailtrap, Mailgun, etc.
- Configurable authentication and encryption

### SES Driver (Production)
- AWS Simple Email Service integration
- Scalable and reliable for production
- Built-in bounce and complaint handling

## Queue Management

### Queue Statistics
```typescript
const stats = await this.mailService.getQueueStats();
// Returns: { waiting: 5, active: 2, completed: 100, failed: 1 }
```

### Clear Queue
```typescript
await this.mailService.clearQueue();
```

### Delayed Sending
```typescript
// Send email in 1 hour (3600000 ms)
await this.mailService
  .to(user.email)
  .later(new WelcomeUserMailable({ user }), 3600000);
```

## Template Development

Templates are located in `src/templates/` and use Pug syntax:

```pug
// welcome-user.pug
doctype html
html
  head
    title Welcome!
  body
    h1 Welcome #{user.name}!
    p Thank you for joining Courseted.
    a(href=loginUrl) Get Started
```

### Template Context
All templates have access to:
- Variables passed via `.context()`
- `year` (current year)
- Any global variables you define

## Error Handling

- Failed jobs are automatically retried (3 attempts by default)
- Exponential backoff between retries
- Comprehensive logging for debugging
- Queue monitoring and statistics

## Production Considerations

1. **Redis Configuration**: Ensure Redis is properly configured for production
2. **SES Setup**: Configure proper IAM roles and verified domains
3. **Queue Workers**: Run queue workers as separate processes
4. **Monitoring**: Monitor queue statistics and failure rates
5. **Templates**: Optimize templates for various email clients

## Available Mock Assertions

- `assertSent(MailableClass, count?)` - Assert emails were sent
- `assertQueued(MailableClass, count?)` - Assert emails were queued
- `assertSentTo(MailableClass, recipient)` - Assert email sent to specific recipient
- `assertQueuedTo(MailableClass, recipient)` - Assert email queued for specific recipient
- `assertNothingSent()` - Assert no emails were sent
- `assertNothingQueued()` - Assert no emails were queued
- `getSentMail()` - Get all sent emails
- `getQueuedMail()` - Get all queued emails
- `getSentMailOfType(MailableClass)` - Get sent emails of specific type
- `getQueuedMailOfType(MailableClass)` - Get queued emails of specific type

## API Endpoints

The mail system includes example endpoints:

- `POST /mail/welcome` - Send welcome email (queued)
- `POST /mail/password-reset` - Send password reset (immediate)
- `POST /mail/broadcast` - Send to multiple recipients
- `POST /mail/queue-stats` - Get queue statistics

## Architecture

This implementation follows Laravel's mail architecture:

1. **Mailable Classes** - Encapsulate email logic (Laravel's `app/Mail`)
2. **Mail Service** - Central API (Laravel's `Mail` facade)
3. **Queue Integration** - Background processing (Laravel's queue system)
4. **Template Engine** - Dynamic content (Laravel's Blade templates)
5. **Mock Testing** - Laravel-style `Mail::fake()` functionality

## Contributing

When adding new mailable classes:

1. Create the mailable class extending `Mailable`
2. Add the `@RegisterMailable()` decorator
3. Create corresponding Pug template
4. Add tests using `MockMailService`
5. Export from the index file
