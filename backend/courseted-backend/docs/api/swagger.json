{"openapi": "3.0.0", "paths": {"/": {"get": {"description": "Returns basic application information and health status", "operationId": "AppController_getApplicationInfo", "parameters": [], "responses": {"200": {"description": "Application information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Application is running successfully"}, "data": {"type": "object", "properties": {"name": {"type": "string", "example": "Courseted Backend API"}, "version": {"type": "string", "example": "1.0.0"}, "environment": {"type": "string", "example": "development"}, "timestamp": {"type": "string", "example": "2025-07-16T15:21:23.000Z"}}}}}}}}}, "summary": "Get application information", "tags": ["Application"]}}, "/v1": {"get": {"description": "Returns basic application information and health status for v1 API", "operationId": "AppController_getApplicationInfoV1", "parameters": [], "responses": {"200": {"description": "Application information retrieved successfully"}}, "summary": "Get application information (v1)", "tags": ["Application"]}}, "/v1/users": {"get": {"description": "Get all users with search, pagination, and sorting capabilities", "operationId": "getUsers", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search users by email or phone number", "schema": {"example": "john", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "size", "required": false, "in": "query", "description": "Alias for limit - items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field (email, phoneNumber, createdAt, updatedAt, id, role)", "schema": {"example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get All Users with Filtering", "tags": ["Users", "Users"]}}, "/v1/users/instructors": {"get": {"description": "Get instructors with search, pagination, and sorting", "operationId": "getInstructors", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search instructors by email or phone number", "schema": {"example": "john", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "size", "required": false, "in": "query", "description": "Alias for limit - items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "schema": {"example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get instructors with filtering", "tags": ["Users", "Users"]}}, "/v1/users/students": {"get": {"description": "Get students with search, pagination, and sorting", "operationId": "getStudents", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search students by email or phone number", "schema": {"example": "john", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "size", "required": false, "in": "query", "description": "Alias for limit - items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "schema": {"example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get students with filtering", "tags": ["Users", "Users"]}}, "/v1/users/profile": {"get": {"description": "Get profile", "operationId": "getProfile", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get profile", "tags": ["Users", "Users"]}, "post": {"description": "Storing profile", "operationId": "storeProfile", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Store profile", "tags": ["Users", "Users"]}}, "/v1/mail/welcome": {"post": {"operationId": "MailController_sendWelcomeEmail_v1", "parameters": [], "responses": {"201": {"description": "Welcome email queued successfully"}}, "summary": "Send welcome email (queued)", "tags": ["Mail"]}}, "/v1/mail/password-reset": {"post": {"operationId": "MailController_sendPasswordResetEmail_v1", "parameters": [], "responses": {"201": {"description": "Password reset email sent successfully"}}, "summary": "Send password reset email (immediate)", "tags": ["Mail"]}}, "/v1/mail/broadcast": {"post": {"operationId": "MailController_sendBroadcastEmail_v1", "parameters": [], "responses": {"201": {"description": "Broadcast email queued successfully"}}, "summary": "Send broadcast email to multiple recipients", "tags": ["Mail"]}}, "/v1/mail/queue-stats": {"post": {"operationId": "MailController_getQueueStats_v1", "parameters": [], "responses": {"200": {"description": "Queue statistics retrieved successfully"}}, "summary": "Get mail queue statistics", "tags": ["Mail"]}}, "/v1/auth/login": {"post": {"description": "Login with email and password", "operationId": "login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "<PERSON><PERSON>", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/register": {"post": {"description": "Register", "operationId": "register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Register", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/profile": {"get": {"description": "profile", "operationId": "profile", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Profile", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/google": {"get": {"description": "Google login", "operationId": "google", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Google Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/google/callback": {"get": {"description": "Login with email and password", "operationId": "googleCallback", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Google Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/facebook": {"get": {"description": "Login with email and password", "operationId": "facebookAuth", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/facebook/callback": {"get": {"description": "Facebook login", "operationId": "facebookCallback", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/linkedin": {"get": {"description": "Facebook login", "operationId": "linkedinAuth", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/linkedin/callback": {"get": {"description": "linkedin login", "operationId": "linkedinAuthCallback", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/verify-otp": {"post": {"description": "verifyOtp", "operationId": "verifyOtp", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/context": {"get": {"description": "Get current user context data for React client", "operationId": "context", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get User Context", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/forgot-password": {"post": {"description": "Send password reset email to user", "operationId": "forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Forgot Password", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/reset-password": {"post": {"description": "Reset user password using reset token", "operationId": "resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Reset Password", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/send-otp": {"post": {"description": "Send OTP to registered phone number", "operationId": "sendOtp", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendOtpDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Send OTP", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/countries": {"get": {"description": "Retrieve all countries with their codes, names, phone codes, and flags", "operationId": "CountryController_findAll_v1", "parameters": [], "responses": {"200": {"description": "List of all countries", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}}}}}}, "summary": "Get all countries", "tags": ["Countries"]}}, "/v1/countries/{code}": {"get": {"description": "Retrieve a specific country by its ISO 2-letter code", "operationId": "CountryController_findByCode_v1", "parameters": [{"name": "code", "required": true, "in": "path", "description": "ISO 2-letter country code (e.g., US, UK, CA)", "schema": {"example": "US", "type": "string"}}], "responses": {"200": {"description": "Country found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Country"}}}}, "404": {"description": "Country not found"}}, "summary": "Get country by code", "tags": ["Countries"]}}, "/v1/sms/send": {"post": {"operationId": "SmsController_sendSms_v1", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Sms"]}}, "/v1/sms/bulk": {"post": {"operationId": "SmsController_sendBulkSms_v1", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkSmsDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Sms"]}}, "/v1/meetings": {"post": {"operationId": "MeetingController_createMeeting_v1", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMeetingDto"}}}}, "responses": {"201": {"description": "Meeting created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Meeting"}}}}, "403": {"description": "Students cannot create meetings"}}, "security": [{"bearer": []}], "summary": "Create a new meeting", "tags": ["meetings"]}, "get": {"operationId": "MeetingController_getMeetings_v1", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "Filter by meeting status", "schema": {"type": "string", "enum": ["scheduled", "live", "ended", "cancelled"]}}, {"name": "type", "required": false, "in": "query", "description": "Filter by meeting type", "schema": {"type": "string", "enum": ["class", "webinar", "office_hours", "group_study"]}}, {"name": "hostId", "required": false, "in": "query", "description": "Filter by host ID", "schema": {"example": 1, "type": "number"}}, {"name": "courseId", "required": false, "in": "query", "description": "Filter by course ID", "schema": {"example": 1, "type": "number"}}, {"name": "startDate", "required": false, "in": "query", "description": "Filter by start date (ISO string)", "schema": {"example": "2024-01-01T00:00:00Z", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "Filter by end date (ISO string)", "schema": {"example": "2024-12-31T23:59:59Z", "type": "string"}}], "responses": {"200": {"description": "Meetings retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get all meetings with filters and pagination", "tags": ["meetings"]}}, "/v1/meetings/upcoming": {"get": {"operationId": "MeetingController_getUpcomingMeetings_v1", "parameters": [], "responses": {"200": {"description": "Upcoming meetings retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Meeting"}}}}}}, "security": [{"bearer": []}], "summary": "Get upcoming meetings for the current user", "tags": ["meetings"]}}, "/v1/meetings/{id}": {"get": {"operationId": "MeetingController_getMeetingById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Meeting ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "Meeting retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Meeting"}}}}, "404": {"description": "Meeting not found"}}, "security": [{"bearer": []}], "summary": "Get meeting by ID", "tags": ["meetings"]}, "put": {"operationId": "MeetingController_updateMeeting_v1", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Meeting ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMeetingDto"}}}}, "responses": {"200": {"description": "Meeting updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Meeting"}}}}, "403": {"description": "You can only update your own meetings"}, "404": {"description": "Meeting not found"}}, "security": [{"bearer": []}], "summary": "Update meeting", "tags": ["meetings"]}, "delete": {"operationId": "MeetingController_deleteMeeting_v1", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Meeting ID", "schema": {"type": "number"}}], "responses": {"204": {"description": "Meeting deleted successfully"}, "403": {"description": "You can only delete your own meetings"}, "404": {"description": "Meeting not found"}}, "security": [{"bearer": []}], "summary": "Delete meeting", "tags": ["meetings"]}}, "/v1/meetings/signature/{id}": {"get": {"operationId": "MeetingController_getMeetingSignature_v1", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Meeting ID", "schema": {"type": "number"}}, {"name": "role", "required": false, "in": "query", "description": "User role (0=participant, 1=host)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Meeting signature generated successfully"}}, "security": [{"bearer": []}], "summary": "Get meeting signature for SDK", "tags": ["meetings"]}}, "/v1/meetings/join": {"post": {"operationId": "MeetingController_joinMeeting_v1", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinMeetingDto"}}}}, "responses": {"200": {"description": "Meeting join URL generated successfully"}}, "security": [{"bearer": []}], "summary": "Join a meeting", "tags": ["meetings"]}}, "/v1/meetings/{id}/recordings": {"get": {"operationId": "MeetingController_getMeetingRecordings_v1", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Meeting ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "Meeting recordings retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get meeting recordings", "tags": ["meetings"]}}, "/v1/meetings/{id}/invite": {"post": {"operationId": "MeetingController_inviteParticipants_v1", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Meeting ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "Participants invited successfully"}}, "security": [{"bearer": []}], "summary": "Invite participants to meeting", "tags": ["meetings"]}}, "/v1/webhooks/zoom": {"post": {"operationId": "ZoomWebhookController_handleWebhook_v1", "parameters": [], "responses": {"200": {"description": "Webhook processed successfully"}}, "summary": "Handle Zoom webhook events", "tags": ["zoom-webhooks"]}}}, "info": {"title": "Courseted API", "description": "Courseted API Description", "version": "1.0.0", "contact": {}}, "tags": [], "servers": [{"url": "http://localhost:4000"}], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"ProfileDto": {"type": "object", "properties": {"firstName": {"type": "string", "description": "The first name of the user", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "The last name of the user", "example": "<PERSON><PERSON>"}, "profilePicture": {"type": "string", "description": "URL to the user's profile picture", "example": "https://courseted.com/images/profile.jpg"}}, "required": ["firstName", "lastName"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>", "description": "The email of the user"}, "password": {"type": "string", "example": "password123", "description": "The password of the user", "minLength": 6}}, "required": ["email", "password"]}, "RegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "The email address of the user", "example": "<EMAIL>"}, "password": {"type": "string", "description": "The password of the user", "example": "password123", "minLength": 6}, "phoneNumber": {"type": "string", "description": "The phone number of the user", "example": "+1234567890"}, "countryId": {"type": "number", "description": "The country ID of the user", "example": "1"}}, "required": ["email", "password", "phoneNumber", "countryId"]}, "VerifyOtpDto": {"type": "object", "properties": {"code": {"type": "string", "description": "Code for the user", "example": "123567"}, "phoneNumber": {"type": "string", "description": "The phone number of the user", "example": "123456789"}}, "required": ["code", "phoneNumber"]}, "ForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>", "description": "Email address of the user"}}, "required": ["email"]}, "ResetPasswordDto": {"type": "object", "properties": {"token": {"type": "string", "example": "abc123xyz789", "description": "Password reset token"}, "newPassword": {"type": "string", "example": "newSecurePassword123", "description": "New password for the user", "minLength": 8}}, "required": ["token", "newPassword"]}, "SendOtpDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "The phone number to send OTP to", "example": "+1234567890"}}, "required": ["phoneNumber"]}, "Country": {"type": "object", "properties": {"createdAt": {"format": "date-time", "type": "string", "description": "The date when the profile was created", "example": "2023-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "The date when the profile was last updated", "example": "2023-01-02T00:00:00Z"}, "id": {"type": "number", "description": "The unique identifier of the country", "example": "1"}, "name": {"type": "string", "description": "The name of the country", "example": "United States"}, "code": {"type": "string", "description": "The ISO 3166-1 alpha-2 country code", "example": "US"}, "iso3": {"type": "string", "description": "The ISO 3166-1 alpha-3 country code", "example": "USA"}, "phoneCode": {"type": "string", "description": "The international dialing code", "example": "+1"}, "flag": {"type": "string", "description": "The emoji flag of the country", "example": "🇺🇸"}, "profiles": {"description": "Profiles associated with this country", "type": "array", "items": {"type": "Profile"}}}, "required": ["createdAt", "updatedAt", "id", "name", "code", "iso3", "phoneCode", "profiles"]}, "SmsDto": {"type": "object", "properties": {"to": {"type": "string", "example": "+1234567890", "description": "The recipient phone number"}, "body": {"type": "string", "example": "Hello, this is a test message", "description": "The SMS message body"}, "from": {"type": "string", "example": "+1987654321", "description": "The sender phone number (optional)"}, "senderId": {"type": "number", "example": 1, "description": "The sender user ID (optional)"}}, "required": ["to", "body"]}, "BulkSmsDto": {"type": "object", "properties": {"messages": {"description": "Array of SMS messages to send", "example": [{"to": "+1234567890", "body": "Hello, this is a test message 1", "from": "+1987654321"}, {"to": "+1234567891", "body": "Hello, this is a test message 2", "from": "+1987654321"}], "type": "array", "items": {"$ref": "#/components/schemas/SmsDto"}}}, "required": ["messages"]}, "CreateMeetingDto": {"type": "object", "properties": {"topic": {"type": "string", "description": "Meeting topic/title", "example": "React Advanced Concepts", "minLength": 1}, "description": {"type": "string", "description": "Meeting description", "example": "Advanced React concepts including hooks, context, and performance optimization"}, "type": {"type": "string", "description": "Type of meeting", "enum": ["class", "webinar", "office_hours", "group_study"], "example": "class"}, "startTime": {"type": "string", "description": "Meeting start time in ISO format", "example": "2024-01-15T10:00:00Z"}, "duration": {"type": "number", "description": "Meeting duration in minutes", "example": 90, "minimum": 15, "maximum": 480}, "timezone": {"type": "string", "description": "Meeting timezone", "example": "America/New_York"}, "password": {"type": "string", "description": "Meeting password", "example": "secret123"}, "maxParticipants": {"type": "number", "description": "Maximum number of participants", "example": 100, "minimum": 1, "maximum": 1000}, "isRecordingEnabled": {"type": "boolean", "description": "Whether recording is enabled", "example": true}, "isWaitingRoomEnabled": {"type": "boolean", "description": "Whether waiting room is enabled", "example": true}, "isMuteOnEntry": {"type": "boolean", "description": "Whether participants are muted on entry", "example": true}, "courseId": {"type": "number", "description": "Course ID if this meeting is associated with a course", "example": 1}, "invitedUserIds": {"description": "Array of user IDs to invite to the meeting", "example": [1, 2, 3], "type": "array", "items": {"type": "string"}}}, "required": ["topic", "type", "startTime", "duration"]}, "UserRole": {"type": "string", "enum": ["admin", "student", "instructor"], "description": "The role of the user in the system"}, "Profile": {"type": "object", "properties": {"createdAt": {"format": "date-time", "type": "string", "description": "The date when the profile was created", "example": "2023-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "The date when the profile was last updated", "example": "2023-01-02T00:00:00Z"}, "id": {"type": "number", "description": "The unique identifier of the profile", "example": "1"}, "firstName": {"type": "string", "description": "The first name of the user", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "The last name of the user", "example": "<PERSON><PERSON>"}, "profilePicture": {"type": "string", "description": "URL to the user's profile picture", "example": "https://courseted.com/images/profile.jpg"}, "country": {"description": "The country of the user", "allOf": [{"$ref": "#/components/schemas/Country"}]}, "user": {"description": "The user this profile belongs to", "allOf": [{"$ref": "#/components/schemas/User"}]}}, "required": ["createdAt", "updatedAt", "id", "user"]}, "User": {"type": "object", "properties": {"createdAt": {"format": "date-time", "type": "string", "description": "The date when the profile was created", "example": "2023-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "The date when the profile was last updated", "example": "2023-01-02T00:00:00Z"}, "id": {"type": "number", "description": "The unique identifier of the user", "example": "1"}, "email": {"type": "string", "description": "The email address of the user", "example": "<EMAIL>"}, "password": {"type": "string", "description": "The hashed password of the user", "example": "hashedPassword123"}, "isEmailVerified": {"type": "boolean", "description": "Whether the user has verified their email", "example": false, "default": false}, "role": {"description": "The role of the user in the system", "example": "student", "default": "student", "allOf": [{"$ref": "#/components/schemas/UserRole"}]}, "googleId": {"type": "string", "description": "Google OAuth ID for the user", "example": "123456789"}, "facebookId": {"type": "string", "description": "Facebook OAuth ID for the user", "example": "123456789"}, "linkedinId": {"type": "string", "description": "LinkedIn OAuth ID for the user", "example": "123456789"}, "profile": {"description": "The user's profile information", "allOf": [{"$ref": "#/components/schemas/Profile"}]}, "country": {"description": "The country this user belongs to", "allOf": [{"$ref": "#/components/schemas/Country"}]}, "phoneNumber": {"type": "string", "description": "User's phone number", "example": "+8801XXXXXXXXX"}, "isPhoneVerified": {"type": "boolean", "description": "Whether phone is verified", "default": false}, "resetPasswordToken": {"type": "string", "description": "Password reset token"}, "resetPasswordExpiresAt": {"format": "date-time", "type": "string", "description": "Password reset token expiration date"}}, "required": ["createdAt", "updatedAt", "id", "email", "isEmailVerified", "role", "profile", "country", "phoneNumber", "isPhoneVerified"]}, "Meeting": {"type": "object", "properties": {"createdAt": {"format": "date-time", "type": "string", "description": "The date when the profile was created", "example": "2023-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "The date when the profile was last updated", "example": "2023-01-02T00:00:00Z"}, "id": {"type": "number", "description": "The unique identifier of the meeting", "example": "1"}, "meetingId": {"type": "string", "description": "Zoom meeting ID", "example": "123456789"}, "topic": {"type": "string", "description": "Meeting topic/title", "example": "React Advanced Concepts"}, "description": {"type": "string", "description": "Meeting description", "example": "Advanced React concepts including hooks, context, and performance optimization"}, "type": {"type": "string", "description": "Type of meeting", "enum": ["class", "webinar", "office_hours", "group_study"], "example": "class"}, "status": {"type": "string", "description": "Meeting status", "enum": ["scheduled", "live", "ended", "cancelled"], "example": "scheduled"}, "startTime": {"format": "date-time", "type": "string", "description": "Meeting start time", "example": "2024-01-15T10:00:00Z"}, "duration": {"type": "number", "description": "Meeting duration in minutes", "example": 90}, "timezone": {"type": "string", "description": "Meeting timezone", "example": "America/New_York"}, "password": {"type": "string", "description": "Meeting password", "example": "secret123"}, "joinUrl": {"type": "string", "description": "Zoom join URL", "example": "https://zoom.us/j/123456789"}, "startUrl": {"type": "string", "description": "Zoom start URL for host", "example": "https://zoom.us/s/123456789"}, "maxParticipants": {"type": "number", "description": "Maximum number of participants", "example": 100}, "isRecordingEnabled": {"type": "boolean", "description": "Whether recording is enabled", "example": true}, "isWaitingRoomEnabled": {"type": "boolean", "description": "Whether waiting room is enabled", "example": true}, "isMuteOnEntry": {"type": "boolean", "description": "Whether participants are muted on entry", "example": true}, "host": {"description": "Meeting host", "allOf": [{"$ref": "#/components/schemas/User"}]}, "hostId": {"type": "number", "description": "Host ID", "example": 1}, "hostEmail": {"type": "string", "description": "Host email address", "example": "<EMAIL>"}, "courseId": {"type": "number", "description": "Course ID if this meeting is associated with a course", "example": 1}}, "required": ["createdAt", "updatedAt", "id", "meetingId", "topic", "type", "status", "startTime", "duration", "timezone", "joinUrl", "startUrl", "maxParticipants", "isRecordingEnabled", "isWaitingRoomEnabled", "isMuteOnEntry", "host", "hostId", "hostEmail"]}, "UpdateMeetingDto": {"type": "object", "properties": {"topic": {"type": "string", "description": "Meeting topic/title", "example": "React Advanced Concepts - Updated", "minLength": 1}, "description": {"type": "string", "description": "Meeting description", "example": "Updated description for advanced React concepts"}, "startTime": {"type": "string", "description": "Meeting start time in ISO format", "example": "2024-01-15T10:00:00Z"}, "duration": {"type": "number", "description": "Meeting duration in minutes", "example": 90, "minimum": 15, "maximum": 480}, "password": {"type": "string", "description": "Meeting password", "example": "newsecret123"}, "maxParticipants": {"type": "number", "description": "Maximum number of participants", "example": 150, "minimum": 1, "maximum": 1000}, "isRecordingEnabled": {"type": "boolean", "description": "Whether recording is enabled", "example": false}, "isWaitingRoomEnabled": {"type": "boolean", "description": "Whether waiting room is enabled", "example": false}, "isMuteOnEntry": {"type": "boolean", "description": "Whether participants are muted on entry", "example": false}}}, "JoinMeetingDto": {"type": "object", "properties": {"meetingId": {"type": "string", "description": "Meeting ID to join", "example": "123456789"}, "password": {"type": "string", "description": "Meeting password if required", "example": "secret123"}, "userName": {"type": "string", "description": "User name for the meeting", "example": "<PERSON>"}, "userEmail": {"type": "string", "description": "User email for the meeting", "example": "<EMAIL>"}}, "required": ["meetingId", "userName", "userEmail"]}}}, "externalDocs": {"description": "More Information", "url": ""}}