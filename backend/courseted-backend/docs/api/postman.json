{"item": [{"id": "d2b79e7e-7991-4c9d-abf1-470539b49b7d", "name": "Users", "item": [{"id": "bbaf44ab-b10d-483f-9558-70cf7a63f05f", "name": "GET /v1/users", "request": {"description": {"content": "Get All Users with Filtering", "type": "text/plain"}, "url": {"path": ["v1", "users"], "host": ["{{baseUrl}}"], "query": [{"description": {"content": "Search users by email or phone number", "type": "text/plain"}, "key": "search", "value": "john"}, {"description": {"content": "Page number (1-based)", "type": "text/plain"}, "key": "page", "value": "1"}, {"description": {"content": "Items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "limit", "value": "10"}, {"description": {"content": "Alias for limit - items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "size", "value": "10"}, {"description": {"content": "Sort by field (email, phoneNumber, createdAt, updatedAt, id, role)", "type": "text/plain"}, "key": "sortBy", "value": "createdAt"}, {"description": {"content": "Sort order", "type": "text/plain"}, "key": "sortOrder", "value": "value"}], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "1175f1a8-f634-454e-b115-da16823b85b7", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "1d963f99-2471-45f2-88f0-2a9fbf4fb359", "name": "GET /v1/users/instructors", "request": {"description": {"content": "Get instructors with filtering", "type": "text/plain"}, "url": {"path": ["v1", "users", "instructors"], "host": ["{{baseUrl}}"], "query": [{"description": {"content": "Search instructors by email or phone number", "type": "text/plain"}, "key": "search", "value": "john"}, {"description": {"content": "Page number (1-based)", "type": "text/plain"}, "key": "page", "value": "1"}, {"description": {"content": "Items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "limit", "value": "10"}, {"description": {"content": "Alias for limit - items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "size", "value": "10"}, {"description": {"content": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "type": "text/plain"}, "key": "sortBy", "value": "createdAt"}, {"description": {"content": "Sort order", "type": "text/plain"}, "key": "sortOrder", "value": "value"}], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "eac54cd6-dc14-49b7-9dad-5f751d38b6b6", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "75033788-e92a-43c5-ba17-0570621d0f8d", "name": "GET /v1/users/students", "request": {"description": {"content": "Get students with filtering", "type": "text/plain"}, "url": {"path": ["v1", "users", "students"], "host": ["{{baseUrl}}"], "query": [{"description": {"content": "Search students by email or phone number", "type": "text/plain"}, "key": "search", "value": "john"}, {"description": {"content": "Page number (1-based)", "type": "text/plain"}, "key": "page", "value": "1"}, {"description": {"content": "Items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "limit", "value": "10"}, {"description": {"content": "Alias for limit - items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "size", "value": "10"}, {"description": {"content": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "type": "text/plain"}, "key": "sortBy", "value": "createdAt"}, {"description": {"content": "Sort order", "type": "text/plain"}, "key": "sortOrder", "value": "value"}], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "9a225487-699f-47eb-9843-291d76f30d40", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "4b66a4fb-43d9-4ae1-8c54-2c1e22c36dd5", "name": "GET /v1/users/profile", "request": {"description": {"content": "Get profile", "type": "text/plain"}, "url": {"path": ["v1", "users", "profile"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "7bf7771b-0f09-4cbf-bade-cf1fd574dbd4", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "6485fff9-0ef4-43f5-b903-901d9727837f", "name": "POST /v1/users/profile", "request": {"description": {"content": "Store profile", "type": "text/plain"}, "url": {"path": ["v1", "users", "profile"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"profilePicture\": \"https://courseted.com/images/profile.jpg\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "6dcb9055-d83a-49a2-ba60-8ab788bf5cce", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "c35c0723-8ccb-4f2c-9a57-ec470093d0cd", "name": "Mail", "item": [{"id": "2afd7a6c-e7d3-4c84-8096-910707045c4a", "name": "POST /v1/mail/welcome", "request": {"description": {"content": "Send welcome email (queued)", "type": "text/plain"}, "url": {"path": ["v1", "mail", "welcome"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST"}, "response": [], "event": [{"listen": "test", "script": {"id": "e5f42274-bfb3-4db0-b4d6-74e7492d6097", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "f4b60e9f-8192-4c4f-bee0-15d9917589fe", "name": "POST /v1/mail/password-reset", "request": {"description": {"content": "Send password reset email (immediate)", "type": "text/plain"}, "url": {"path": ["v1", "mail", "password-reset"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST"}, "response": [], "event": [{"listen": "test", "script": {"id": "b6658cbf-225c-4ccc-b191-e6a3163452ad", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "28220d9d-564e-4cb5-ad31-44b07c13d656", "name": "POST /v1/mail/broadcast", "request": {"description": {"content": "Send broadcast email to multiple recipients", "type": "text/plain"}, "url": {"path": ["v1", "mail", "broadcast"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST"}, "response": [], "event": [{"listen": "test", "script": {"id": "c793a39f-3077-406a-9db6-dac72b369bc7", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "7c8d13b9-c90e-442b-b976-7bb0e6918be8", "name": "POST /v1/mail/queue-stats", "request": {"description": {"content": "Get mail queue statistics", "type": "text/plain"}, "url": {"path": ["v1", "mail", "queue-stats"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST"}, "response": [], "event": [{"listen": "test", "script": {"id": "b9cfb0d9-33a3-47cb-8ccd-f8c40ced3187", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "65f6b227-d1fa-4142-90f2-b6ce20d0125c", "name": "<PERSON><PERSON>", "item": [{"id": "23434ff4-0782-45c6-850a-f1baa42c83ac", "name": "POST /v1/auth/login", "request": {"description": {"content": "<PERSON><PERSON>", "type": "text/plain"}, "url": {"path": ["v1", "auth", "login"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "516fed11-a014-4351-84a1-03ce41076b82", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "75eb0742-a8b3-41fd-984e-98720612cc68", "name": "POST /v1/auth/register", "request": {"description": {"content": "Register", "type": "text/plain"}, "url": {"path": ["v1", "auth", "register"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"phoneNumber\": \"123456789\",\n  \"countryId\": \"1\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "2ead6cea-6c78-40f2-81d8-caf712019aec", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "22dc8030-b93a-4e02-a4a0-08c79e5860bb", "name": "GET /v1/auth/profile", "request": {"description": {"content": "Profile", "type": "text/plain"}, "url": {"path": ["v1", "auth", "profile"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "9a9f5137-252f-4b90-9158-10043b9f05c2", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "c5461268-680a-4dad-b1e6-7989abe4024c", "name": "GET /v1/auth/google", "request": {"description": {"content": "Google Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "google"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "fc59c7be-96d8-403a-a239-d1b52303f857", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "bb712343-d65c-4237-9686-3aa7bbe149b0", "name": "GET /v1/auth/google/callback", "request": {"description": {"content": "Google Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "google", "callback"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "ee466333-c8fb-41e7-91e6-d8189b9378be", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "ae137ede-535e-4f4a-9fe4-f7b9c636002a", "name": "GET /v1/auth/facebook", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "facebook"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "56c16a0c-6ff0-4026-bd2d-d26b6391ff26", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "e940990d-2bd7-4608-84d1-e7f94f2d018a", "name": "GET /v1/auth/facebook/callback", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "facebook", "callback"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "11de6414-fec6-4192-97a8-295f394e7d24", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "f4f1a689-8a3f-4c5f-a116-35661bb29933", "name": "GET /v1/auth/linkedin", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "linkedin"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "b735e5be-8336-4e07-804a-740bbab3a5c2", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "52232820-27ad-4cea-8cc5-f122bd7fb476", "name": "GET /v1/auth/linkedin/callback", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "linkedin", "callback"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "6fbf4739-3293-49c0-8295-087ddaea53f1", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "dc17635e-4310-4f48-9b95-d8f9f11ad686", "name": "POST /v1/auth/verify-otp", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "verify-otp"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"code\": \"123567\",\n  \"phoneNumber\": \"123456789\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "bd7169f0-900a-4d62-b8be-ffe2fef88491", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "47cbc707-a202-4b57-87c2-a4cfd5e11624", "name": "GET /v1/auth/context", "request": {"description": {"content": "Get User Context", "type": "text/plain"}, "url": {"path": ["v1", "auth", "context"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "02085f57-a75a-49c8-845b-47b286c588ff", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "00db4df9-7285-4532-bff5-5ea677dfc78c", "name": "POST /v1/auth/forgot-password", "request": {"description": {"content": "Forgot Password", "type": "text/plain"}, "url": {"path": ["v1", "auth", "forgot-password"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "fc5bbebc-5954-4ad2-9cc8-3cc406050d80", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "9dfa9ebe-8648-4a02-9c50-3a2e0a9a578e", "name": "POST /v1/auth/reset-password", "request": {"description": {"content": "Reset Password", "type": "text/plain"}, "url": {"path": ["v1", "auth", "reset-password"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"token\": \"abc123xyz789\",\n  \"newPassword\": \"newSecurePassword123\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "ae65ddd1-69e8-4e7c-b655-0e1441ac0eb2", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "e8f13ee6-8edb-4a39-bb55-789a17d0c852", "name": "Countries", "item": [{"id": "7fc09a6c-8f8f-463f-a141-3c3f3b2cc31e", "name": "GET /v1/countries", "request": {"description": {"content": "Get all countries", "type": "text/plain"}, "url": {"path": ["v1", "countries"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "caa9ba42-a8b2-474f-aaba-fa613692c774", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "91d605f5-a260-4174-adbc-36681fd6a039", "name": "GET /v1/countries/{code}", "request": {"description": {"content": "Get country by code", "type": "text/plain"}, "url": {"path": ["v1", "countries", "US"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "5f9894fe-2af6-41ea-8678-9a2ac9f8664f", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "02164121-7146-4ab8-9707-dad9de672c7b", "name": "Sms", "item": [{"id": "792a3873-53a9-4002-a19d-cff6eedc51e3", "name": "POST /v1/sms/send", "request": {"description": {}, "url": {"path": ["v1", "sms", "send"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"password\": \"<EMAIL>\",\n  \"to\": \"<EMAIL>\",\n  \"body\": \"<EMAIL>\",\n  \"from\": \"<EMAIL>\",\n  \"senderId\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "f8b0c139-c45c-41ed-b9d2-9a75d770487a", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "6f0840cb-eb32-4537-b192-f4734321aa2c", "name": "POST /v1/sms/bulk", "request": {"description": {}, "url": {"path": ["v1", "sms", "bulk"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST"}, "response": [], "event": [{"listen": "test", "script": {"id": "b85d54ae-f2be-4cd6-8baa-c243c943cbdb", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}], "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.T6umjVi6SGGXNe9g0jgrt_EDl5NdrStArO8ReuJixBM", "key": "token"}]}, "event": [], "variable": [{"type": "string", "value": "http://localhost:8000", "key": "baseUrl"}], "info": {"_postman_id": "6bf5f79e-8e9a-43b9-91dd-e1e3b376c775", "name": "courseted-api", "version": {"raw": "1.0.0", "major": 1, "minor": 0, "patch": 0, "prerelease": [], "build": [], "string": "1.0.0"}, "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": {"content": "API collection generated from OpenAPI/Swagger documentation", "type": "text/plain"}}}