# Rate Limiting Security Implementation

## Overview
Comprehensive rate limiting security has been implemented to protect the API from abuse and DDoS attacks.

## Components Added

### 1. Global Rate Limiting (app.module.ts)
- **ThrottlerModule** configured with multiple tiers:
  - **Short**: 3 requests per second
  - **Medium**: 20 requests per 10 seconds  
  - **Long**: 100 requests per minute
- **ThrottlerGuard** applied globally via APP_GUARD

### 2. Security Middleware (main.ts)
- **Helmet**: Security headers and CSP protection
- **Compression**: Response compression for better performance

### 3. Custom Throttle Decorators (throttle.decorator.ts)

#### AuthThrottle
- **Usage**: Login/Register endpoints
- **Limit**: 5 requests per minute
- **Purpose**: Prevent brute force attacks

#### SensitiveOperationThrottle  
- **Usage**: OTP verification, password reset
- **Limit**: 3 requests per minute
- **Purpose**: Strict protection for sensitive operations

#### GeneralThrottle
- **Usage**: General authenticated endpoints
- **Limit**: 100 requests per minute
- **Purpose**: Normal API usage protection

#### PublicThrottle
- **Usage**: OAuth endpoints, public APIs
- **Limit**: 200 requests per minute
- **Purpose**: Lenient protection for public access

## Applied Rate Limits

### Authentication Endpoints
| Endpoint | Rate Limit | Decorator |
|----------|------------|-----------|
| `POST /auth/login` | 5/min | @AuthThrottle |
| `POST /auth/register` | 5/min | @AuthThrottle |
| `POST /auth/verify-otp` | 3/min | @SensitiveOperationThrottle |

### User Endpoints
| Endpoint | Rate Limit | Decorator |
|----------|------------|-----------|
| `GET /auth/profile` | 100/min | @GeneralThrottle |
| `GET /auth/context` | 100/min | @GeneralThrottle |

### OAuth Endpoints
| Endpoint | Rate Limit | Decorator |
|----------|------------|-----------|
| `GET /auth/google` | 200/min | @PublicThrottle |
| `GET /auth/google/callback` | 200/min | @PublicThrottle |
| `GET /auth/facebook` | 200/min | @PublicThrottle |
| `GET /auth/facebook/callback` | 200/min | @PublicThrottle |
| `GET /auth/linkedin` | 200/min | @PublicThrottle |
| `GET /auth/linkedin/callback` | 200/min | @PublicThrottle |

## Security Benefits

1. **DDoS Protection**: Global rate limiting prevents overwhelming the server
2. **Brute Force Prevention**: Strict limits on auth endpoints
3. **Resource Conservation**: Prevents excessive API consumption
4. **Security Headers**: Helmet adds essential security headers
5. **Response Optimization**: Compression reduces bandwidth usage

## Configuration

### Environment Variables
No additional environment variables required. Rate limits are configured in code for consistency.

### Customization
Rate limits can be adjusted by modifying the decorators in `throttle.decorator.ts` or the global configuration in `app.module.ts`.

## Monitoring

Rate limiting responses include headers:
- `X-RateLimit-Limit`: The rate limit ceiling
- `X-RateLimit-Remaining`: Number of requests left
- `X-RateLimit-Reset`: UTC date time when the rate limit resets

## Error Responses

When rate limit is exceeded:
```json
{
  "statusCode": 429,
  "message": "ThrottlerException: Too Many Requests",
  "error": "Too Many Requests"
}
```

## Future Enhancements

1. **Redis Integration**: For distributed rate limiting across multiple servers
2. **IP-based Limiting**: Different limits per IP address
3. **User-based Limiting**: Different limits based on user roles
4. **Dynamic Limits**: Configurable limits via environment variables
5. **Rate Limit Bypass**: Special tokens for development/testing
