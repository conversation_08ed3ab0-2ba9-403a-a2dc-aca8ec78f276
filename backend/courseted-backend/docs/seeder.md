# Database Seeding System

This project uses a modular seeding system based on design patterns to make it easy to develop and maintain database seeds.

## Architecture

The seeding system follows these design patterns:

- **Strategy Pattern**: Each seeder implements the `ISeeder` interface
- **Template Method Pattern**: `BaseSeeder` provides a common structure for all seeders
- **Factory Pattern**: `SeederRunner` manages and executes seeders
- **Dependency Injection**: Seeders receive the DataSource and existing data as dependencies

## Structure

```
src/database/seeds/
├── interfaces/
│   └── seeder.interface.ts     # ISeeder interface
├── base/
│   └── base.seeder.ts          # Abstract base seeder class
├── seeders/
│   ├── country.seeder.ts       # Country seeder
│   ├── user.seeder.ts          # User seeder
│   ├── profile.seeder.ts       # Profile seeder
│   └── sms.seeder.ts           # SMS seeder
├── runner/
│   └── seeder.runner.ts        # Seeder orchestrator
├── data/
│   ├── country.seed.ts         # Country seed data
│   ├── user.seed.ts            # User seed data
│   ├── profile.seed.ts         # Profile seed data
│   └── sms.seed.ts             # SMS seed data
├── index.ts                    # Exports
└── seed-data.ts                # Main entry point
```

## Usage

### Advanced Seeding

Run all seeders:
```bash
npm run seed:advanced
```

List available seeders:
```bash
npm run seed:list
```

Run specific seeders:
```bash
npm run seed:only Countries Users
```

## Creating New Seeders

1. **Create seed data file**:
```typescript
// src/database/seeds/data/my-entity.seed.ts
export const MY_ENTITY_SEED = [
  { name: "Example", value: "test" },
  // ... more data
];
```

2. **Create seeder class**:
```typescript
// src/database/seeds/seeders/my-entity.seeder.ts
import { MyEntity } from "@/entities";
import { BaseSeeder } from "../base/base.seeder";
import { MY_ENTITY_SEED } from "../data/my-entity.seed";

export class MyEntitySeeder extends BaseSeeder<MyEntity> {
  protected entityClass = MyEntity;
  protected seedData = MY_ENTITY_SEED;

  getName(): string {
    return "MyEntities";
  }

  // Optional: Override to process data before saving
  protected processData(data: any, existingData?: any): any {
    // Transform data if needed
    return data;
  }
}
```

3. **Add to SeederRunner**:
```typescript
// src/database/seeds/runner/seeder.runner.ts
import { MyEntitySeeder } from "../seeders/my-entity.seeder";

constructor() {
  this.seeders = [
    new CountrySeeder(),
    new UserSeeder(),
    new MyEntitySeeder(), // Add here in dependency order
    // ... other seeders
  ];
}
```

4. **Export from index**:
```typescript
// src/database/seeds/index.ts
export * from "./seeders/my-entity.seeder";
```

## Features

- **Automatic Duplicate Prevention**: Checks if data exists before seeding
- **Dependency Management**: Seeders run in order, with access to previously seeded data
- **Selective Seeding**: Run only specific seeders when needed
- **Error Handling**: Proper error handling and rollback
- **Extensible**: Easy to add new seeders following the same pattern
- **Data Processing**: Override `processData()` to transform data before saving
- **Relationship Handling**: Access to previously seeded entities for foreign key relationships

## Best Practices

1. **Order Matters**: Place seeders in dependency order (independent entities first)
2. **Use processData()**: Transform data in `processData()` method for complex relationships
3. **Keep Data Separate**: Store seed data in separate files for maintainability
4. **Idempotent Seeds**: Ensure seeders can run multiple times safely
5. **Clear Naming**: Use descriptive names for seeders and data files
