services:
  courseted-api:
    image: node:22-alpine
    working_dir: /app
    command: yarn start:dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    env_file:
      - .env
    environment:
      - NODE_ENV=development
      - POSTGRES_HOST=courseted-db
      - POSTGRES_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=courseted
      - REDIS_HOST=courseted-redis
      - REDIS_PORT=6379
      - FRONTEND_URL=http://localhost:3000
    depends_on:
      - courseted-db
      # - courseted-redis
    networks:
      - courseted
  courseted-db:
    image: postgres:17-alpine
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=courseted
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - courseted
  courseted-redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - courseted
  # redis-commander:
  #   image: rediscommander/redis-commander:latest
  #   environment:
  #     - REDIS_HOSTS=local:courseted-redis:6379
  #   ports:
  #     - "8081:8081"
  #   depends_on:
  #     - courseted-redis
  #   networks:
  #     - courseted

networks:
  courseted:
    external: true

volumes:
  postgres_data:
  redis_data: