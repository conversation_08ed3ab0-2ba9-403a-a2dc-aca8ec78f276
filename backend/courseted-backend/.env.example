NODE_ENV=development
PORT=3000

POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=courseted

# Mail Configuration
MAIL_DRIVER=smtp
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=Courseted

# SMTP Configuration (when MAIL_DRIVER=smtp)
MAIL_HOST=smtp.ethereal.email
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=d7BKnSe61RZ4Z2xw8u

# AWS SES Configuration (when MAIL_DRIVER=ses)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Redis Configuration (for mail queuing)
REDIS_HOST=localhost
REDIS_PORT=6379
MAIL_QUEUE_NAME=mail

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d

# Zoom API Configuration
ZOOM_API_KEY=hljq8sdTQNmqcj_gBuSFPg
ZOOM_API_SECRET=ay90utN8BCbJGncWRfTv9tW6CHiw7lu0
ZOOM_SDK_KEY=4II9KGGRSEqNERrw3TavmQ
ZOOM_SDK_SECRET=vJgejYGsQlWSnqcufbOS2LakSnQRcvG4
ZOOM_WEBHOOK_SECRET=your_zoom_webhook_secret_here

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Application URLs
FRONTEND_URL=http://localhost:3000