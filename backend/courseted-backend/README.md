# Courseted Backend

A modern e-learning platform backend built with NestJS and PostgreSQL.

## Tech Stack

- **Framework:** NestJS
- **Database:** PostgreSQL
- **ORM:** TypeORM
- **Container:** Docker
- **Testing:** Jest

## System Architecture

```mermaid
%%{init: {'theme': 'forest', 'themeVariables': { 'primaryColor': '#5a67d8', 'primaryTextColor': '#fff', 'primaryBorderColor': '#4c51bf', 'lineColor': '#5a67d8', 'secondaryColor': '#6b7280', 'tertiaryColor': '#f0f4f8'}}}%%
flowchart TB
    %% Client Layer
    subgraph ClientLayer["🌐 Client Layer"]
        style ClientLayer fill:#e6f7ff,stroke:#1890ff,stroke-width:2px
        Web["💻 Web Browser"]
        Mobile["📱 Mobile App"]
        API["🔌 API Client"]
    end

    %% Load Balancer
    LoadBalancer["⚖️ Load Balancer"]
    style LoadBalancer fill:#f6ffed,stroke:#52c41a,stroke-width:2px
    
    %% Docker Environment
    subgraph DockerEnv["🐳 Docker Environment"]
        style DockerEnv fill:#f9f0ff,stroke:#722ed1,stroke-width:2px
        %% Application Container
        subgraph AppContainer["🚀 Application Container"]
            style AppContainer fill:#f4f4f4,stroke:#d9d9d9,stroke-width:1px
            NestJS["🔥 NestJS Backend"]
            TypeORM["🔄 TypeORM"]
            NestJS --> TypeORM
        end
        
        %% Database Container
        subgraph DBContainer["💾 Database Container"]
            style DBContainer fill:#e6fffb,stroke:#13c2c2,stroke-width:1px
            PostgreSQL[(🐘 PostgreSQL)]
            style PostgreSQL fill:#e6fffb,stroke:#13c2c2,stroke-width:1px
            TypeORM --> PostgreSQL
        end
        
        %% Cache Container
        subgraph CacheContainer["⚡ Cache Container"]
            style CacheContainer fill:#fff2e8,stroke:#fa8c16,stroke-width:1px
            Redis[(🔄 Redis)]
            style Redis fill:#fff2e8,stroke:#fa8c16,stroke-width:1px
            NestJS --> Redis
        end

        %% Utility Containers
        subgraph UtilityContainers["🔧 Utility Containers"]
            style UtilityContainers fill:#f9f0ff,stroke:#722ed1,stroke-width:1px
            Logger["📝 Logging Service"]
            Metrics["📊 Metrics Service"]
            NestJS --> Logger
            NestJS --> Metrics
        end
    end

    %% Storage Layer
    subgraph StorageLayer["💽 Storage Layer"]
        style StorageLayer fill:#e6f7ff,stroke:#1890ff,stroke-width:2px
        Backup["🔒 Backup Service"]
        Volume1["💿 PostgreSQL Volume"]
        Volume2["💿 Redis Volume"]
        PostgreSQL --> Backup
        PostgreSQL --> Volume1
        Redis --> Volume2
    end

    %% Monitoring
    subgraph MonitoringLayer["📈 Monitoring"]
        style MonitoringLayer fill:#fff1f0,stroke:#f5222d,stroke-width:2px
        Monitoring["📊 Monitoring Dashboard"]
        Logger --> Monitoring
        Metrics --> Monitoring
    end

    %% Connections
    Web --> LoadBalancer
    Mobile --> LoadBalancer
    API --> LoadBalancer
    LoadBalancer --> NestJS
    
    %% Styling for connections
    linkStyle 0,1,2 stroke:#1890ff,stroke-width:2px,stroke-dasharray: 5 5
    linkStyle 3,4,5,6,7,8,9,10,11,12,13 stroke:#722ed1,stroke-width:2px
```

## Prerequisites

- Node.js 22.x
- Docker and Docker Compose
- PostgreSQL (if running locally)
- Yarn package manager

## Project setup
### Macos
```bash
# Install Make using Homebrew
brew install make
```
### Windows
```bash
# Install Make using Chocolatey
choco install make
```
## Installation
```bash
# Start containers
$ make up
# Install dependencies
$ make install
# Access PostgreSQL shell
$ make db-migrate
```

## Commands

Create docker network:
```bash
make network
```

```bash
# Start all containers in detached mode
$ make up

# Install dependencies in container
$ make install


# View container logs
$ make logs

# Access PostgreSQL shell
$ make db-shell

# Stop all containers
$ make down

# Run tests
$ make test
```
