# Makefile for Courseted Backend
# Commands run inside Docker container

# Docker variables
DOCKER_COMPOSE = docker-compose
DOCKER_EXEC = $(DOCKER_COMPOSE) exec courseted-api
DOCKER_EXEC_ROOT = $(DOCKER_COMPOSE) exec -u root courseted-api

# Project variables
PROJECT_NAME = courseted-backend
SRC_DIR = src
DIST_DIR = dist
SCRIPTS_DIR = $(SRC_DIR)/scripts

# Main targets
.PHONY: help build start dev debug prod lint test test-watch test-cov test-e2e clean postman install lint-fix network

help:
	@echo "Available commands:"
	@echo "  make up            - Start Docker containers"
	@echo "  make down          - Stop Docker containers"
	@echo "  make network       - Create Docker network for courseted"
	@echo "  make install       - Install dependencies using yarn inside container"
	@echo "  make build         - Build the application inside container"
	@echo "  make start         - Start the application inside container"
	@echo "  make dev           - Start the application in development mode inside container"
	@echo "  make debug         - Start the application in debug mode inside container"
	@echo "  make prod          - Start the application in production mode inside container"
	@echo "  make lint          - Run linting inside container"
	@echo "  make lint-fix      - Run linting with autofix inside container"
	@echo "  make test          - Run tests inside container"
	@echo "  make test-watch    - Run tests in watch mode inside container"
	@echo "  make test-cov      - Run tests with coverage inside container"
	@echo "  make test-e2e      - Run end-to-end tests inside container"
	@echo "  make clean         - Clean build artifacts inside container"
	@echo "  make postman       - Generate Postman collection inside container"
	@echo "  make shell         - Open a shell in the container"
	@echo "  make logs          - View container logs"
	@echo ""
	@echo "Database commands:"
	@echo "  make db-migrate    - Run database migrations"
	@echo "  make db-generate   - Generate a new migration"
	@echo "  make db-revert     - Revert last migration"
	@echo "  make db-drop       - Drop database schema"
	@echo "  make db-shell      - Open database shell"
	@echo ""
	@echo "Seeding commands:"
	@echo "  make seed - Run advanced seeding (all entities)"
	@echo "  make seed-list     - List available seeders"
	@echo "  make seed-only     - Run specific seeders (usage: make seed-only seeders='Countries Users')"

# Docker commands
up:
	$(DOCKER_COMPOSE) up -d

down:
	$(DOCKER_COMPOSE) down

network:
	@echo "Creating Docker network 'courseted'..."
	@docker network create courseted 2>/dev/null || echo "Network 'courseted' already exists"

shell:
	$(DOCKER_EXEC) sh

logs:
	$(DOCKER_COMPOSE) logs -f

# Dependency management
install:
	$(DOCKER_EXEC) yarn install

# Application commands
build:
	$(DOCKER_EXEC) yarn build

start:
	$(DOCKER_EXEC) yarn start

dev:
	$(DOCKER_COMPOSE) up -d

debug:
	$(DOCKER_EXEC) yarn start:debug

prod:
	$(DOCKER_EXEC) yarn start:prod

lint:
	$(DOCKER_EXEC) yarn lint

lint-fix:
	$(DOCKER_EXEC) yarn lint:fix

test:
	$(DOCKER_EXEC) yarn test

test-watch:
	$(DOCKER_EXEC) yarn test:watch

test-cov:
	$(DOCKER_EXEC) yarn test:coverage

test-e2e:
	$(DOCKER_EXEC) yarn test:e2e

clean:
	$(DOCKER_EXEC) rm -rf $(DIST_DIR)

postman:
	$(DOCKER_EXEC) yarn build:postman

# Database related commands
.PHONY: db-migrate db-generate db-create db-revert db-shell db-drop seed seed-list seed-only

db-migrate:
	$(DOCKER_EXEC) yarn migration:run

db-generate:
	@echo "Generating migration with name: $(name)"
	$(DOCKER_EXEC) yarn migration:generate

db-create:
	@echo "Creating empty migration with name: $(name)"
	$(DOCKER_EXEC) yarn typeorm migration:create

db-revert:
	$(DOCKER_EXEC) yarn migration:revert

db-drop:
	$(DOCKER_EXEC) yarn db:drop

db-shell:
	$(DOCKER_COMPOSE) exec courseted-db psql -U postgres -d courseted

# Seeding commands
seed:
	$(DOCKER_EXEC) yarn seed:advanced

seed-list:
	$(DOCKER_EXEC) yarn seed:list

seed-only:
	@if [ -z "$(seeders)" ]; then \
		echo "Usage: make seed-only seeders='Countries Users'"; \
		echo "Available seeders: run 'make seed-list' to see all available seeders"; \
	else \
		$(DOCKER_EXEC) yarn seed:only $(seeders); \
	fi