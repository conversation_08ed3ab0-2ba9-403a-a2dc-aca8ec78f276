#!/bin/sh

# Function to setup Node.js environment
setup_node_env() {
  # Try to load nvm if it exists
  if [ -f "$HOME/.nvm/nvm.sh" ]; then
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
  fi
  
  # Add common Node.js paths
  export PATH="$HOME/.nvm/versions/node/$(node --version 2>/dev/null)/bin:$HOME/.npm-global/bin:/usr/local/bin:$PATH"
  
  # Fallback: try to find node in common locations
  for path in /usr/local/bin /opt/homebrew/bin /usr/bin; do
    if [ -x "$path/node" ]; then
      export PATH="$path:$PATH"
      break
    fi
  done
}

setup_node_env
npm run lint