import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { TestUtils, TestUser } from "./test-utils";
import { UserRole } from "../src/types";

describe("UsersController (e2e)", () => {
  let app: INestApplication;
  let adminUser: TestUser;
  let instructorUser: TestUser;
  let studentUser: TestUser;

  beforeAll(async () => {
    app = await TestUtils.setupTestApp();
    await TestUtils.cleanupDatabase();
    await TestUtils.seedCountries();
  });

  afterAll(async () => {
    await TestUtils.teardownTestApp();
  });

  beforeEach(async () => {
    // Clean up users and create test users before each test
    await TestUtils.userRepository.delete({});
    adminUser = await TestUtils.createAdminUser();
    instructorUser = await TestUtils.createInstructorUser();
    studentUser = await TestUtils.createStudentUser();
  });

  describe("GET /v1/users", () => {
    it("should return all users for admin", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("data");
      expect(response.body.data.data).toBeInstanceOf(Array);
      expect(response.body.data.data.length).toBeGreaterThanOrEqual(3);
    });

    it("should fail for non-admin users", async () => {
      await request(app.getHttpServer())
        .get("/v1/users")
        .set(TestUtils.getAuthHeaders(studentUser.accessToken!))
        .expect(403);
    });

    it("should fail without authentication", async () => {
      await request(app.getHttpServer()).get("/v1/users").expect(401);
    });

    it("should support pagination", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users?limit=2&page=1")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body.data).toHaveProperty("data");
      expect(response.body.data).toHaveProperty("meta");
      expect(response.body.data.meta).toHaveProperty("totalItems");
      expect(response.body.data.meta).toHaveProperty("itemCount");
      expect(response.body.data.meta).toHaveProperty("itemsPerPage");
      expect(response.body.data.data.length).toBeLessThanOrEqual(2);
    });

    it("should support search functionality", async () => {
      const response = await request(app.getHttpServer())
        .get(`/v1/users?search=${adminUser.email}`)
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body.data.data.length).toBeGreaterThanOrEqual(1);
      const foundUser = response.body.data.data.find(
        (u: any) => u.email === adminUser.email,
      );
      expect(foundUser).toBeDefined();
    });

    it("should support sorting", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users?sortBy=email&sortOrder=asc")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body.data.data).toBeInstanceOf(Array);
      // Verify sorting (emails should be in ascending order)
      if (response.body.data.data.length > 1) {
        const emails = response.body.data.data.map((u: any) => u.email);
        const sortedEmails = [...emails].sort();
        expect(emails).toEqual(sortedEmails);
      }
    });
  });

  describe("GET /v1/users/instructors", () => {
    it("should return all instructors for admin", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users/instructors")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data.data).toBeInstanceOf(Array);

      // Check if the instructor user is in the response
      const instructor = response.body.data.data.find(
        (u: any) => u.email === instructorUser.email,
      );
      expect(instructor).toBeDefined();
      expect(instructor.role).toBe(UserRole.INSTRUCTOR);
    });

    it("should fail for non-admin users", async () => {
      await request(app.getHttpServer())
        .get("/v1/users/instructors")
        .set(TestUtils.getAuthHeaders(studentUser.accessToken!))
        .expect(403);
    });

    it("should not include non-instructor users", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users/instructors")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      // Verify all returned users are instructors
      response.body.data.data.forEach((user: any) => {
        expect(user.role).toBe(UserRole.INSTRUCTOR);
      });
    });
  });

  describe("GET /v1/users/students", () => {
    it("should return all students for admin", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users/students")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data.data).toBeInstanceOf(Array);

      // Check if the student user is in the response
      const student = response.body.data.data.find(
        (u: any) => u.email === studentUser.email,
      );
      expect(student).toBeDefined();
      expect(student.role).toBe(UserRole.STUDENT);
    });

    it("should fail for non-admin users", async () => {
      await request(app.getHttpServer())
        .get("/v1/users/students")
        .set(TestUtils.getAuthHeaders(instructorUser.accessToken!))
        .expect(403);
    });

    it("should not include non-student users", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users/students")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      // Verify all returned users are students
      response.body.data.data.forEach((user: any) => {
        expect(user.role).toBe(UserRole.STUDENT);
      });
    });
  });

  describe("GET /v1/users/profile", () => {
    it("should return user profile for admin", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users/profile")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("email", adminUser.email);
      expect(response.body.data).toHaveProperty("role", UserRole.ADMIN);
      expect(response.body.data).not.toHaveProperty("password");
    });

    it("should fail for non-admin users", async () => {
      await request(app.getHttpServer())
        .get("/v1/users/profile")
        .set(TestUtils.getAuthHeaders(studentUser.accessToken!))
        .expect(403);
    });

    it("should fail without authentication", async () => {
      await request(app.getHttpServer()).get("/v1/users/profile").expect(401);
    });
  });

  describe("POST /v1/users/profile", () => {
    const profileData = {
      firstName: "John",
      lastName: "Doe",
      dateOfBirth: "1990-01-01",
      bio: "Test bio",
      website: "https://example.com",
      location: "Test City",
    };

    it("should update user profile", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/users/profile")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(profileData)
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty(
        "firstName",
        profileData.firstName,
      );
      expect(response.body.data).toHaveProperty(
        "lastName",
        profileData.lastName,
      );
      expect(response.body.data).toHaveProperty("bio", profileData.bio);
    });

    it("should fail with invalid data", async () => {
      await request(app.getHttpServer())
        .post("/v1/users/profile")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          ...profileData,
          website: "invalid-url",
        })
        .expect(400);
    });

    it("should fail without authentication", async () => {
      await request(app.getHttpServer())
        .post("/v1/users/profile")
        .send(profileData)
        .expect(401);
    });

    it("should handle partial profile updates", async () => {
      const partialUpdate = {
        firstName: "Jane",
        bio: "Updated bio",
      };

      const response = await request(app.getHttpServer())
        .post("/v1/users/profile")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(partialUpdate)
        .expect(200);

      expect(response.body.data).toHaveProperty("firstName", "Jane");
      expect(response.body.data).toHaveProperty("bio", "Updated bio");
    });
  });

  describe("User data integrity", () => {
    it("should not expose sensitive data in user lists", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      response.body.data.data.forEach((user: any) => {
        expect(user).not.toHaveProperty("password");
        expect(user).not.toHaveProperty("otpCode");
        expect(user).not.toHaveProperty("passwordResetToken");
      });
    });

    it("should include required user fields", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/users")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      response.body.data.data.forEach((user: any) => {
        expect(user).toHaveProperty("id");
        expect(user).toHaveProperty("email");
        expect(user).toHaveProperty("role");
        expect(user).toHaveProperty("phoneNumber");
        expect(user).toHaveProperty("isEmailVerified");
        expect(user).toHaveProperty("isPhoneVerified");
        expect(user).toHaveProperty("createdAt");
        expect(user).toHaveProperty("updatedAt");
      });
    });

    it("should maintain role-based access control", async () => {
      // Test that each role can only access appropriate endpoints
      const endpoints = [
        { path: "/v1/users", allowedRoles: [UserRole.ADMIN] },
        { path: "/v1/users/instructors", allowedRoles: [UserRole.ADMIN] },
        { path: "/v1/users/students", allowedRoles: [UserRole.ADMIN] },
        { path: "/v1/users/profile", allowedRoles: [UserRole.ADMIN] },
      ];

      const users = [
        { user: adminUser, role: UserRole.ADMIN },
        { user: instructorUser, role: UserRole.INSTRUCTOR },
        { user: studentUser, role: UserRole.STUDENT },
      ];

      for (const endpoint of endpoints) {
        for (const { user, role } of users) {
          const expectedStatus = endpoint.allowedRoles.includes(role)
            ? 200
            : 403;

          await request(app.getHttpServer())
            .get(endpoint.path)
            .set(TestUtils.getAuthHeaders(user.accessToken!))
            .expect(expectedStatus);
        }
      }
    });
  });
});
