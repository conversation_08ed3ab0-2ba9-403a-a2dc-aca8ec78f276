import { config } from "dotenv";

// Load test environment variables
config({ path: ".env.test" });

// Set test environment
process.env.NODE_ENV = "test";

// Use SQLite for testing
process.env.DB_TYPE = "sqlite";
process.env.DB_DATABASE = ":memory:";
process.env.DB_SYNCHRONIZE = "true";

// Set JWT secret for testing
process.env.JWT_SECRET = "test-jwt-secret-key-for-testing-purposes-only";

// Redis configuration for testing
process.env.REDIS_HOST = "localhost";
process.env.REDIS_PORT = "6379";

// Twilio test credentials (use test values in proper format)
process.env.TWILIO_ACCOUNT_SID = "ACtest0123456789abcdef0123456789abcd";
process.env.TWILIO_AUTH_TOKEN = "test_auth_token_32_characters_long";
process.env.TWILIO_PHONE_NUMBER = "+***********"; // Twilio test number

// OAuth test credentials (dummy values for testing)
process.env.GOOGLE_CLIENT_ID = "test_google_client_id";
process.env.GOOGLE_CLIENT_SECRET = "test_google_client_secret";
process.env.FACEBOOK_APP_ID = "test_facebook_app_id";
process.env.FACEBOOK_APP_SECRET = "test_facebook_app_secret";
process.env.LINKEDIN_CLIENT_ID = "test_linkedin_client_id";
process.env.LINKEDIN_CLIENT_SECRET = "test_linkedin_client_secret";

// Mail configuration for testing (use log driver to avoid actual sending)
process.env.MAIL_DRIVER = "log";
process.env.MAIL_HOST = "localhost";
process.env.MAIL_PORT = "1025";
process.env.MAIL_USERNAME = "test";
process.env.MAIL_PASSWORD = "test";
process.env.MAIL_FROM = "<EMAIL>";

// Set test timeout
jest.setTimeout(30000);
