import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { TestUtils } from "./test-utils";

describe("CountryController (e2e)", () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await TestUtils.setupTestApp();
    await TestUtils.cleanupDatabase();
  });

  afterAll(async () => {
    await TestUtils.teardownTestApp();
  });

  beforeEach(async () => {
    // Clean up and seed countries before each test
    await TestUtils.countryRepository.delete({});
    await TestUtils.seedCountries();
  });

  describe("GET /v1/countries", () => {
    it("should return all countries", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);

      const country = response.body.data[0];
      expect(country).toHaveProperty("id");
      expect(country).toHaveProperty("name");
      expect(country).toHaveProperty("code");
      expect(country).toHaveProperty("iso3");
      expect(country).toHaveProperty("phoneCode");
      expect(country).toHaveProperty("flag");
    });

    it("should return empty array when no countries exist", async () => {
      // Clean up all countries
      await TestUtils.countryRepository.delete({});

      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBe(0);
    });

    it("should return countries with correct structure", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      const country = response.body.data.find((c: any) => c.code === "US");
      expect(country).toBeDefined();
      expect(country.name).toBe("United States");
      expect(country.code).toBe("US");
      expect(country.iso3).toBe("USA");
      expect(country.phoneCode).toBe("+1");
      expect(country.flag).toBe("🇺🇸");
    });
  });

  describe("GET /v1/countries/:code", () => {
    it("should return specific country by code", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries/US")
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("name", "United States");
      expect(response.body.data).toHaveProperty("code", "US");
      expect(response.body.data).toHaveProperty("iso3", "USA");
      expect(response.body.data).toHaveProperty("phoneCode", "+1");
      expect(response.body.data).toHaveProperty("flag", "🇺🇸");
    });

    it("should return country by lowercase code", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries/us")
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("code", "US");
    });

    it("should return 404 for non-existent country code", async () => {
      await request(app.getHttpServer()).get("/v1/countries/XX").expect(404);
    });

    it("should return 404 for invalid country code format", async () => {
      await request(app.getHttpServer())
        .get("/v1/countries/INVALID")
        .expect(404);
    });

    it("should handle special characters in country code", async () => {
      await request(app.getHttpServer()).get("/v1/countries/@#").expect(404);
    });

    it("should return Bangladesh country", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries/BD")
        .expect(200);

      expect(response.body.data).toHaveProperty("name", "Bangladesh");
      expect(response.body.data).toHaveProperty("code", "BD");
      expect(response.body.data).toHaveProperty("iso3", "BGD");
      expect(response.body.data).toHaveProperty("phoneCode", "+880");
    });

    it("should return United Kingdom country", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries/GB")
        .expect(200);

      expect(response.body.data).toHaveProperty("name", "United Kingdom");
      expect(response.body.data).toHaveProperty("code", "GB");
      expect(response.body.data).toHaveProperty("iso3", "GBR");
      expect(response.body.data).toHaveProperty("phoneCode", "+44");
    });
  });

  describe("Country data integrity", () => {
    it("should have unique country codes", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      const codes = response.body.data.map((country: any) => country.code);
      const uniqueCodes = new Set(codes);
      expect(codes.length).toBe(uniqueCodes.size);
    });

    it("should have valid phone code format", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      response.body.data.forEach((country: any) => {
        expect(country.phoneCode).toMatch(/^\+\d+$/);
      });
    });

    it("should have 2-letter country codes", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      response.body.data.forEach((country: any) => {
        expect(country.code).toMatch(/^[A-Z]{2}$/);
      });
    });

    it("should have 3-letter ISO3 codes", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      response.body.data.forEach((country: any) => {
        expect(country.iso3).toMatch(/^[A-Z]{3}$/);
      });
    });
  });
});
