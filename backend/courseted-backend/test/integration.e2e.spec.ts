import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { TestUtils, TestUser } from "./test-utils";
import { UserRole } from "../src/types";

describe("Integration Tests (e2e)", () => {
  let app: INestApplication;
  let adminUser: TestUser;
  let instructorUser: TestUser;
  let studentUser: TestUser;

  beforeAll(async () => {
    app = await TestUtils.setupTestApp();
    await TestUtils.cleanupDatabase();
    await TestUtils.seedCountries();
  });

  afterAll(async () => {
    await TestUtils.teardownTestApp();
  });

  beforeEach(async () => {
    // Clean up users before each test
    await TestUtils.userRepository.delete({});
    adminUser = await TestUtils.createAdminUser();
    instructorUser = await TestUtils.createInstructorUser();
    studentUser = await TestUtils.createStudentUser();
  });

  describe("Complete User Registration and Verification Flow", () => {
    it("should complete full user registration and verification process", async () => {
      const countries = await TestUtils.countryRepository.find();
      const country = countries[0];

      // Step 1: Register a new user
      const registrationData = {
        email: "<EMAIL>",
        password: "Password123!",
        phoneNumber: "+1555000001",
      };

      const registerResponse = await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(registrationData)
        .expect(201);

      expect(registerResponse.body).toHaveProperty("success", true);
      const userId = registerResponse.body.data.user.id;

      // Step 2: Simulate OTP verification
      // First update user with OTP
      await TestUtils.userRepository.update(userId, {
        otpCode: "123456",
        otpExpiresAt: new Date(Date.now() + 10 * 60 * 1000),
      });

      const otpResponse = await request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: registrationData.phoneNumber,
          code: "123456",
        })
        .expect(200);

      expect(otpResponse.body).toHaveProperty("success", true);

      // Step 3: Login with verified account
      const loginResponse = await request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: registrationData.email,
          password: registrationData.password,
        })
        .expect(200);

      expect(loginResponse.body.data).toHaveProperty("accessToken");
      const accessToken = loginResponse.body.data.accessToken;

      // Step 4: Access profile
      const profileResponse = await request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", `Bearer ${accessToken}`)
        .expect(200);

      expect(profileResponse.body.data.email).toBe(registrationData.email);
    });
  });

  describe("Cross-Service Integration", () => {
    it("should integrate SMS and Mail services", async () => {
      // Send an SMS
      const smsResponse = await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          to: "+*********0",
          body: "Integration test SMS",
          from: "+1987654321",
        })
        .expect(201);

      expect(smsResponse.body).toHaveProperty("success", true);

      // Send a welcome email
      const mailResponse = await request(app.getHttpServer())
        .post("/v1/mail/welcome")
        .send({
          email: "<EMAIL>",
          name: "Integration Test User",
        })
        .expect(201);

      expect(mailResponse.body).toHaveProperty("success", true);
    });

    it("should handle country and user relationship", async () => {
      // Get countries
      const countriesResponse = await request(app.getHttpServer())
        .get("/v1/countries")
        .expect(200);

      expect(countriesResponse.body.data.length).toBeGreaterThan(0);

      // Get users as admin
      const usersResponse = await request(app.getHttpServer())
        .get("/v1/users")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .expect(200);

      expect(usersResponse.body.data.data.length).toBeGreaterThanOrEqual(3);

      // Each user should have a country
      usersResponse.body.data.data.forEach((user: any) => {
        expect(user).toHaveProperty("country");
        expect(user.country).toHaveProperty("code");
      });
    });
  });

  describe("Role-Based Access Control Integration", () => {
    const testEndpoints = [
      { method: "GET", path: "/v1/users", allowedRoles: [UserRole.ADMIN] },
      {
        method: "GET",
        path: "/v1/users/instructors",
        allowedRoles: [UserRole.ADMIN],
      },
      {
        method: "GET",
        path: "/v1/users/students",
        allowedRoles: [UserRole.ADMIN],
      },
      {
        method: "GET",
        path: "/v1/users/profile",
        allowedRoles: [UserRole.ADMIN],
      },
    ];

    it("should enforce role-based access across all endpoints", async () => {
      const users = [
        { user: adminUser, role: UserRole.ADMIN },
        { user: instructorUser, role: UserRole.INSTRUCTOR },
        { user: studentUser, role: UserRole.STUDENT },
      ];

      for (const endpoint of testEndpoints) {
        for (const { user, role } of users) {
          const expectedStatus = endpoint.allowedRoles.includes(role)
            ? 200
            : 403;

          const response = await request(app.getHttpServer())
            [endpoint.method.toLowerCase() as "get"](endpoint.path)
            .set(TestUtils.getAuthHeaders(user.accessToken!));

          expect(response.status).toBe(expectedStatus);
        }
      }
    });
  });

  describe("Error Handling Integration", () => {
    it("should handle authentication errors consistently", async () => {
      const protectedEndpoints = [
        "/v1/auth/profile",
        "/v1/auth/context",
        "/v1/users",
        "/v1/users/profile",
        "/v1/sms/send",
      ];

      for (const endpoint of protectedEndpoints) {
        // No token
        await request(app.getHttpServer()).get(endpoint).expect(401);

        // Invalid token
        await request(app.getHttpServer())
          .get(endpoint)
          .set("Authorization", "Bearer invalid-token")
          .expect(401);
      }
    });

    it("should handle validation errors consistently", async () => {
      const invalidPayloads = [
        {
          endpoint: "/v1/auth/register",
          method: "post",
          payload: { email: "invalid-email" },
        },
        {
          endpoint: "/v1/auth/login",
          method: "post",
          payload: { email: "<EMAIL>" }, // Missing password
        },
        {
          endpoint: "/v1/sms/send",
          method: "post",
          payload: { to: "invalid-phone" },
          auth: true,
        },
        {
          endpoint: "/v1/mail/welcome",
          method: "post",
          payload: { email: "invalid-email" },
        },
      ];

      for (const { endpoint, method, payload, auth } of invalidPayloads) {
        const requestBuilder = request(app.getHttpServer())[method as "post"](
          endpoint,
        );

        if (auth) {
          requestBuilder.set(TestUtils.getAuthHeaders(adminUser.accessToken!));
        }

        await requestBuilder.send(payload).expect(400);
      }
    });
  });

  describe("Data Consistency Integration", () => {
    it("should maintain data integrity across services", async () => {
      // Create a user through registration
      const userData = {
        email: "<EMAIL>",
        password: "Password123!",
        phoneNumber: "+1555000002",
      };

      await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(userData)
        .expect(201);

      // Verify user exists in database
      const user = await TestUtils.userRepository.findOne({
        where: { email: userData.email },
        relations: ["country"],
      });

      expect(user).toBeDefined();
      expect(user?.email).toBe(userData.email);
      expect(user?.phoneNumber).toBe(userData.phoneNumber);
      expect(user?.country).toBeDefined();

      // Send SMS for this user
      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          to: userData.phoneNumber,
          body: "Test message for consistency",
          from: "+1987654321",
        })
        .expect(201);

      // Verify SMS record is created and linked
      const smsRecords = await TestUtils.smsRepository.find({
        where: { to: userData.phoneNumber },
        relations: ["sender"],
      });

      expect(smsRecords.length).toBeGreaterThan(0);
    });
  });

  describe("Performance and Concurrency", () => {
    it("should handle concurrent requests properly", async () => {
      const concurrentRequests = Array(20)
        .fill(null)
        .map((_, index) => {
          return request(app.getHttpServer()).get("/v1/countries").expect(200);
        });

      const responses = await Promise.all(concurrentRequests);

      responses.forEach((response) => {
        expect(response.body).toHaveProperty("success", true);
        expect(response.body.data).toBeInstanceOf(Array);
      });
    });

    it("should handle bulk operations efficiently", async () => {
      const bulkSmsData = {
        messages: Array(10)
          .fill(null)
          .map((_, index) => ({
            to: `+*********${index}`,
            body: `Bulk message ${index + 1}`,
            from: "+1987654321",
          })),
      };

      const response = await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(bulkSmsData)
        .expect(201);

      expect(response.body.data.count).toBe(10);
      expect(response.body.data.jobIds.length).toBe(10);
    });
  });

  describe("API Versioning Integration", () => {
    it("should support versioned endpoints consistently", async () => {
      const versionedEndpoints = [
        "/v1/auth/profile",
        "/v1/countries",
        "/v1/users",
        "/v1/sms/send",
        "/v1/mail/welcome",
      ];

      for (const endpoint of versionedEndpoints) {
        // Test that v1 endpoints work
        const response = await request(app.getHttpServer()).get(endpoint);

        // Should not be 404 (endpoint exists)
        expect(response.status).not.toBe(404);
      }
    });
  });
});
