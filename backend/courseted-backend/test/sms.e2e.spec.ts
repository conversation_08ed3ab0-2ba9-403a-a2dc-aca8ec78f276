import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { TestUtils, TestUser } from "./test-utils";

describe("SmsController (e2e)", () => {
  let app: INestApplication;
  let adminUser: TestUser;
  let studentUser: TestUser;

  beforeAll(async () => {
    app = await TestUtils.setupTestApp();
    await TestUtils.cleanupDatabase();
    await TestUtils.seedCountries();
  });

  afterAll(async () => {
    await TestUtils.teardownTestApp();
  });

  beforeEach(async () => {
    // Clean up users and SMS records before each test
    await TestUtils.smsRepository.delete({});
    await TestUtils.userRepository.delete({});
    adminUser = await TestUtils.createAdminUser();
    studentUser = await TestUtils.createStudentUser();
  });

  describe("POST /v1/sms/send", () => {
    const validSmsData = {
      to: "+1234567890",
      body: "Test SMS message",
      from: "+1987654321",
    };

    it("should send SMS with valid data", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(validSmsData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("jobId");
    });

    it("should fail without authentication", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .send(validSmsData)
        .expect(401);
    });

    it("should fail with invalid phone number format", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          ...validSmsData,
          to: "invalid-phone",
        })
        .expect(400);
    });

    it("should fail with empty message body", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          ...validSmsData,
          body: "",
        })
        .expect(400);
    });

    it("should fail with missing required fields", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          to: validSmsData.to,
          // Missing body
        })
        .expect(400);
    });

    it("should handle long message body", async () => {
      const longMessage = "A".repeat(1600); // SMS limit is usually 160 characters

      const response = await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          ...validSmsData,
          body: longMessage,
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("jobId");
    });

    it("should allow student users to send SMS", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(studentUser.accessToken!))
        .send(validSmsData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("jobId");
    });

    it("should handle international phone numbers", async () => {
      const internationalSmsData = {
        to: "+44123456789", // UK number
        body: "International SMS test",
        from: "+1987654321",
      };

      const response = await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(internationalSmsData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });

    it("should validate from number format", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({
          ...validSmsData,
          from: "invalid-from-number",
        })
        .expect(400);
    });
  });

  describe("POST /v1/sms/bulk", () => {
    const validBulkSmsData = {
      messages: [
        {
          to: "+1234567890",
          body: "Test SMS message 1",
          from: "+1987654321",
        },
        {
          to: "+1234567891",
          body: "Test SMS message 2",
          from: "+1987654321",
        },
        {
          to: "+1234567892",
          body: "Test SMS message 3",
          from: "+1987654321",
        },
      ],
    };

    it("should send bulk SMS with valid data", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(validBulkSmsData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("count", 3);
      expect(response.body.data).toHaveProperty("jobIds");
      expect(response.body.data.jobIds).toBeInstanceOf(Array);
      expect(response.body.data.jobIds.length).toBe(3);
    });

    it("should fail without authentication", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .send(validBulkSmsData)
        .expect(401);
    });

    it("should fail with empty messages array", async () => {
      await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send({ messages: [] })
        .expect(400);
    });

    it("should fail with invalid message data in array", async () => {
      const invalidBulkData = {
        messages: [
          {
            to: "+1234567890",
            body: "Valid message",
            from: "+1987654321",
          },
          {
            to: "invalid-phone",
            body: "Invalid message",
            from: "+1987654321",
          },
        ],
      };

      await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(invalidBulkData)
        .expect(400);
    });

    it("should handle large bulk SMS batches", async () => {
      const largeBatch = {
        messages: Array(50)
          .fill(null)
          .map((_, index) => ({
            to: `+123456789${index.toString().padStart(2, "0")}`,
            body: `Bulk message ${index + 1}`,
            from: "+1987654321",
          })),
      };

      const response = await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(largeBatch)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data.count).toBe(50);
      expect(response.body.data.jobIds.length).toBe(50);
    });

    it("should allow student users to send bulk SMS", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(studentUser.accessToken!))
        .send(validBulkSmsData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data.count).toBe(3);
    });

    it("should validate all messages before processing", async () => {
      const mixedValidityData = {
        messages: [
          {
            to: "+1234567890",
            body: "Valid message",
            from: "+1987654321",
          },
          {
            // Missing required fields
            to: "+1234567891",
            // Missing body and from
          },
        ],
      };

      await request(app.getHttpServer())
        .post("/v1/sms/bulk")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(mixedValidityData)
        .expect(400);
    });
  });

  describe("SMS data validation", () => {
    it("should validate phone number format properly", async () => {
      const invalidNumbers = [
        "",
        "123",
        "phone-number",
        "12345678901234567890", // Too long
        "+", // Just plus sign
        "************", // Wrong format
      ];

      for (const invalidNumber of invalidNumbers) {
        await request(app.getHttpServer())
          .post("/v1/sms/send")
          .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
          .send({
            to: invalidNumber,
            body: "Test message",
            from: "+1987654321",
          })
          .expect(400);
      }
    });

    it("should accept valid international phone number formats", async () => {
      const validNumbers = [
        "+1234567890",
        "+44123456789",
        "+880123456789",
        "+86123456789",
        "+91123456789",
      ];

      for (const validNumber of validNumbers) {
        const response = await request(app.getHttpServer())
          .post("/v1/sms/send")
          .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
          .send({
            to: validNumber,
            body: "Test message",
            from: "+1987654321",
          })
          .expect(201);

        expect(response.body).toHaveProperty("success", true);
      }
    });

    it("should handle special characters in message body", async () => {
      const specialCharMessages = [
        "Message with émojis 😀🎉",
        "Message with special chars: !@#$%^&*()",
        "Message with unicode: αβγδε",
        "Multi-line\nmessage\nwith\nbreaks",
      ];

      for (const message of specialCharMessages) {
        const response = await request(app.getHttpServer())
          .post("/v1/sms/send")
          .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
          .send({
            to: "+1234567890",
            body: message,
            from: "+1987654321",
          })
          .expect(201);

        expect(response.body).toHaveProperty("success", true);
      }
    });
  });

  describe("SMS queue integration", () => {
    it("should create SMS record in database when queuing", async () => {
      const smsData = {
        to: "+1234567890",
        body: "Database test message",
        from: "+1987654321",
      };

      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(smsData)
        .expect(201);

      // Check if SMS record was created in database
      const smsRecords = await TestUtils.smsRepository.find();
      expect(smsRecords.length).toBeGreaterThan(0);

      const smsRecord = smsRecords.find((record) => record.to === smsData.to);
      expect(smsRecord).toBeDefined();
      expect(smsRecord?.body).toBe(smsData.body);
      expect(smsRecord?.from).toBe(smsData.from);
    });

    it("should link SMS to sender user", async () => {
      const smsData = {
        to: "+1234567890",
        body: "User link test message",
        from: "+1987654321",
      };

      await request(app.getHttpServer())
        .post("/v1/sms/send")
        .set(TestUtils.getAuthHeaders(adminUser.accessToken!))
        .send(smsData)
        .expect(201);

      // Check if SMS is linked to the correct user
      const smsRecords = await TestUtils.smsRepository.find({
        relations: ["sender"],
      });
      const smsRecord = smsRecords.find((record) => record.to === smsData.to);

      expect(smsRecord?.sender?.id).toBe(adminUser.id);
    });
  });
});
