{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".*\\.e2e\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/../src/$1", "^@common/(.*)$": "<rootDir>/../src/common/$1", "^@modules/(.*)$": "<rootDir>/../src/modules/$1", "^@entities/(.*)$": "<rootDir>/../src/entities/$1", "^@entities$": "<rootDir>/../src/entities/index"}, "setupFilesAfterEnv": ["<rootDir>/setup.ts"], "testTimeout": 30000, "maxWorkers": 1, "detectOpenHandles": true, "forceExit": true}