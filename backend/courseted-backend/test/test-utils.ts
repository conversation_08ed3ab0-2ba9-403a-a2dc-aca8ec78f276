import { Test, TestingModule } from "@nestjs/testing";
import {
  INestApplication,
  ValidationPipe,
  VersioningType,
} from "@nestjs/common";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { JwtService } from "@nestjs/jwt";
import { Logger } from "nestjs-pino";
import { AppModule } from "../src/app.module";
import { getApplicationInstance } from "../src/app";
import { User, Country, Profile, SmsQueued } from "../src/entities";
import { UserRole } from "../src/types";
import { hashPassword } from "../src/utils";
import { CommonExceptionFilter } from "../src/common/filter/exception.filter";
import { ResponseInterceptor } from "../src/common/interceptors/response.interceptor";

export interface TestUser {
  id: number;
  email: string;
  phoneNumber: string;
  role: UserRole;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  accessToken?: string;
}

export class TestUtils {
  static app: INestApplication;
  static moduleFixture: TestingModule;
  static userRepository: Repository<User>;
  static countryRepository: Repository<Country>;
  static profileRepository: Repository<Profile>;
  static smsRepository: Repository<SmsQueued>;
  static jwtService: JwtService;

  static async setupTestApp(): Promise<INestApplication> {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    // Create app using the same module fixture to ensure repository consistency
    const app = moduleFixture.createNestApplication();

    // Apply the same configuration as production
    app.enableCors({
      origin: [
        "http://localhost:3000",
        "http://localhost:80",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:80",
        process.env.FRONTEND_URL,
      ].filter(Boolean),
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
      allowedHeaders: [
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Accept",
        "Origin",
      ],
      credentials: true,
    });

    app.enableVersioning({
      type: VersioningType.URI,
      prefix: "v",
      defaultVersion: "1",
    });

    app.useGlobalPipes(new ValidationPipe({ transform: true }));

    // Add global filter and interceptor for consistent response handling
    const logger = app.get(Logger);
    app.useGlobalFilters(new CommonExceptionFilter(logger));
    app.useGlobalInterceptors(new ResponseInterceptor());

    this.app = app;
    this.moduleFixture = moduleFixture;
    this.userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    this.countryRepository = moduleFixture.get<Repository<Country>>(
      getRepositoryToken(Country),
    );
    this.profileRepository = moduleFixture.get<Repository<Profile>>(
      getRepositoryToken(Profile),
    );
    this.smsRepository = moduleFixture.get<Repository<SmsQueued>>(
      getRepositoryToken(SmsQueued),
    );
    this.jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();
    return app;
  }

  static async cleanupDatabase(): Promise<void> {
    await this.smsRepository.delete({});
    await this.profileRepository.delete({});
    await this.userRepository.delete({});
    await this.countryRepository.delete({});
  }

  static async seedCountries(): Promise<Country[]> {
    const countries = [
      {
        name: "United States",
        code: "US",
        iso3: "USA",
        phoneCode: "+1",
        flag: "🇺🇸",
      },
      {
        name: "United Kingdom",
        code: "GB",
        iso3: "GBR",
        phoneCode: "+44",
        flag: "🇬🇧",
      },
      {
        name: "Bangladesh",
        code: "BD",
        iso3: "BGD",
        phoneCode: "+880",
        flag: "🇧🇩",
      },
    ];

    return await this.countryRepository.save(countries);
  }

  static async createTestUser(
    userData: Partial<User> = {},
    role: UserRole = UserRole.STUDENT,
  ): Promise<TestUser> {
    const countries = await this.countryRepository.find();
    let country = countries[0];

    if (!country) {
      const seededCountries = await this.seedCountries();
      country = seededCountries[0];
    }

    const defaultUser = {
      email: `test-${Date.now()}@example.com`,
      password: await hashPassword("Password123!"),
      phoneNumber: `+1234567${Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, "0")}`,
      role,
      isEmailVerified: true,
      isPhoneVerified: true,
      country,
      ...userData,
    };

    const savedUser = await this.userRepository.save(defaultUser);

    // Generate access token
    const payload = {
      sub: savedUser.id,
      email: savedUser.email,
      role: savedUser.role,
    };
    const accessToken = this.jwtService.sign(payload);

    return {
      id: savedUser.id,
      email: savedUser.email,
      phoneNumber: savedUser.phoneNumber,
      role: savedUser.role,
      isEmailVerified: savedUser.isEmailVerified,
      isPhoneVerified: savedUser.isPhoneVerified,
      accessToken,
    };
  }

  static async createAdminUser(): Promise<TestUser> {
    return this.createTestUser({ email: "<EMAIL>" }, UserRole.ADMIN);
  }

  static async createInstructorUser(): Promise<TestUser> {
    return this.createTestUser(
      { email: "<EMAIL>" },
      UserRole.INSTRUCTOR,
    );
  }

  static async createStudentUser(): Promise<TestUser> {
    return this.createTestUser({ email: "<EMAIL>" }, UserRole.STUDENT);
  }

  static getAuthHeaders(accessToken: string) {
    return {
      Authorization: `Bearer ${accessToken}`,
    };
  }

  static async teardownTestApp(): Promise<void> {
    await this.cleanupDatabase();
    await this.app.close();
  }
}
