import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { TestUtils, TestUser } from "./test-utils";

describe("AuthController (e2e)", () => {
  let app: INestApplication;
  let countries: any[];

  beforeAll(async () => {
    app = await TestUtils.setupTestApp();
    await TestUtils.cleanupDatabase();
    countries = await TestUtils.seedCountries();
  });

  afterAll(async () => {
    await TestUtils.teardownTestApp();
  });

  beforeEach(async () => {
    // Clean up database and reseed countries before each test
    await TestUtils.cleanupDatabase();
    countries = await TestUtils.seedCountries();
  });

  describe("POST /v1/auth/register", () => {
    let validRegistrationData: any;

    beforeEach(() => {
      validRegistrationData = {
        email: "<EMAIL>",
        password: "Password123!",
        phoneNumber: "+1234567890",
        countryId: countries[0].id, // Use the first seeded country
      };
    });

    it("should register a new user successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(validRegistrationData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("message");
      expect(response.body.data).toHaveProperty("user");
      expect(response.body.data.user.email).toBe(validRegistrationData.email);
      expect(response.body.data.user).not.toHaveProperty("password");
    });

    it("should fail with duplicate email", async () => {
      // Create user first
      await TestUtils.createTestUser({ email: validRegistrationData.email });

      await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(validRegistrationData)
        .expect(409);
    });

    it("should fail with invalid email format", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({ ...validRegistrationData, email: "invalid-email" })
        .expect(400);
    });

    it("should fail with weak password", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({ ...validRegistrationData, password: "123" })
        .expect(400);
    });

    it("should fail with invalid phone number", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({ ...validRegistrationData, phoneNumber: "invalid" })
        .expect(400);
    });
  });

  describe("POST /v1/auth/login", () => {
    let testUser: TestUser;

    beforeEach(async () => {
      testUser = await TestUtils.createTestUser({
        email: "<EMAIL>",
        isEmailVerified: true,
      });
    });

    it("should login with valid credentials", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("accessToken");
      expect(response.body.data).toHaveProperty("user");
      expect(response.body.data.user.email).toBe("<EMAIL>");
    });

    it("should fail with invalid email", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
        })
        .expect(401);
    });

    it("should fail with invalid password", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "wrongpassword",
        })
        .expect(401);
    });

    it("should fail with missing fields", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({ email: "<EMAIL>" })
        .expect(400);
    });
  });

  describe("GET /v1/auth/profile", () => {
    let testUser: TestUser;

    beforeEach(async () => {
      testUser = await TestUtils.createTestUser();
    });

    it("should get user profile with valid token", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set(TestUtils.getAuthHeaders(testUser.accessToken!))
        .expect(200);

      expect(response.body.data.email).toBe(testUser.email);
      expect(response.body.data).not.toHaveProperty("password");
    });

    it("should fail without authentication token", async () => {
      await request(app.getHttpServer()).get("/v1/auth/profile").expect(401);
    });

    it("should fail with invalid token", async () => {
      await request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", "Bearer invalid-token")
        .expect(401);
    });
  });

  describe("POST /v1/auth/verify-otp", () => {
    let testUser: TestUser;

    beforeEach(async () => {
      testUser = await TestUtils.createTestUser({
        isPhoneVerified: false,
      });

      // Manually update user with OTP data
      await TestUtils.userRepository.update(testUser.id, {
        otpCode: "123456",
        otpExpiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from now
      });
    });

    it("should verify OTP successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: testUser.phoneNumber,
          code: "123456",
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });

    it("should fail with invalid OTP", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: testUser.phoneNumber,
          code: "000000",
        })
        .expect(401);
    });

    it("should fail with non-existent phone number", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: "+9999999999",
          code: "123456",
        })
        .expect(404);
    });
  });

  describe("GET /v1/auth/context", () => {
    let testUser: TestUser;

    beforeEach(async () => {
      testUser = await TestUtils.createTestUser();
    });

    it("should get user context with valid token", async () => {
      const response = await request(app.getHttpServer())
        .get("/v1/auth/context")
        .set(TestUtils.getAuthHeaders(testUser.accessToken!))
        .expect(200);

      expect(response.body.data).toHaveProperty("user");
      expect(response.body.data).toHaveProperty("permissions");
      expect(response.body.data.user.email).toBe(testUser.email);
    });

    it("should fail without authentication", async () => {
      await request(app.getHttpServer()).get("/v1/auth/context").expect(401);
    });
  });

  describe("POST /v1/auth/forgot-password", () => {
    let testUser: TestUser;

    beforeEach(async () => {
      testUser = await TestUtils.createTestUser({
        email: "<EMAIL>",
      });
    });

    it("should send forgot password email", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/auth/forgot-password")
        .send({ email: "<EMAIL>" })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("message");
    });

    it("should handle non-existent email gracefully", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/auth/forgot-password")
        .send({ email: "<EMAIL>" })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });

    it("should fail with invalid email format", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/forgot-password")
        .send({ email: "invalid-email" })
        .expect(400);
    });
  });

  describe("POST /v1/auth/send-otp", () => {
    let testUser: TestUser;

    beforeEach(async () => {
      testUser = await TestUtils.createTestUser({
        isPhoneVerified: false,
      });
    });

    it("should send OTP to registered phone number", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/auth/send-otp")
        .send({ phoneNumber: testUser.phoneNumber })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body.data).toHaveProperty("message");
    });

    it("should fail with non-existent phone number", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/send-otp")
        .send({ phoneNumber: "+9999999999" })
        .expect(404);
    });

    it("should fail with invalid phone number format", async () => {
      await request(app.getHttpServer())
        .post("/v1/auth/send-otp")
        .send({ phoneNumber: "invalid" })
        .expect(400);
    });
  });
});
