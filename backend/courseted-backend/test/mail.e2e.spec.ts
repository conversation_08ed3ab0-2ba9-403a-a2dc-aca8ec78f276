import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { TestUtils } from "./test-utils";

describe("MailController (e2e)", () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await TestUtils.setupTestApp();
    await TestUtils.cleanupDatabase();
  });

  afterAll(async () => {
    await TestUtils.teardownTestApp();
  });

  describe("POST /v1/mail/welcome", () => {
    const validWelcomeData = {
      email: "<EMAIL>",
      name: "Test User",
    };

    it("should queue welcome email successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/welcome")
        .send(validWelcomeData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body).toHaveProperty(
        "message",
        "Welcome email queued successfully",
      );
    });

    it("should fail with invalid email format", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/welcome")
        .send({
          ...validWelcomeData,
          email: "invalid-email",
        })
        .expect(400);
    });

    it("should fail with missing email", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/welcome")
        .send({
          name: "Test User",
        })
        .expect(400);
    });

    it("should work without name (optional field)", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/welcome")
        .send({
          email: "<EMAIL>",
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });

    it("should handle multiple welcome emails", async () => {
      const emails = [
        { email: "<EMAIL>", name: "User 1" },
        { email: "<EMAIL>", name: "User 2" },
        { email: "<EMAIL>", name: "User 3" },
      ];

      for (const emailData of emails) {
        const response = await request(app.getHttpServer())
          .post("/v1/mail/welcome")
          .send(emailData)
          .expect(201);

        expect(response.body).toHaveProperty("success", true);
      }
    });
  });

  describe("POST /v1/mail/password-reset", () => {
    const validPasswordResetData = {
      email: "<EMAIL>",
      name: "Test User",
      token: "valid-reset-token-123",
    };

    it("should send password reset email successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/password-reset")
        .send(validPasswordResetData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body).toHaveProperty(
        "message",
        "Password reset email sent successfully",
      );
    });

    it("should fail with invalid email format", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/password-reset")
        .send({
          ...validPasswordResetData,
          email: "invalid-email",
        })
        .expect(400);
    });

    it("should fail with missing required fields", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/password-reset")
        .send({
          email: "<EMAIL>",
          // Missing token
        })
        .expect(400);
    });

    it("should work without name (optional field)", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/password-reset")
        .send({
          email: "<EMAIL>",
          token: "valid-reset-token-123",
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });

    it("should handle different token formats", async () => {
      const tokenFormats = [
        "simple-token",
        "jwt.token.format",
        "token_with_underscores",
        "token-with-123-numbers",
        "UPPERCASE-TOKEN",
      ];

      for (const token of tokenFormats) {
        const response = await request(app.getHttpServer())
          .post("/v1/mail/password-reset")
          .send({
            email: "<EMAIL>",
            token,
          })
          .expect(201);

        expect(response.body).toHaveProperty("success", true);
      }
    });
  });

  describe("POST /v1/mail/broadcast", () => {
    const validBroadcastData = {
      emails: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
      message: "This is a broadcast message",
    };

    it("should send broadcast email successfully", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/broadcast")
        .send(validBroadcastData)
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body).toHaveProperty("message");
      expect(response.body).toHaveProperty("recipients", 3);
    });

    it("should fail with empty email array", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/broadcast")
        .send({
          emails: [],
          message: "Test message",
        })
        .expect(400);
    });

    it("should fail with invalid email in array", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/broadcast")
        .send({
          emails: ["<EMAIL>", "invalid-email"],
          message: "Test message",
        })
        .expect(400);
    });

    it("should fail with missing message", async () => {
      await request(app.getHttpServer())
        .post("/v1/mail/broadcast")
        .send({
          emails: ["<EMAIL>"],
        })
        .expect(400);
    });

    it("should handle large recipient lists", async () => {
      const largeEmailList = Array(50)
        .fill(null)
        .map((_, index) => `user${index}@example.com`);

      const response = await request(app.getHttpServer())
        .post("/v1/mail/broadcast")
        .send({
          emails: largeEmailList,
          message: "Broadcast to many recipients",
        })
        .expect(201);

      expect(response.body).toHaveProperty("recipients", 50);
    });

    it("should handle duplicate emails in list", async () => {
      const emailsWithDuplicates = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>", // Duplicate
        "<EMAIL>",
        "<EMAIL>", // Another duplicate
      ];

      const response = await request(app.getHttpServer())
        .post("/v1/mail/broadcast")
        .send({
          emails: emailsWithDuplicates,
          message: "Broadcast with duplicates",
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });
  });

  describe("POST /v1/mail/queue-stats", () => {
    it("should return mail queue statistics", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/queue-stats")
        .expect(200);

      expect(response.body).toHaveProperty("success", true);
      expect(response.body).toHaveProperty(
        "message",
        "Queue statistics retrieved successfully",
      );
      expect(response.body).toHaveProperty("stats");
      expect(response.body.stats).toBeInstanceOf(Object);
    });

    it("should include expected statistics fields", async () => {
      const response = await request(app.getHttpServer())
        .post("/v1/mail/queue-stats")
        .expect(200);

      const stats = response.body.stats;

      // These fields may vary based on Bull queue implementation
      // but should be present in some form
      expect(stats).toBeDefined();
      expect(typeof stats).toBe("object");
    });
  });

  describe("Mail service integration", () => {
    it("should handle concurrent mail requests", async () => {
      const requests = Array(10)
        .fill(null)
        .map((_, index) =>
          request(app.getHttpServer())
            .post("/v1/mail/welcome")
            .send({
              email: `concurrent${index}@example.com`,
              name: `User ${index}`,
            }),
        );

      const responses = await Promise.all(requests);

      responses.forEach((response) => {
        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty("success", true);
      });
    });

    it("should validate email templates exist", async () => {
      // This test ensures the mail service can find and use templates
      const response = await request(app.getHttpServer())
        .post("/v1/mail/welcome")
        .send({
          email: "<EMAIL>",
          name: "Template Test User",
        })
        .expect(201);

      expect(response.body).toHaveProperty("success", true);
    });

    it("should handle malformed request bodies", async () => {
      const malformedBodies = [
        {},
        { invalid: "field" },
        { email: null },
        { email: "" },
      ];

      for (const body of malformedBodies) {
        await request(app.getHttpServer())
          .post("/v1/mail/welcome")
          .send(body)
          .expect(400);
      }
    });
  });

  describe("Email format validation", () => {
    it("should accept various valid email formats", async () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      for (const email of validEmails) {
        const response = await request(app.getHttpServer())
          .post("/v1/mail/welcome")
          .send({ email })
          .expect(201);

        expect(response.body).toHaveProperty("success", true);
      }
    });

    it("should reject invalid email formats", async () => {
      const invalidEmails = [
        "",
        "invalid",
        "@example.com",
        "test@",
        "<EMAIL>",
        "test@example",
        "test @example.com",
        "test@ex ample.com",
      ];

      for (const email of invalidEmails) {
        await request(app.getHttpServer())
          .post("/v1/mail/welcome")
          .send({ email })
          .expect(400);
      }
    });
  });
});
