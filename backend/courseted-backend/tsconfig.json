{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@": ["src"], "@entities/*": ["src/entities/*"], "@entities": ["src/entities"], "@modules": ["src/modules"], "@modules/*": ["src/modules/*"], "@config/*": ["src/config/*"], "@common/*": ["src/common/*"]}}}