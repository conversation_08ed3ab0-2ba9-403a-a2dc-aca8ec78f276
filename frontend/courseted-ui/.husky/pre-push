# BRANCH=$(git rev-parse --abbrev-ref HEAD)
# PROTECTED_BRANCH="main"

# if [ "$BRANCH" = "$PROTECTED_BRANCH" ]; then
#   echo "Direct push to $PROTECTED_BRANCH branch is not allowed!"
#   exit 1
# fi

# if [ "$BRANCH" != "$PROTECTED_BRANCH" ]; then
#   MERGE_TARGET=$(git for-each-ref --format='%(upstream:short)' "$(git symbolic-ref -q HEAD)")
#   if [[ $MERGE_TARGET == *"$PROTECTED_BRANCH"* ]]; then
#     if [[ ! "$BRANCH" =~ -RC$ ]]; then
#       echo "Only branches ending with -RC can be merged into $PROTECTED_BRANCH!"
#       exit 1
#     fi
#   fi
# fi

# exit 0

npm run lint
