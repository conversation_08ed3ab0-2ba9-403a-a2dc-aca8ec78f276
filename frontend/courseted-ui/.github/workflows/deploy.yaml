name: Deploy to dev

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy (e.g., CTED-30)'
        required: true
        type: string

jobs:
  deploy:
    name: Deploy to Dev Environment
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Get latest image tag
        id: get-image
        run: |
          BRANCH_NAME="${{ github.event.inputs.branch }}"
          BRANCH_NAME=$(echo $BRANCH_NAME | sed 's/\//-/g')
          IMAGE_TAG=$(aws ecr describe-images \
            --repository-name "courseted-ui" \
            --query 'sort_by(imageDetails,& imagePushedAt)[*].imageTags[*]' | grep -i "$BRANCH_NAME" | tail -1 | awk -F'["|"]' '{print $2}')
          if [ ! -n "${IMAGE_TAG}" ]
          then
            echo "Unable to pull image tag with prefix $BRANCH_NAME, please check branch or image $BRANCH_NAME exists"
            exit 1
          else
            echo "image_tag=${IMAGE_TAG}" >> $GITHUB_OUTPUT
            echo "Using image tag: ${IMAGE_TAG}"
          fi

      - name: Generate Dockerrun.aws.json
        run: |
          cat > Dockerrun.aws.json << EOF
          {
            "AWSEBDockerrunVersion": "1",
            "Image": {
              "Name": "${{ steps.login-ecr.outputs.registry }}/courseted-ui:${{ steps.get-image.outputs.image_tag }}",
              "Update": "true"
            },
            "Ports": [
              {
                "ContainerPort": 3000,
                "HostPort": 80
              }
            ]
          }
          EOF

      - name: Deploy to Elastic Beanstalk
        uses: einaregilsson/beanstalk-deploy@v21
        with:
          aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          application_name: courseted-ui
          environment_name: courseted-ui-dev
          version_label: ${{ github.event.inputs.branch }}-${{ github.run_id }}
          region: ${{ secrets.AWS_REGION }}
          deployment_package: Dockerrun.aws.json
          wait_for_environment_recovery: 30
          use_existing_version_if_available: true
