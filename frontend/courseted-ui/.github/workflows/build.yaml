name: CI/CD Pipeline

on:
  push:
    branches:
      - '**'

jobs:
  test:
    name: <PERSON><PERSON> and <PERSON> Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run type check
        run: npm run type-check

  build-and-push:
    name: Build and Push to ECR
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Generate unique image tag
        id: tag
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          BRANCH_NAME=$(echo $BRANCH_NAME | sed 's/\//-/g')
          COMMIT_HASH=$(echo $GITHUB_SHA | head -c 7)
          echo "tag=${BRANCH_NAME}-${COMMIT_HASH}" >> $GITHUB_OUTPUT
          echo "latest=latest" >> $GITHUB_OUTPUT

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.prod
          push: true
          platforms: linux/amd64
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/courseted/courseted-ui:${{ steps.tag.outputs.tag }}
            ${{ steps.login-ecr.outputs.registry }}/courseted/courseted-ui:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Update Elastic Beanstalk Dockerrun
        run: |
          # Create Dockerrun.aws.json with the new image tag (EB handles ECR auth automatically)
          cat > Dockerrun.aws.json << EOF
          {
            "AWSEBDockerrunVersion": "1",
            "Image": {
              "Name": "${{ steps.login-ecr.outputs.registry }}/courseted/courseted-ui:${{ steps.tag.outputs.tag }}",
              "Update": "true"
            },
            "Ports": [{
              "ContainerPort": 3000,
              "HostPort": 80
            }]
          }
          EOF


      - name: Deploy to Elastic Beanstalk
        uses: einaregilsson/beanstalk-deploy@v22
        with:
          aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          application_name: courseted-ui
          environment_name: courseted-ui-dev
          region: ${{ secrets.AWS_REGION }}
          version_label: ${{ steps.tag.outputs.tag }}
          deployment_package: Dockerrun.aws.json
          use_existing_version_if_available: true
          wait_for_environment_recovery: 300
