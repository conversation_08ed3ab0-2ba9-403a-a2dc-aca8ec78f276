# Courseted UI

A modern educational platform built with Next.js 14, TypeScript, Material-UI, and Redux Toolkit.

## Tech Stack

- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Material-UI** for components
- **Redux Toolkit** for state management
- **Docker** for deployment

## Installation

### Prerequisites

- Node.js 18.17+ (use `.nvmrc` file)
- npm 9+

### Setup

```bash
# Clone repository
git clone <repository-url>
cd courseted-ui

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to view the application.

## Scripts

```bash
npm run dev          # Development server
npm run build        # Production build
npm start            # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
npm run analyze      # Bundle analysis
```

## Docker

```bash
# Development
docker-compose -f docker-compose.dev.yml up

# Production
docker-compose -f docker-compose.prod.yml up
```

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable UI components
├── features/           # Feature-based modules (auth, courses, etc.)
├── hooks/              # Custom React hooks
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
└── theme/              # Material-UI theme configuration
```

## Performance

- Server-side rendering (SSR)
- Code splitting and lazy loading
- Image optimization
- Bundle analysis with `npm run analyze`
- Performance monitoring (see `OPTIMIZATION.md`)

## Documentation

- [Optimization Guide](OPTIMIZATION.md) - Performance and bundle optimization
- [Environment Setup](.env.example) - Required environment variables
