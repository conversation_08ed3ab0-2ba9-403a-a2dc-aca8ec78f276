events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Vite app (React/Vite)
    upstream vite_app {
        server courseted-ui-vite:3000;
    }

    # Next.js app
    upstream nextjs_app {
        server courseted-ui-next:3000;
    }

    # Vite development server
    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://vite_app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # WebSocket support for HMR
            proxy_set_header Connection "Upgrade";
            proxy_set_header Upgrade $http_upgrade;
        }
    }

    # Next.js development server
    server {
        listen 81;
        server_name localhost;

        location / {
            proxy_pass http://nextjs_app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # WebSocket support for HMR
            proxy_set_header Connection "Upgrade";
            proxy_set_header Upgrade $http_upgrade;
        }
    }
}
