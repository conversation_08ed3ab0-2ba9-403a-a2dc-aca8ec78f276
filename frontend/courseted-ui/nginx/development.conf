events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log debug;

    gzip on;
    gzip_types
        text/plain
        text/css
        text/javascript
        application/json
        application/javascript
        application/xml
        application/xml+rss
        text/xml;

    upstream courseted-ui-nextjs {
        server localhost:3001;
    }

    upstream courseted-api {
        server courseted-api:8000;
    }

    server {
        listen 80;
        server_name localhost;

        location /api/rest/ {
            rewrite ^/api/rest/(.*) /$1 break;
            proxy_pass http://courseted-api;
            proxy_redirect http://courseted-api/ /api/rest/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header jaeger-debug-id $http_jaeger_debug_id;
            proxy_set_header jaeger-baggage $http_jaeger_baggage;
            proxy_set_header traceparent $http_traceparent;
            add_header X-PREFIX-URL "/api/rest/";
        }

        # Route /nextjs to Next.js app
        location /nextjs/ {
            rewrite ^/nextjs/(.*) /$1 break;
            proxy_pass http://courseted-ui-nextjs;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header Accept-Encoding gzip;
        }

        # Default route to Next.js app
        location / {
            proxy_pass http://courseted-ui-nextjs;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header Accept-Encoding gzip;
        }
    }
}
