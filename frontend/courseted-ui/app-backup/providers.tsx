"use client";

import type { ReactNode } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Provider } from 'react-redux';
import theme from '@/theme';
import { store } from '@/app/store';
import ErrorBoundary from '../src/components/ErrorBoundary';

interface ProvidersProps {
  children: ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          {/* Prevent hydration mismatch by ensuring consistent rendering */}
          <div suppressHydrationWarning>
            {children}
          </div>
        </ThemeProvider>
      </Provider>
    </ErrorBoundary>
  );
}
