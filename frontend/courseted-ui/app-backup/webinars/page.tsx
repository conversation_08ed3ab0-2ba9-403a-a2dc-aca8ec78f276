import { FC } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Container,
  Grid2,
  Card,
  CardContent,
  CardMedia,
  Button,
  Stack,
  Chip,
} from "@mui/material";
import { CalendarToday, AccessTime, People, PlayCircleOutline } from "@mui/icons-material";
import MainLayout from "../../src/components/MainLayout";

// Enable ISR with revalidation every 1800 seconds (30 minutes)
export const revalidate = 1800;

// Generate metadata for the webinars page
export async function generateMetadata() {
  return {
    title: 'Webinars - CourseTed',
    description: 'Join our live and recorded webinars featuring industry experts. Learn from the best and stay updated with latest trends.',
    keywords: 'webinars, online events, live sessions, expert talks, professional development',
    openGraph: {
      title: 'Webinars - CourseTed',
      description: 'Join our live and recorded webinars featuring industry experts. Learn from the best and stay updated with latest trends.',
      type: 'website',
    },
  };
}

// Fetch webinars data
async function getWebinarsData() {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/webinars/featured`, {
    //   next: { revalidate: 1800 }
    // });

    // For now, return mock data
    return {
      upcomingWebinars: [
        {
          id: 1,
          title: "The Future of AI in Web Development",
          description: "Explore how artificial intelligence is revolutionizing web development and what it means for developers.",
          speaker: "Dr. Sarah Johnson",
          speakerTitle: "AI Research Director at TechCorp",
          speakerAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=center",
          date: "2024-02-15",
          time: "2:00 PM EST",
          duration: "60 minutes",
          attendees: 1250,
          image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=500&h=300&fit=crop",
          isLive: true,
          tags: ["AI", "Web Development", "Technology"]
        },
        {
          id: 2,
          title: "Building Scalable React Applications",
          description: "Learn best practices for building large-scale React applications that can grow with your business.",
          speaker: "Mike Chen",
          speakerTitle: "Senior Frontend Architect",
          speakerAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center",
          date: "2024-02-20",
          time: "3:00 PM EST",
          duration: "90 minutes",
          attendees: 890,
          image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=500&h=300&fit=crop",
          isLive: true,
          tags: ["React", "Frontend", "Architecture"]
        },
        {
          id: 3,
          title: "Data Science Career Roadmap 2024",
          description: "Navigate your path to becoming a successful data scientist with insights from industry veterans.",
          speaker: "Dr. Emily Rodriguez",
          speakerTitle: "Lead Data Scientist at DataCorp",
          speakerAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=center",
          date: "2024-02-25",
          time: "1:00 PM EST",
          duration: "75 minutes",
          attendees: 1450,
          image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop",
          isLive: false,
          tags: ["Data Science", "Career", "Analytics"]
        }
      ],
      stats: {
        totalWebinars: 150,
        totalAttendees: 45000,
        averageRating: 4.8,
        upcomingEvents: 12
      }
    };
  } catch (error) {
    console.error('Error fetching webinars data:', error);
    return {
      upcomingWebinars: [],
      stats: {
        totalWebinars: 150,
        totalAttendees: 45000,
        averageRating: 4.8,
        upcomingEvents: 12
      }
    };
  }
}

const WebinarsPage: FC = async () => {
  const data = await getWebinarsData();

  return (
    <MainLayout>
      <Container maxWidth="xl" sx={{ py: 8 }}>
        <Box textAlign="center" mb={6}>
          <Typography variant="h2" component="h1" gutterBottom fontWeight={600}>
            Expert Webinars
          </Typography>
          <Typography variant="h6" color="text.secondary" maxWidth={600} mx="auto">
            Join live sessions with industry experts and access our library of recorded webinars to accelerate your learning.
          </Typography>
        </Box>

        <Grid2 container spacing={4} mb={8}>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <PlayCircleOutline color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.totalWebinars}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Total Webinars
              </Typography>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <People color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.totalAttendees.toLocaleString()}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Total Attendees
              </Typography>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <CalendarToday color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.upcomingEvents}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Upcoming Events
              </Typography>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.averageRating}⭐
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Average Rating
              </Typography>
            </Card>
          </Grid2>
        </Grid2>

        <Typography variant="h4" component="h2" gutterBottom mb={4}>
          Upcoming Webinars
        </Typography>

        <Grid2 container spacing={4} mb={8}>
          {data.upcomingWebinars.map((webinar) => (
            <Grid2 key={webinar.id} size={{ xs: 12, md: 6, lg: 4 }}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box position="relative">
                  <CardMedia
                    component="img"
                    height="200"
                    image={webinar.image}
                    alt={webinar.title}
                  />
                  {webinar.isLive && (
                    <Chip
                      label="LIVE"
                      color="error"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        fontWeight: 'bold'
                      }}
                    />
                  )}
                </Box>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {webinar.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {webinar.description}
                  </Typography>

                  <Box display="flex" alignItems="center" gap={2} mb={2}>
                    {/* <Avatar src={webinar.speakerAvatar} alt={webinar.speaker} size="small" /> */}
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {webinar.speaker}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {webinar.speakerTitle}
                      </Typography>
                    </Box>
                  </Box>

                  <Stack spacing={1} mb={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <CalendarToday fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary">
                        {webinar.date} at {webinar.time}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                      <AccessTime fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary">
                        {webinar.duration}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1}>
                      <People fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary">
                        {webinar.attendees} registered
                      </Typography>
                    </Box>
                  </Stack>

                  <Box mb={3}>
                    <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                      {webinar.tags.map((tag) => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                    </Stack>
                  </Box>

                  <Button
                    variant="contained"
                    fullWidth
                    color={webinar.isLive ? "error" : "primary"}
                  >
                    {webinar.isLive ? "Join Live" : "Register Now"}
                  </Button>
                </CardContent>
              </Card>
            </Grid2>
          ))}
        </Grid2>

        <Card sx={{ p: 4, textAlign: 'center', bgcolor: 'primary.50' }}>
          <Typography variant="h5" gutterBottom>
            Never Miss a Webinar
          </Typography>
          <Typography variant="body1" color="text.secondary" mb={3}>
            Subscribe to our newsletter and get notified about upcoming webinars and exclusive content.
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
            <Button variant="contained" size="large">
              Subscribe to Newsletter
            </Button>
            <Button variant="outlined" size="large">
              Browse All Webinars
            </Button>
          </Stack>
        </Card>
      </Container>
    </MainLayout>
  );
};

export default WebinarsPage;
