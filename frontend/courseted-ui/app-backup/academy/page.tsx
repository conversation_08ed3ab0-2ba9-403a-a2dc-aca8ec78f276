import { FC } from "react";
import { Box, Typography, Container, Grid2, <PERSON>, CardContent, CardMedia } from "@mui/material";
import MainLayout from "../../src/components/MainLayout";

// Enable ISR with revalidation every 600 seconds (10 minutes)
export const revalidate = 600;

// Generate metadata for the academy page
export async function generateMetadata() {
  return {
    title: 'Academy - CourseTed',
    description: 'Explore our comprehensive academy programs and advanced learning paths designed for professional growth.',
    keywords: 'academy, professional development, advanced courses, certification programs',
    openGraph: {
      title: 'Academy - CourseTed',
      description: 'Explore our comprehensive academy programs and advanced learning paths designed for professional growth.',
      type: 'website',
    },
  };
}

// Fetch academy data
async function getAcademyData() {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/academy/programs`, {
    //   next: { revalidate: 600 }
    // });

    // For now, return mock data
    return {
      programs: [
        {
          id: 1,
          title: "Full Stack Development Bootcamp",
          description: "Comprehensive 12-week program covering modern web development technologies",
          duration: "12 weeks",
          level: "Intermediate",
          image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500&h=300&fit=crop",
          price: "$2,999"
        },
        {
          id: 2,
          title: "Data Science Mastery",
          description: "Advanced data science program with hands-on projects and real-world applications",
          duration: "16 weeks",
          level: "Advanced",
          image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop",
          price: "$3,499"
        },
        {
          id: 3,
          title: "UX/UI Design Professional",
          description: "Complete design program from fundamentals to advanced prototyping",
          duration: "10 weeks",
          level: "Beginner to Advanced",
          image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=500&h=300&fit=crop",
          price: "$2,499"
        }
      ],
      stats: {
        totalGraduates: 5000,
        employmentRate: 92,
        averageSalaryIncrease: 45
      }
    };
  } catch (error) {
    console.error('Error fetching academy data:', error);
    return {
      programs: [],
      stats: {
        totalGraduates: 5000,
        employmentRate: 92,
        averageSalaryIncrease: 45
      }
    };
  }
}

const AcademyPage: FC = async () => {
  const data = await getAcademyData();

  return (
    <MainLayout>
      <Container maxWidth="xl" sx={{ py: 8 }}>
        <Box textAlign="center" mb={6}>
          <Typography variant="h2" component="h1" gutterBottom fontWeight={600}>
            CourseTed Academy
          </Typography>
          <Typography variant="h6" color="text.secondary" maxWidth={600} mx="auto">
            Intensive programs designed to accelerate your career with industry-relevant skills and hands-on experience.
          </Typography>
        </Box>

        <Grid2 container spacing={4} mb={8}>
          <Grid2 size={{ xs: 12, md: 4 }}>
            <Box textAlign="center">
              <Typography variant="h3" color="primary.main" fontWeight={700}>
                {data.stats.totalGraduates.toLocaleString()}+
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Graduates
              </Typography>
            </Box>
          </Grid2>
          <Grid2 size={{ xs: 12, md: 4 }}>
            <Box textAlign="center">
              <Typography variant="h3" color="primary.main" fontWeight={700}>
                {data.stats.employmentRate}%
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Employment Rate
              </Typography>
            </Box>
          </Grid2>
          <Grid2 size={{ xs: 12, md: 4 }}>
            <Box textAlign="center">
              <Typography variant="h3" color="primary.main" fontWeight={700}>
                {data.stats.averageSalaryIncrease}%
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Average Salary Increase
              </Typography>
            </Box>
          </Grid2>
        </Grid2>

        <Typography variant="h4" component="h2" gutterBottom mb={4} textAlign="center">
          Our Programs
        </Typography>

        <Grid2 container spacing={4}>
          {data.programs.map((program) => (
            <Grid2 key={program.id} size={{ xs: 12, md: 6, lg: 4 }}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardMedia
                  component="img"
                  height="200"
                  image={program.image}
                  alt={program.title}
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {program.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {program.description}
                  </Typography>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                    <Typography variant="body2" color="primary.main">
                      {program.duration}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {program.level}
                    </Typography>
                  </Box>
                  <Typography variant="h6" color="primary.main" mt={2}>
                    {program.price}
                  </Typography>
                </CardContent>
              </Card>
            </Grid2>
          ))}
        </Grid2>
      </Container>
    </MainLayout>
  );
};

export default AcademyPage;
