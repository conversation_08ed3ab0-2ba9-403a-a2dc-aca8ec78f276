'use client';

// Force dynamic rendering to avoid prerendering issues
export const dynamic = 'force-dynamic';

import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, Stack, Avatar, InputBase } from '@mui/material';
import Button from '@/components/ui/Button';
import Icon from '@/components/ui/Icon';
import theme from '@/theme';

const CODE_LENGTH = 4;

const VerifyMail = () => {
  const [code, setCode] = useState(Array(CODE_LENGTH).fill(''));
  const inputsRef = useRef<Array<HTMLInputElement | null>>([]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, idx: number) => {
    const val = e.target.value.replace(/[^0-9]/g, '').slice(0, 1);
    const newCode = [...code];
    newCode[idx] = val;
    setCode(newCode);

    if (val && idx < CODE_LENGTH - 1) {
      inputsRef.current[idx + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>, idx: number) => {
    if (e.key === 'Backspace' && !code[idx] && idx > 0) {
      const newCode = [...code];
      newCode[idx - 1] = '';
      setCode(newCode);
      inputsRef.current[idx - 1]?.focus();
    }
  };

  useEffect(() => {
    inputsRef.current[0]?.focus()
  }, [])
  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: theme.palette.grey[100],
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Stack spacing={3} alignItems="center" sx={{ width: '100%', maxWidth: 440, height: '340px' }}>
        <Avatar
          sx={{
            bgcolor: 'green.100',
            width: 56,
            height: 56,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 1,
          }}
        >
          <Icon name="Mail2" sx={{ color: '#128A40', width: 28, height: 28, m: 0 }} />
        </Avatar>
        <Typography
          variant="header3xs"
          sx={{ color: 'grey.900', fontWeight: 700, textAlign: 'center' }}
        >
          Verify email
        </Typography>
        <Typography
          variant="textSm"
          sx={{
            color: 'grey.700',
            fontWeight: 400,
            textAlign: 'center',
            fontSize: 13,
            maxWidth: 320,
            lineHeight: 1.5,
          }}
        >
          A 4 digit code has been sent to your <br />
          <span style={{ color: theme.palette.grey[900], fontWeight: 500 }}>
            <EMAIL>
          </span>
          . Please enter the code below.
        </Typography>
        <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 1 }}>
          {code.map((digit, idx) => (
            <InputBase
              placeholder='-'
              key={idx}
              inputRef={el => (inputsRef.current[idx] = el)}
              value={digit}
              onChange={e => handleChange(e, idx)}
              onKeyDown={e => handleKeyDown(e, idx)}
              onFocus={e => e.target.placeholder = ''}
              onBlur={e => e.target.placeholder = '-'}
              inputProps={{
                maxLength: 1,
              }}
              sx={{
                width: '91px',
                height: '52px',
                borderRadius: '10px',
                backgroundColor: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: digit
                  ? `1px solid ${theme.palette.grey[900]}`
                  : `1px solid ${theme.palette.grey[300]}`,
                transition: 'border-color 0.2s ease',

                '& input': {
                  p: 0,
                  textAlign: 'center',
                  fontSize: 20,
                  fontWeight: 500,
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  outline: 'none',
                  background: 'transparent',
                  color: theme.palette.grey[900],
                },

                '&:focus-within': {
                  border: `1px solid ${theme.palette.green[600]}`,
                },
              }}
            />
          ))}
        </Stack>
        <Button
          fullWidth
          disabled={code.includes('')}
          sx={{
            mt: 2,
            borderRadius: '100px',
            fontWeight: 500,
            fontSize: 15,
            minHeight: 44,
            minWidth: 320,
          }}
        >
          Proceed
        </Button>
      </Stack>
    </Box>
  );
};

export default VerifyMail;
