'use client';

// Force dynamic rendering to avoid prerendering issues
export const dynamic = 'force-dynamic';

import { Box, Typography, Stack } from '@mui/material';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Icon from '@/components/ui/Icon';
import theme from '@/theme';
import React, { memo, useState } from 'react';

const ResetPassword = () => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleClickShowNewPassword = () => setShowNewPassword(show => !show);
  const handleClickShowConfirmPassword = () => setShowConfirmPassword(show => !show);

  const handleNewPasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewPassword(event.target.value);
    if (error) setError('');
  };

  const handleConfirmPasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(event.target.value);
    if (error) setError('');
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError('');
    setLoading(true);

    if (!newPassword || !confirmPassword) {
      setError('Both password fields are required.');
      setLoading(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match.');
      setLoading(false);
      return;
    }


    try {
      console.log('Attempting to reset password with:', newPassword);
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Password reset successful (simulated)');
    } catch (apiError: any) {
      setError(apiError.message || 'Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: theme.palette.grey[100],
        p: 4,
      }}
    >
      <Stack spacing={4} alignItems="center" sx={{ maxWidth: 400, width: '100%' }}>
        <Icon name="LogoBlack" width={'122px'} height={'20px'} />

        <Stack spacing={1.5} alignItems="center" sx={{ width: '100%' }}>
          <Typography
            variant="header3xs"
            sx={{ fontWeight: 'bold', textAlign: 'center', mt: '24px' }}
          >
            Create new password
          </Typography>
          <Typography variant="textSm" color="text.secondary" sx={{ textAlign: 'center' }}>
            Your new password must be different from previously used passwords.
          </Typography>
        </Stack>

        <Stack spacing={3} sx={{ width: '100%', mt: 2 }}>
          <Input
            label="New password"
            placeholder="Enter new password"
            fullWidth
            id="new-password"
            name="newPassword"
            type={showNewPassword ? 'text' : 'password'}
            value={newPassword}
            onChange={handleNewPasswordChange}
            autoComplete="new-password"
            required
            rightIcon={
              <Icon
                name={showNewPassword ? 'ViewOn' : 'ViewOff'}
                onClick={handleClickShowNewPassword}
                sx={{ cursor: 'pointer', color: theme.palette.grey[600] }}
              />
            }
          />
          <Input
            label="Confirm password"
            placeholder="Confirm new password"
            fullWidth
            id="confirm-password"
            name="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            value={confirmPassword}
            onChange={handleConfirmPasswordChange}
            autoComplete="new-password"
            required
            rightIcon={
              <Icon
                name={showConfirmPassword ? 'ViewOn' : 'ViewOff'}
                onClick={handleClickShowConfirmPassword}
                sx={{ cursor: 'pointer', color: theme.palette.grey[600] }}
              />
            }
            error={!!error}
            helperText={error}
          />
          <Button
            type="submit"
            fullWidth
            loading={loading}
            disabled={loading || !newPassword || !confirmPassword}
          >
            Reset Password
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};

export default memo(ResetPassword);
