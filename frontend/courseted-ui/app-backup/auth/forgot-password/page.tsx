'use client';

// Force dynamic rendering to avoid prerendering issues
export const dynamic = 'force-dynamic';

import { useState } from 'react';
import { Box, Typography, Stack, Avatar } from '@mui/material';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Icon from '@/components/ui/Icon';
import Link from '@/components/ui/Link';
import theme from '@/theme';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');

  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: theme.palette.grey[100],
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Stack spacing={4} alignItems="center" sx={{ width: '100%', maxWidth: 400 }}>
        <Avatar
          sx={{
            bgcolor: '#E9E1F9',
            width: 64,
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Icon name="SquareLock" sx={{ color: '#6228D6', width: 32, height: 32, m: 0 }} />
        </Avatar>
        <Stack spacing={2} alignItems="center" sx={{ width: '100%' }}>
          <Typography
            variant="header3xs"
            sx={{ color: 'grey.900', fontWeight: 700, textAlign: 'center' }}
          >
            Forgot password
          </Typography>
          <Typography variant="textSm" sx={{ color: 'grey.700', fontWeight: 400 }}>
            Please enter your registered email below to recover password
          </Typography>
        </Stack>
        <Stack spacing={3} sx={{ width: '100%' }}>
          <Input
            label="Email"
            placeholder="Enter email address"
            fullWidth
            id="email"
            name="email"
            autoComplete="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
          />
          <Button fullWidth>
            Send OTP
          </Button>
        </Stack>
        <Box sx={{ width: '100%', textAlign: 'center', mt: 1 }}>
          <Link
            href="/auth/login"
            customType="secondary"
            customSize="md"
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: 1,
              color: theme.palette.grey[900],
              fontWeight: 500,
              fontSize: 14,
              textDecoration: 'none',
              mt: 1,
            }}
          >
            <Icon name="ArrowLeft" sx={{ fontSize: 18, mr: 1 }} />
            Back to login
          </Link>
        </Box>
      </Stack>
    </Box>
  );
};

export default ForgotPassword;
