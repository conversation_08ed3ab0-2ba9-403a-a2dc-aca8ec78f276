"use client";
import { Box, Typography, Stack, IconButton, Grid } from '@mui/material';
import { palette } from '@/theme/palette';
import ClickableCourseCard from './components/ClickableCourseCard';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { SectionVisible, CourseCardData } from '@/types';
import 'swiper/css';
import 'swiper/css/navigation';

interface CourseGroupProps {
    groupKey: number;
    title: string;
    courses: CourseCardData[];
    sectionVisible: SectionVisible;
}

export default function CourseGroup({ groupKey, title, courses, sectionVisible }: CourseGroupProps) {
    console.log('Key:::: ', groupKey);

    // Transform course data to match CourseCard interface
    const transformCourse = (course: CourseCardData): CourseCardData | null => {
        if (!course) {
            console.error('Course is undefined or null');
            return null;
        }

        return {
            ...course,
            id: course?.id,
            // Keep price as string for display, CourseCard expects string
            price: course?.price || '$0',
        };
    };

    return (
        <Box my={0} position={'relative'}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h5" fontWeight={600} mb={0} fontSize={'32px !important'}>{title}</Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                    <IconButton
                        aria-label="arraw-left"
                        sx={{
                            backgroundColor: palette.grey[200],
                            position: 'relative',
                            '&:hover': {
                                backgroundColor: palette.grey[300],
                            },
                        }}
                        className="prev-btn"
                    >
                        <ChevronLeft className={`prev-btn-${groupKey}`} style={{ color: palette.grey[900] }} />
                    </IconButton>
                    <IconButton
                        aria-label="arrow-right"
                        sx={{
                            backgroundColor: palette.grey[200],
                            position: 'relative',
                            '&:hover': {
                                backgroundColor: palette.grey[300],
                            },
                        }}
                        className="next-btn"
                    >
                        <ChevronRight className={`next-btn-${groupKey}`} style={{ color: palette.grey[900] }} />
                    </IconButton>
                </Stack>
            </Stack>
            <Grid container spacing={4} position={'relative'}>
                <Grid item xs={12}>

                    <Swiper
                        modules={[Navigation]}
                        spaceBetween={20}
                        slidesPerView={1.1}
                        breakpoints={{
                            768: { slidesPerView: 2 },
                            1024: { slidesPerView: 3 },
                        }}
                        navigation={{
                            prevEl: `.prev-btn-${groupKey}`,
                            nextEl: `.next-btn-${groupKey}`,
                        }}
                        style={{ position: 'relative', width: '100%' }}
                    >
                        {
                            courses?.map((course, idx) => {
                                const transformedCourse = transformCourse(course);
                                if (!transformedCourse) {
                                    return null;
                                }

                                return (
                                    <SwiperSlide key={idx}>
                                        <Grid
                                            item
                                            key={idx}
                                            // xs={12}
                                            // sm={6}
                                            // md={4}
                                            // lg={3}
                                            display={'flex'}
                                            justifyContent={'center'}
                                            maxWidth={'600px'}
                                        >
                                            <ClickableCourseCard course={transformedCourse} sectionVisible={sectionVisible} />
                                        </Grid>

                                    </SwiperSlide>
                                )
                            })
                        }

                    </Swiper>
                </Grid>
            </Grid>
        </Box>
    );
};
