import { Typo<PERSON>, <PERSON>ack, TextField, Button, Divider } from '@mui/material';
import CourseGroup from '../CourseGroup';
import FilterListIcon from '@mui/icons-material/FilterList';
import SectionLayout from '@/components/landing/components/SectionLayout';
import { palette } from '@/theme/palette';
import { courses as mockCourses } from '@/data/mockData';
import { SectionVisible, CourseCardData } from '@/types';

interface DegreeSectionProps {
    courses?: CourseCardData[];
    sectionVisible: SectionVisible;
}

export default function DegreeSection({ courses = mockCourses, sectionVisible }: DegreeSectionProps) {

    return (
        <SectionLayout
            py={{ xs: 5, sm: 10 }}
            maxWidth="xl"
        >
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={8}>
                <Typography variant="h4" fontWeight={600} fontSize={'40px !important'}>Find the right degree for you</Typography>
                <Stack direction="row" spacing={2}>
                    <TextField size="small" placeholder="Search" />
                    <Button variant="outlined" startIcon={<FilterListIcon />}>Filter</Button>
                </Stack>
            </Stack>

            {/* <PopularCoursesSlider /> */}
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup groupKey={0} title="Popular courses" courses={courses} sectionVisible={sectionVisible} />
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup groupKey={1} title="Popular courses" courses={courses} sectionVisible={sectionVisible} />
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup groupKey={2} title="Popular courses" courses={courses} sectionVisible={sectionVisible} />
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup groupKey={3} title="Popular courses" courses={courses} sectionVisible={sectionVisible} />
        </SectionLayout>
    );
};
