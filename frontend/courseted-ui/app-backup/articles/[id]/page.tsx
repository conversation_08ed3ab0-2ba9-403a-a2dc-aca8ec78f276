import { FC } from "react";
import { notFound } from 'next/navigation';
import { Box, Typography, Card, CardContent, CardMedia, Chip, Stack } from '@mui/material';
import MainLayout from "@/components/MainLayout";
import { ArticlePageProps } from '@/types/pages/article.types';
import { ArticleDetail } from '@/types/content/article.types';

// Enable ISR with revalidation every 300 seconds (5 minutes)
export const revalidate = 300;

// Generate metadata for the page
export async function generateMetadata({ params }: ArticlePageProps) {
  const article = await getArticle(params?.id);
  
  if (!article) {
    return {
      title: 'Article Not Found - CourseTed',
    };
  }

  return {
    title: `${article.title} - CourseTed`,
    description: article.description,
    openGraph: {
      title: article.title,
      description: article.description,
      images: [article.image],
      type: 'article',
    },
  };
}

// Fetch individual article data
async function getArticle(id: string): Promise<ArticleDetail | null> {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch(`https://your-api.com/articles/${id}`, {
    //   next: { revalidate: 300 } // Revalidate every 5 minutes
    // });
    // 
    // if (!res.ok) {
    //   return null;
    // }
    // 
    // return res.json();
    
    // For now, return mock data
    const { courses } = await import('@/data/mockData');
    const article = courses.find(course => course.id.toString() === id);
    return article || null;
  } catch (error) {
    console.error('Error fetching article:', error);
    return null;
  }
}

// Generate static params for ISR
export async function generateStaticParams() {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch('https://your-api.com/articles');
    // const articles = await res.json();
    
    // For now, use mock data
    const { courses } = await import('@/data/mockData');
    
    return courses.slice(0, 10).map((course) => ({
      id: course.id.toString(),
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

const ArticlePage: FC<ArticlePageProps> = async ({ params }) => {
  const article = await getArticle(params.id);
  
  if (!article) {
    notFound();
  }
  
  return (
    <MainLayout>
      <Box maxWidth="lg" mx="auto" px={2} py={4}>
        <Card elevation={0} sx={{ borderRadius: 2 }}>
          <CardMedia
            component="img"
            height="400"
            image={article.image}
            alt={article.title}
            sx={{ objectFit: 'cover' }}
          />
          <CardContent sx={{ p: 4 }}>
            <Stack spacing={2} mb={3}>
              <Chip 
                label={article.category} 
                color="primary" 
                size="small" 
                sx={{ alignSelf: 'flex-start' }}
              />
              <Typography variant="h3" component="h1" fontWeight={600}>
                {article.title}
              </Typography>
              <Typography variant="body1" color="text.secondary" fontSize="1.1rem">
                {article.description}
              </Typography>
            </Stack>
            
            <Stack direction="row" spacing={4} flexWrap="wrap" gap={2}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Instructor
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {article.instructor}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Duration
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {article.duration}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Lessons
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {article.lessons}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Students
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {article.students}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Price
                </Typography>
                <Typography variant="body1" fontWeight={500} color="primary.main">
                  {article.price}
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Box>
    </MainLayout>
  );
};

export default ArticlePage;
