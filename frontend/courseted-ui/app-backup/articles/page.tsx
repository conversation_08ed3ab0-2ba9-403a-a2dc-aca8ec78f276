import SectionHeader from "./sections/SectionHeader";
import DegreeSection from "./sections/DegreeSection";
import CtaSection from "@/components/landing/sections/CtaSection";
import MainLayout from "@/components/MainLayout";

// Enable ISR with revalidation every 60 seconds
export const revalidate = 60;

// Generate metadata for the page
export async function generateMetadata() {
  return {
    title: 'Articles - CourseTed',
    description: 'Explore our collection of educational articles and courses',
    openGraph: {
      title: 'Articles - CourseTed',
      description: 'Explore our collection of educational articles and courses',
      type: 'website',
    },
  };
}

// Fetch data for the page (this will be cached and revalidated)
async function getArticlesData() {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch('https://your-api.com/articles', {
    //   next: { revalidate: 60 } // Revalidate every 60 seconds
    // });
    //
    // if (!res.ok) {
    //   throw new Error('Failed to fetch articles');
    // }
    //
    // return res.json();

    // For now, return mock data
    const { courses } = await import('@/data/mockData');
    return { courses };
  } catch (error) {
    console.error('Error fetching articles data:', error);
    // Return fallback data
    return { courses: [] };
  }
}

const ArticlesPage = async () => {
  const data = await getArticlesData();

  return (
    <MainLayout>
      <SectionHeader />
      <DegreeSection
        courses={data.courses}
        sectionVisible={{
          rating: false,
          category: true,
          price: false,
          lessons: false,
          duration: false,
          students: false,
          instructor: false,
          time: true,
          avatar: true,
          description: false,
        }}
      />
      <CtaSection />
    </MainLayout>
  );
}

export default ArticlesPage;
