import { Box, CircularProgress, Typography } from '@mui/material';
import MainLayout from '@/components/MainLayout';

export default function Loading() {
  return (
    <MainLayout>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="60vh"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          Loading articles...
        </Typography>
      </Box>
    </MainLayout>
  );
}
