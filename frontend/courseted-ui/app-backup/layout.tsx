import type { Metadata, Viewport } from 'next';
import type { ReactNode } from 'react';
import { Rethink_Sans, Public_Sans } from 'next/font/google';
import './globals.css';
import Providers from './providers';

const rethinkSans = Rethink_Sans({
  subsets: ['latin'],
  variable: '--font-rethink-sans',
});

const publicSans = Public_Sans({
  subsets: ['latin'],
  variable: '--font-public-sans',
});

export const viewport: Viewport = {
  themeColor: '#4285F4',
};

export const metadata: Metadata = {
  title: 'Courseted | Online Learning Platform',
  description: 'Courseted - Your platform for online learning and education. Discover courses that help you advance your skills and career.',
  keywords: 'online courses, education, e-learning, skills development, professional courses, learning platform',
  authors: [{ name: 'Courseted' }],
  robots: 'index, follow',
  alternates: {
    canonical: 'https://courseted.com',
  },
  openGraph: {
    type: 'website',
    url: 'https://courseted.com',
    title: 'Courseted | Online Learning Platform',
    description: 'Discover and learn from our wide range of online courses designed to help you advance your skills and career.',
    images: [
      {
        url: 'https://courseted.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Courseted - Online Learning Platform',
      },
    ],
    siteName: 'Courseted',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@courseted',
    title: 'Courseted | Online Learning Platform',
    description: 'Discover and learn from our wide range of online courses designed to help you advance your skills and career.',
    images: ['https://courseted.com/twitter-image.jpg'],
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'black',
    title: 'Courseted',
  },
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
  },
  manifest: '/site.webmanifest',
};

interface RootLayoutProps {
  children: ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" className={`${rethinkSans.variable} ${publicSans.variable}`}>
      <body suppressHydrationWarning={true}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
