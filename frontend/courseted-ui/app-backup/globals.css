:root {
  --font-rethink-sans: 'Rethink Sans', system-ui, sans-serif;
  --font-public-sans: 'Public Sans', system-ui, sans-serif;

  /* Light theme colors */
  --background: #ffffff;
  --foreground: #0f0f23;
  --primary: #4285f4;
  --primary-foreground: #ffffff;
  --secondary: #f1f3f4;
  --secondary-foreground: #5f6368;
  --muted: #f8f9fa;
  --muted-foreground: #6b7280;
  --border: #e5e7eb;
  --input: #ffffff;
  --ring: #4285f4;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f0f23;
    --foreground: #ffffff;
    --primary: #4285f4;
    --primary-foreground: #ffffff;
    --secondary: #1f2937;
    --secondary-foreground: #d1d5db;
    --muted: #374151;
    --muted-foreground: #9ca3af;
    --border: #374151;
    --input: #1f2937;
    --ring: #4285f4;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-public-sans);
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  font-family: inherit;
}

input,
textarea {
  font-family: inherit;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Responsive breakpoints */
:root {
  --breakpoint-xs: 0px;
  --breakpoint-sm: 600px;
  --breakpoint-md: 900px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1536px;
}

/* Mobile-first responsive utilities */
.container-responsive {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 600px) {
  .container-responsive {
    padding: 0 24px;
  }
}

@media (min-width: 900px) {
  .container-responsive {
    padding: 0 32px;
  }
}

@media (min-width: 1200px) {
  .container-responsive {
    max-width: 1200px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1400px;
  }
}

/* Responsive text utilities */
.text-responsive-xs {
  font-size: 11px;
  line-height: 16px;
}

.text-responsive-sm {
  font-size: 13px;
  line-height: 18px;
}

.text-responsive-base {
  font-size: 14px;
  line-height: 20px;
}

.text-responsive-lg {
  font-size: 16px;
  line-height: 24px;
}

.text-responsive-xl {
  font-size: 18px;
  line-height: 28px;
}

@media (min-width: 600px) {
  .text-responsive-xs {
    font-size: 12px;
    line-height: 18px;
  }

  .text-responsive-sm {
    font-size: 14px;
    line-height: 20px;
  }

  .text-responsive-base {
    font-size: 16px;
    line-height: 24px;
  }

  .text-responsive-lg {
    font-size: 18px;
    line-height: 28px;
  }

  .text-responsive-xl {
    font-size: 20px;
    line-height: 30px;
  }
}

/* Responsive spacing utilities */
.spacing-xs { margin: 4px; }
.spacing-sm { margin: 8px; }
.spacing-md { margin: 16px; }
.spacing-lg { margin: 24px; }
.spacing-xl { margin: 32px; }

.padding-xs { padding: 4px; }
.padding-sm { padding: 8px; }
.padding-md { padding: 16px; }
.padding-lg { padding: 24px; }
.padding-xl { padding: 32px; }

@media (min-width: 600px) {
  .spacing-md { margin: 20px; }
  .spacing-lg { margin: 32px; }
  .spacing-xl { margin: 48px; }

  .padding-md { padding: 20px; }
  .padding-lg { padding: 32px; }
  .padding-xl { padding: 48px; }
}

/* Responsive display utilities */
.hide-mobile {
  display: none;
}

.hide-tablet {
  display: block;
}

.hide-desktop {
  display: block;
}

@media (min-width: 600px) {
  .hide-mobile {
    display: block;
  }

  .hide-tablet {
    display: none;
  }

  .show-tablet-up {
    display: block;
  }
}

@media (min-width: 900px) {
  .hide-tablet {
    display: block;
  }

  .hide-desktop {
    display: none;
  }

  .show-desktop-up {
    display: block;
  }
}

/* Touch-friendly interactive elements */
@media (max-width: 599px) {
  button,
  .button,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  input,
  textarea,
  select {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Responsive images */
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Responsive grid utilities */
.grid-responsive {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 600px) {
  .grid-responsive {
    gap: 24px;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 900px) {
  .grid-responsive {
    gap: 32px;
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Responsive flex utilities */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media (min-width: 600px) {
  .flex-responsive {
    flex-direction: row;
    gap: 24px;
  }
}

/* Safe area for mobile devices with notches */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
}

/* Zoom SDK styles */
#zmmtg-root {
  width: 100%;
  height: 100%;
}

.zoom-meeting-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.zoom-meeting-container > div {
  width: 100% !important;
  height: 100% !important;
}
