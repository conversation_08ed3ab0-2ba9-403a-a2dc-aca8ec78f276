import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'instructor';
  avatar?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResult {
  user: User | null;
  isAuthenticated: boolean;
}

/**
 * Server-side authentication check
 * This function runs on the server and checks if the user is authenticated
 */
export async function getServerAuth(): Promise<AuthResult> {
  try {
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('accessToken')?.value;
    const refreshToken = cookieStore.get('refreshToken')?.value;

    if (!accessToken) {
      return { user: null, isAuthenticated: false };
    }

    // Verify the token with your backend API
    // Replace with your actual API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/auth/verify`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Don't cache auth requests
    });

    if (!response.ok) {
      // Try to refresh the token if we have a refresh token
      if (refreshToken) {
        const refreshResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/auth/refresh`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refreshToken }),
          cache: 'no-store',
        });

        if (refreshResponse.ok) {
          const refreshData = await refreshResponse.json();
          // In a real app, you'd set the new tokens in cookies here
          // For now, we'll just return the user data
          return {
            user: refreshData.user,
            isAuthenticated: true,
          };
        }
      }

      return { user: null, isAuthenticated: false };
    }

    const data = await response.json();
    return {
      user: data.user,
      isAuthenticated: true,
    };
  } catch (error) {
    console.error('Server auth check failed:', error);
    return { user: null, isAuthenticated: false };
  }
}

/**
 * Server-side authentication check with redirect
 * Use this in pages that require authentication
 */
export async function requireAuth(redirectTo: string = '/auth/login'): Promise<User> {
  const { user, isAuthenticated } = await getServerAuth();
  
  if (!isAuthenticated || !user) {
    redirect(redirectTo);
  }
  
  return user;
}

/**
 * Server-side role-based authentication check
 * Use this in pages that require specific roles
 */
export async function requireRole(
  allowedRoles: string[], 
  redirectTo: string = '/auth/login'
): Promise<User> {
  const user = await requireAuth(redirectTo);
  
  if (!allowedRoles.includes(user.role)) {
    redirect('/unauthorized');
  }
  
  return user;
}

/**
 * Mock authentication for development
 * Remove this in production and use real API calls
 */
export async function getMockAuth(): Promise<AuthResult> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Mock user data - in production, this would come from your API
  const mockUser: User = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=center',
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  };

  return {
    user: mockUser,
    isAuthenticated: true,
  };
}

/**
 * Get user preferences from server
 */
export async function getUserPreferences() {
  try {
    // Replace with your actual API endpoint
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/users/${userId}/preferences`, {
    //   cache: 'no-store',
    // });
    
    // Mock preferences for now
    return {
      theme: 'light',
      language: 'en',
      notifications: {
        email: true,
        push: true,
        sms: false,
      },
      privacy: {
        profileVisible: true,
        showEmail: false,
        showPhone: false,
      },
    };
  } catch (error) {
    console.error('Failed to fetch user preferences:', error);
    return null;
  }
}

/**
 * Get user dashboard data
 */
export async function getDashboardData() {
  try {
    // Replace with your actual API endpoints
    // const [coursesRes, progressRes, statsRes] = await Promise.all([
    //   fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/users/${userId}/courses`),
    //   fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/users/${userId}/progress`),
    //   fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/users/${userId}/stats`),
    // ]);
    
    // Mock dashboard data for now
    return {
      enrolledCourses: [
        {
          id: '1',
          title: 'React Advanced Patterns',
          progress: 75,
          lastAccessed: '2024-01-15T10:30:00Z',
          instructor: 'Jane Smith',
          thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop',
        },
        {
          id: '2',
          title: 'Node.js Masterclass',
          progress: 45,
          lastAccessed: '2024-01-14T15:20:00Z',
          instructor: 'Mike Johnson',
          thumbnail: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=300&h=200&fit=crop',
        },
      ],
      stats: {
        totalCourses: 12,
        completedCourses: 8,
        totalHours: 156,
        certificatesEarned: 6,
      },
      recentActivity: [
        {
          id: '1',
          type: 'course_completed',
          title: 'Completed "JavaScript Fundamentals"',
          date: '2024-01-14T09:00:00Z',
        },
        {
          id: '2',
          type: 'certificate_earned',
          title: 'Earned certificate for "CSS Grid Layout"',
          date: '2024-01-13T14:30:00Z',
        },
      ],
    };
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    return null;
  }
}
