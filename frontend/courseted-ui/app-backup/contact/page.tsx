import { FC } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Container,
  Grid2,
  Card,
  TextField,
  Button,
  Stack,
} from "@mui/material";
import { Email, Phone, LocationOn, AccessTime } from "@mui/icons-material";
import MainLayout from "../../src/components/MainLayout";

// Enable ISR with revalidation every 3600 seconds (1 hour)
export const revalidate = 3600;

// Generate metadata for the contact page
export async function generateMetadata() {
  return {
    title: 'Contact Us - CourseTed',
    description: 'Get in touch with CourseTed. Find our contact information, office locations, and send us a message.',
    keywords: 'contact, support, help, office locations, customer service',
    openGraph: {
      title: 'Contact Us - CourseTed',
      description: 'Get in touch with CourseTed. Find our contact information, office locations, and send us a message.',
      type: 'website',
    },
  };
}

// Fetch contact data
async function getContactData() {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/contact/info`, {
    //   next: { revalidate: 3600 }
    // });

    // For now, return mock data
    return {
      offices: [
        {
          id: 1,
          name: "Headquarters",
          address: "123 Education Street, Learning City, LC 12345",
          phone: "+****************",
          email: "<EMAIL>",
          hours: "Mon-Fri: 9AM-6PM"
        },
        {
          id: 2,
          name: "European Office",
          address: "456 Knowledge Avenue, Tech City, TC 67890",
          phone: "+44 20 1234 5678",
          email: "<EMAIL>",
          hours: "Mon-Fri: 9AM-5PM GMT"
        }
      ],
      supportEmail: "<EMAIL>",
      salesEmail: "<EMAIL>"
    };
  } catch (error) {
    console.error('Error fetching contact data:', error);
    return {
      offices: [],
      supportEmail: "<EMAIL>",
      salesEmail: "<EMAIL>"
    };
  }
}

const ContactPage: FC = async () => {
  const data = await getContactData();

  return (
    <MainLayout>
      <Container maxWidth="xl" sx={{ py: 8 }}>
        <Box textAlign="center" mb={6}>
          <Typography variant="h2" component="h1" gutterBottom fontWeight={600}>
            Contact Us
          </Typography>
          <Typography variant="h6" color="text.secondary" maxWidth={600} mx="auto">
            We'd love to hear from you. Get in touch with our team for any questions or support.
          </Typography>
        </Box>

        <Grid2 container spacing={6}>
          <Grid2 size={{ xs: 12, md: 8 }}>
            <Card sx={{ p: 4 }}>
              <Typography variant="h5" gutterBottom mb={3}>
                Send us a Message
              </Typography>
              <Stack spacing={3}>
                <Grid2 container spacing={2}>
                  <Grid2 size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="First Name"
                      variant="outlined"
                      required
                    />
                  </Grid2>
                  <Grid2 size={{ xs: 12, sm: 6 }}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      variant="outlined"
                      required
                    />
                  </Grid2>
                </Grid2>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  variant="outlined"
                  required
                />
                <TextField
                  fullWidth
                  label="Subject"
                  variant="outlined"
                  required
                />
                <TextField
                  fullWidth
                  label="Message"
                  multiline
                  rows={6}
                  variant="outlined"
                  required
                />
                <Button
                  variant="contained"
                  size="large"
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Send Message
                </Button>
              </Stack>
            </Card>
          </Grid2>

          <Grid2 size={{ xs: 12, md: 4 }}>
            <Stack spacing={4}>
              <Card sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Quick Contact
                </Typography>
                <Stack spacing={2}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Email color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        General Inquiries
                      </Typography>
                      <Typography variant="body1">
                        {data.supportEmail}
                      </Typography>
                    </Box>
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Email color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Sales
                      </Typography>
                      <Typography variant="body1">
                        {data.salesEmail}
                      </Typography>
                    </Box>
                  </Box>
                </Stack>
              </Card>

              {data.offices.map((office) => (
                <Card key={office.id} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    {office.name}
                  </Typography>
                  <Stack spacing={2}>
                    <Box display="flex" alignItems="flex-start" gap={2}>
                      <LocationOn color="primary" />
                      <Typography variant="body2">
                        {office.address}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Phone color="primary" />
                      <Typography variant="body2">
                        {office.phone}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Email color="primary" />
                      <Typography variant="body2">
                        {office.email}
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={2}>
                      <AccessTime color="primary" />
                      <Typography variant="body2">
                        {office.hours}
                      </Typography>
                    </Box>
                  </Stack>
                </Card>
              ))}
            </Stack>
          </Grid2>
        </Grid2>
      </Container>
    </MainLayout>
  );
};

export default ContactPage;
