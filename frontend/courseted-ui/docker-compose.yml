services:
  courseted-ui:
    ports:
      - "3000:3000"
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./src:/app/src
      - ./app:/app/app
      - ./public:/app/public
      - ./next.config.js:/app/next.config.js
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.next.json:/app/tsconfig.next.json
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./eslint.config.js:/app/eslint.config.js
      - ./.env:/app/.env
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
    env_file:
      - .env
    networks:
      - courseted
    restart: unless-stopped

networks:
  courseted:
    driver: bridge
