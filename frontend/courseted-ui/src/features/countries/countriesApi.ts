import { baseApi } from '../../api/baseApi';
import type { ApiResponse } from '../../types/common.types';
import type { Country } from '../../types/countries.types';

export const countriesApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    // Get all countries
    getCountries: build.query<ApiResponse<Country[]>, void>({
      query: () => ({
        url: '/v1/countries',
        method: 'GET',
      }),
      providesTags: ['Country'],
    }),

    // Get country by code
    getCountryByCode: build.query<ApiResponse<Country>, string>({
      query: (code) => ({
        url: `/v1/countries/${code}`,
        method: 'GET',
      }),
      providesTags: (_, __, code) => [{ type: 'Country', id: code }],
    }),
  }),
});

export const {
  useGetCountriesQuery,
  useGetCountryByCodeQuery,
} = countriesApi;
