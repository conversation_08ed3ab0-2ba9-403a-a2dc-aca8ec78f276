import { baseApi } from '../../api/baseApi';
import type { User, UpdateUserRequest } from '../../types/auth.types';
import type { ApiResponse, PaginationParams } from '../../types/common.types';

export const usersApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    // Get all users (admin only)
    getUsers: build.query<ApiResponse<User[]>, PaginationParams>({
      query: (params) => ({
        url: '/v1/users',
        method: 'GET',
        params,
      }),
      providesTags: ['User'],
    }),

    // Get user by ID
    getUser: build.query<ApiResponse<User>, string>({
      query: (id) => ({
        url: `/v1/users/${id}`,
        method: 'GET',
      }),
      providesTags: (_, __, id) => [{ type: 'User', id }],
    }),

    // Update user profile
    updateUser: build.mutation<ApiResponse<User>, { id: string; data: UpdateUserRequest }>({
      query: ({ id, data }) => ({
        url: `/v1/users/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: (_, __, { id }) => [{ type: 'User', id }, 'User', 'Auth'],
    }),

    // Delete user (admin only)
    deleteUser: build.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/v1/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_, __, id) => [{ type: 'User', id }, 'User'],
    }),

    // Upload avatar
    uploadAvatar: build.mutation<ApiResponse<{ avatar: string }>, FormData>({
      query: (formData) => ({
        url: '/v1/users/avatar',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['User', 'Auth'],
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserQuery,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useUploadAvatarMutation,
} = usersApi;
