import React from 'react';
import { Container, ContainerProps, useTheme, useMediaQuery } from '@mui/material';

interface ResponsiveContainerProps extends ContainerProps {
  children: React.ReactNode;
  mobilePadding?: number | string;
  tabletPadding?: number | string;
  desktopPadding?: number | string;
  fullWidthOnMobile?: boolean;
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  mobilePadding = 2,
  tabletPadding = 3,
  desktopPadding = 4,
  fullWidthOnMobile = false,
  sx,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const responsiveSx = {
    px: isMobile ? mobilePadding : isTablet ? tabletPadding : desktopPadding,
    ...(fullWidthOnMobile && isMobile && { maxWidth: '100% !important', px: mobilePadding }),
    ...sx,
  };

  return (
    <Container
      maxWidth={fullWidthOnMobile && isMobile ? false : props.maxWidth || 'lg'}
      sx={responsiveSx}
      {...props}
    >
      {children}
    </Container>
  );
};

export default ResponsiveContainer;
