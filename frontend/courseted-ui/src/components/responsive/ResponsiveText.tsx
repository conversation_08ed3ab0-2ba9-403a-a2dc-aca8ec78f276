import React from 'react';
import { Typography, TypographyProps, useTheme, useMediaQuery } from '@mui/material';

interface ResponsiveTextProps extends TypographyProps {
  children: React.ReactNode;
  mobileVariant?: TypographyProps['variant'];
  tabletVariant?: TypographyProps['variant'];
  desktopVariant?: TypographyProps['variant'];
  mobileFontSize?: string | number;
  tabletFontSize?: string | number;
  desktopFontSize?: string | number;
  mobileLineHeight?: string | number;
  tabletLineHeight?: string | number;
  desktopLineHeight?: string | number;
  truncateLines?: number;
  responsive?: boolean;
}

const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  mobileVariant,
  tabletVariant,
  desktopVariant,
  mobileFontSize,
  tabletFontSize,
  desktopFontSize,
  mobileLineHeight,
  tabletLineHeight,
  desktopLineHeight,
  truncateLines,
  responsive = true,
  // sx,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // Determine variant based on screen size
  const variant = responsive
    ? isMobile
      ? mobileVariant || props.variant
      : isTablet
        ? tabletVariant || mobileVariant || props.variant
        : desktopVariant || tabletVariant || mobileVariant || props.variant
    : props.variant;

  // Determine responsive styles
  const responsiveStyles = responsive
    ? {
      fontSize: isMobile
        ? mobileFontSize
        : isTablet
          ? tabletFontSize || mobileFontSize
          : desktopFontSize || tabletFontSize || mobileFontSize,
      lineHeight: isMobile
        ? mobileLineHeight
        : isTablet
          ? tabletLineHeight || mobileLineHeight
          : desktopLineHeight || tabletLineHeight || mobileLineHeight,
    }
    : {};

  // Truncation styles
  const truncationStyles = truncateLines
    ? {
      display: '-webkit-box',
      WebkitLineClamp: truncateLines,
      WebkitBoxOrient: 'vertical',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    }
    : {};

  return (
    <Typography
      variant={variant}
      sx={{
        ...responsiveStyles,
        ...truncationStyles,
        wordBreak: isMobile ? 'break-word' : 'normal',
        hyphens: isMobile ? 'auto' : 'none',
      }}
      {...props}
    >
      {children}
    </Typography>

  );
};

export default ResponsiveText;
