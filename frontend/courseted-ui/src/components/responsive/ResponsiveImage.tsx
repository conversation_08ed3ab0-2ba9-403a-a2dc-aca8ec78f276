import React from 'react';
import { Box, BoxProps, useTheme, useMediaQuery } from '@mui/material';
import Image, { ImageProps } from 'next/image';

interface ResponsiveImageProps extends Omit<ImageProps, 'width' | 'height'> {
  mobileWidth?: number;
  mobileHeight?: number;
  tabletWidth?: number;
  tabletHeight?: number;
  desktopWidth?: number;
  desktopHeight?: number;
  aspectRatio?: string;
  borderRadius?: string | number;
  containerProps?: BoxProps;
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  mobileWidth,
  mobileHeight,
  tabletWidth,
  tabletHeight,
  desktopWidth,
  desktopHeight,
  aspectRatio = '16/9',
  borderRadius = 0,
  containerProps,
  alt,
  src,
  ...imageProps
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // Determine dimensions based on screen size
  const width = isMobile 
    ? mobileWidth 
    : isTablet 
    ? tabletWidth || mobileWidth 
    : desktopWidth || tabletWidth || mobileWidth;
    
  const height = isMobile 
    ? mobileHeight 
    : isTablet 
    ? tabletHeight || mobileHeight 
    : desktopHeight || tabletHeight || mobileHeight;

  return (
    <Box
      sx={{
        position: 'relative',
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : 'auto',
        aspectRatio: !height ? aspectRatio : undefined,
        borderRadius: borderRadius,
        overflow: 'hidden',
        ...containerProps?.sx,
      }}
      {...containerProps}
    >
      <Image
        src={src}
        alt={alt}
        fill={!width || !height}
        width={width}
        height={height}
        style={{
          objectFit: 'cover',
          ...imageProps.style,
        }}
        {...imageProps}
      />
    </Box>
  );
};

export default ResponsiveImage;
