import React from 'react';
import { Grid, GridProps, useTheme, useMediaQuery } from '@mui/material';

interface ResponsiveGridProps extends GridProps {
  children: React.ReactNode;
  tabletColumns?: number;
  desktopColumns?: number;
  mobileSpacing?: number;
  tabletSpacing?: number;
  desktopSpacing?: number;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  tabletColumns = 2,
  desktopColumns = 3,
  mobileSpacing = 2,
  tabletSpacing = 3,
  desktopSpacing = 4,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const spacing = isMobile ? mobileSpacing : isTablet ? tabletSpacing : desktopSpacing;

  return (
    <Grid container spacing={spacing} {...props}>
      {React.Children.map(children, (child, index) => {
        return (
          <Grid item xs={12} sm={tabletColumns === 1 ? 12 : 12 / tabletColumns} md={12 / desktopColumns} key={index}>
            {child}
          </Grid>
        );
      })}
    </Grid>
  );
};

export default ResponsiveGrid;
