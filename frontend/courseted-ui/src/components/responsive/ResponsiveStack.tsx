import React from 'react';
import { Stack, StackProps, useTheme, useMediaQuery } from '@mui/material';

interface ResponsiveStackProps extends StackProps {
  children: React.ReactNode;
  mobileDirection?: 'row' | 'column';
  tabletDirection?: 'row' | 'column';
  desktopDirection?: 'row' | 'column';
  mobileSpacing?: number;
  tabletSpacing?: number;
  desktopSpacing?: number;
  mobileAlign?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  tabletAlign?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  desktopAlign?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  mobileJustify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  tabletJustify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  desktopJustify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
}

const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  mobileDirection = 'column',
  tabletDirection = 'row',
  desktopDirection = 'row',
  mobileSpacing = 2,
  tabletSpacing = 3,
  desktopSpacing = 4,
  mobileAlign = 'flex-start',
  tabletAlign = 'flex-start',
  desktopAlign = 'flex-start',
  mobileJustify = 'flex-start',
  tabletJustify = 'flex-start',
  desktopJustify = 'flex-start',
  sx,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const responsiveSx = {
    flexDirection: isMobile ? mobileDirection : isTablet ? tabletDirection : desktopDirection,
    alignItems: isMobile ? mobileAlign : isTablet ? tabletAlign : desktopAlign,
    justifyContent: isMobile ? mobileJustify : isTablet ? tabletJustify : desktopJustify,
    ...sx,
  };

  const spacing = isMobile ? mobileSpacing : isTablet ? tabletSpacing : desktopSpacing;

  return (
    <Stack spacing={spacing} sx={responsiveSx} {...props}>
      {children}
    </Stack>
  );
};

export default ResponsiveStack;
