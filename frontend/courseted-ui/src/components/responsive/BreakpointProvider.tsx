import React, { ReactNode } from 'react';
import { useResponsive } from '@/hooks/useResponsive';
import { BreakpointContext } from '@/context/BreakpointContext';

interface BreakpointProviderProps {
  children: ReactNode;
}

export const BreakpointProvider: React.FC<BreakpointProviderProps> = ({ children }) => {
  const responsiveValues = useResponsive();

  return (
    <BreakpointContext.Provider value={responsiveValues}>
      {children}
    </BreakpointContext.Provider>
  );
};
