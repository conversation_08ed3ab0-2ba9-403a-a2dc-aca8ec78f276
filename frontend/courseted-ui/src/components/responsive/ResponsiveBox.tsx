import React from 'react';
import { Box, BoxProps, useTheme, useMediaQuery } from '@mui/material';

interface ResponsiveBoxProps extends BoxProps {
  children: React.ReactNode;
  mobilePadding?: number | string;
  tabletPadding?: number | string;
  desktopPadding?: number | string;
  mobileMargin?: number | string;
  tabletMargin?: number | string;
  desktopMargin?: number | string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  hideOnDesktop?: boolean;
  showOnlyMobile?: boolean;
  showOnlyTablet?: boolean;
  showOnlyDesktop?: boolean;
}

const ResponsiveBox: React.FC<ResponsiveBoxProps> = ({
  children,
  mobilePadding,
  tabletPadding,
  desktopPadding,
  mobileMargin,
  tabletMargin,
  desktopMargin,
  hideOnMobile = false,
  hideOnTablet = false,
  hideOnDesktop = false,
  showOnlyMobile = false,
  showOnlyTablet = false,
  showOnlyDesktop = false,
  sx,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));

  // Determine visibility
  let shouldHide = false;
  if (hideOnMobile && isMobile) shouldHide = true;
  if (hideOnTablet && isTablet) shouldHide = true;
  if (hideOnDesktop && isDesktop) shouldHide = true;
  
  if (showOnlyMobile && !isMobile) shouldHide = true;
  if (showOnlyTablet && !isTablet) shouldHide = true;
  if (showOnlyDesktop && !isDesktop) shouldHide = true;

  if (shouldHide) {
    return null;
  }

  const responsiveSx = {
    p: isMobile ? mobilePadding : isTablet ? tabletPadding : desktopPadding,
    m: isMobile ? mobileMargin : isTablet ? tabletMargin : desktopMargin,
    ...sx,
  };

  return (
    <Box sx={responsiveSx} {...props}>
      {children}
    </Box>
  );
};

export default ResponsiveBox;
