
'use client';

import { FC, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/redux/useAuth';
import { Box, CircularProgress, Typography } from '@mui/material';
import { WithAuthProps } from '@/types/components/auth.types';
import ErrorBoundary from './ErrorBoundary';

/**
 * WithAuth Component - Protects routes and optionally wraps with ErrorBoundary
 *
 * Usage Examples:
 *
 * 1. Component Wrapper with ErrorBoundary:
 * <WithAuth errorBoundary={true}>
 *   <YourProtectedContent />
 * </WithAuth>
 *
 * 2. HOC with ErrorBoundary:
 * export default withAuth(YourComponent, { errorBoundary: true });
 *
 * 3. HOC with custom options:
 * export default withAuth(YourComponent, {
 *   redirectTo: '/login',
 *   errorBoundary: true
 * });
 */

const WithAuth: FC<WithAuthProps> = ({
  children,
  redirectTo = '/auth/login',
  fallback,
  errorBoundary = false
}) => {
  const router = useRouter();
  const { isAuthenticated, isLoadingUser } = useAuth();

  useEffect(() => {
    // Only redirect if we're sure the user is not authenticated
    // and not currently loading user data
    if (!isLoadingUser && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoadingUser, router, redirectTo]);

  // Show loading state while checking authentication
  if (isLoadingUser) {
    return fallback || (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Verifying authentication...
        </Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  // Wrap with ErrorBoundary if enabled
  if (errorBoundary) {
    return (
      <ErrorBoundary>
        {children}
      </ErrorBoundary>
    );
  }

  return <>{children}</>;
};

export default WithAuth;
