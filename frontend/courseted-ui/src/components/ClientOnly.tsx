import { useState, useEffect } from 'react';
import { ClientOnlyProps } from '@/types/components/layout.types';

/**
 * ClientOnly component to prevent hydration mismatches
 * This component ensures that its children are only rendered on the client side
 *
 * @param children - The content to render on the client side only
 * @param fallback - Optional fallback content to render during SSR (defaults to null)
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
