'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  VideoCall,
  Schedule,
  People,
  MoreVert,
  Edit,
  Delete,
  Share,
  ContentCopy
} from '@mui/icons-material';
import { ZoomMeeting, MeetingStatus, MeetingType } from '@/types/zoom';
import { User } from '@/types/auth.types';

interface MeetingCardProps {
  meeting: ZoomMeeting;
  currentUser: User;
  onJoin?: (meeting: ZoomMeeting) => void;
  onEdit?: (meeting: ZoomMeeting) => void;
  onDelete?: (meeting: ZoomMeeting) => void;
  onShare?: (meeting: ZoomMeeting) => void;
  onCopyLink?: (meeting: ZoomMeeting) => void;
}

const MeetingCard: React.FC<MeetingCardProps> = ({
  meeting,
  currentUser,
  onJoin,
  onEdit,
  onDelete,
  onShare,
  onCopyLink
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status: MeetingStatus) => {
    switch (status) {
      case 'live':
        return 'success';
      case 'scheduled':
        return 'primary';
      case 'ended':
        return 'default';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getTypeLabel = (type: MeetingType) => {
    switch (type) {
      case 'class':
        return 'Class';
      case 'webinar':
        return 'Webinar';
      case 'office_hours':
        return 'Office Hours';
      case 'group_study':
        return 'Group Study';
      default:
        return type;
    }
  };

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString();
  };

  const canJoin = meeting.status === 'live' || meeting.status === 'scheduled';
  const canEdit = currentUser.id === meeting.hostId || currentUser.role === 'admin';
  const canDelete = currentUser.id === meeting.hostId || currentUser.role === 'admin';

  return (
    <Card 
      sx={{ 
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        }
      }}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box>
            <Chip 
              label={meeting.status.toUpperCase()} 
              color={getStatusColor(meeting.status)}
              size="small"
              sx={{ mb: 1 }}
            />
            <Chip 
              label={getTypeLabel(meeting.type)} 
              variant="outlined"
              size="small"
              sx={{ mb: 1, ml: 1 }}
            />
          </Box>
          <IconButton size="small" onClick={handleMenuOpen}>
            <MoreVert />
          </IconButton>
        </Box>

        <Typography variant="h6" component="h3" gutterBottom>
          {meeting.topic}
        </Typography>

        {meeting.description && (
          <Typography variant="body2" color="text.secondary" paragraph>
            {meeting.description}
          </Typography>
        )}

        <Box display="flex" alignItems="center" mb={1}>
          <Schedule fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            {formatDateTime(meeting.startTime)}
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" mb={1}>
          <People fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            Max {meeting.maxParticipants} participants
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary">
          Duration: {meeting.duration} minutes
        </Typography>
      </CardContent>

      <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
        <Button
          variant="contained"
          startIcon={<VideoCall />}
          onClick={() => onJoin?.(meeting)}
          disabled={!canJoin}
          size={isMobile ? 'small' : 'medium'}
        >
          {meeting.status === 'live' ? 'Join Now' : 'Join Meeting'}
        </Button>

        <Button
          variant="outlined"
          size="small"
          onClick={() => onShare?.(meeting)}
        >
          Share
        </Button>
      </CardActions>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => { onCopyLink?.(meeting); handleMenuClose(); }}>
          <ContentCopy fontSize="small" sx={{ mr: 1 }} />
          Copy Link
        </MenuItem>
        <MenuItem onClick={() => { onShare?.(meeting); handleMenuClose(); }}>
          <Share fontSize="small" sx={{ mr: 1 }} />
          Share
        </MenuItem>
        {canEdit && (
          <MenuItem onClick={() => { onEdit?.(meeting); handleMenuClose(); }}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit
          </MenuItem>
        )}
        {canDelete && (
          <MenuItem onClick={() => { onDelete?.(meeting); handleMenuClose(); }}>
            <Delete fontSize="small" sx={{ mr: 1 }} />
            Delete
          </MenuItem>
        )}
      </Menu>
    </Card>
  );
};

export default MeetingCard;
