'use client';

import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Tooltip,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Mic,
  MicOff,
  Videocam,
  VideocamOff,
  ScreenShare,
  StopScreenShare,
  FiberManualRecord,
  Stop,
  ExitToApp,
  People,
  Chat,
  Settings
} from '@mui/icons-material';
import { zoomSDKService } from '@/services/zoom/sdkService';

interface MeetingControlsProps {
  isHost?: boolean;
  onLeaveMeeting?: () => void;
  onToggleParticipants?: () => void;
  onToggleChat?: () => void;
  onSettings?: () => void;
}

const MeetingControls: React.FC<MeetingControlsProps> = ({
  isHost = false,
  onLeaveMeeting,
  onToggleParticipants,
  onToggleChat,
  onSettings
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  const handleToggleAudio = async () => {
    try {
      const controls = zoomSDKService.getMeetingControls();
      if (controls) {
        if (isAudioMuted) {
          await controls.unmute();
        } else {
          await controls.mute();
        }
        setIsAudioMuted(!isAudioMuted);
      }
    } catch (error) {
      console.error('Error toggling audio:', error);
    }
  };

  const handleToggleVideo = async () => {
    try {
      const controls = zoomSDKService.getMeetingControls();
      if (controls) {
        if (isVideoOff) {
          await controls.startVideo();
        } else {
          await controls.stopVideo();
        }
        setIsVideoOff(!isVideoOff);
      }
    } catch (error) {
      console.error('Error toggling video:', error);
    }
  };

  const handleToggleScreenShare = async () => {
    try {
      const controls = zoomSDKService.getMeetingControls();
      if (controls) {
        if (isScreenSharing) {
          await controls.stopShare();
        } else {
          await controls.startShare();
        }
        setIsScreenSharing(!isScreenSharing);
      }
    } catch (error) {
      console.error('Error toggling screen share:', error);
    }
  };

  const handleToggleRecording = async () => {
    try {
      const controls = zoomSDKService.getMeetingControls();
      if (controls) {
        if (isRecording) {
          await controls.stopRecording();
        } else {
          await controls.startRecording();
        }
        setIsRecording(!isRecording);
      }
    } catch (error) {
      console.error('Error toggling recording:', error);
    }
  };

  const controlButtons = [
    {
      icon: isAudioMuted ? <MicOff /> : <Mic />,
      tooltip: isAudioMuted ? 'Unmute' : 'Mute',
      onClick: handleToggleAudio,
      color: isAudioMuted ? 'error' : 'default',
      show: true
    },
    {
      icon: isVideoOff ? <VideocamOff /> : <Videocam />,
      tooltip: isVideoOff ? 'Start Video' : 'Stop Video',
      onClick: handleToggleVideo,
      color: isVideoOff ? 'error' : 'default',
      show: true
    },
    {
      icon: isScreenSharing ? <StopScreenShare /> : <ScreenShare />,
      tooltip: isScreenSharing ? 'Stop Sharing' : 'Share Screen',
      onClick: handleToggleScreenShare,
      color: isScreenSharing ? 'primary' : 'default',
      show: true
    },
    {
      icon: <People />,
      tooltip: 'Participants',
      onClick: onToggleParticipants,
      color: 'default',
      show: true
    },
    {
      icon: <Chat />,
      tooltip: 'Chat',
      onClick: onToggleChat,
      color: 'default',
      show: true
    },
    {
      icon: isRecording ? <Stop /> : <FiberManualRecord />,
      tooltip: isRecording ? 'Stop Recording' : 'Start Recording',
      onClick: handleToggleRecording,
      color: isRecording ? 'error' : 'default',
      show: isHost
    },
    {
      icon: <Settings />,
      tooltip: 'Settings',
      onClick: onSettings,
      color: 'default',
      show: true
    }
  ];

  return (
    <Paper
      elevation={3}
      sx={{
        position: 'fixed',
        bottom: 20,
        left: '50%',
        transform: 'translateX(-50%)',
        borderRadius: 6,
        p: 1,
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(10px)',
        zIndex: 1000
      }}
    >
      {controlButtons
        .filter(button => button.show)
        .map((button, index) => (
          <Tooltip key={index} title={button.tooltip}>
            <IconButton
              onClick={button.onClick}
              sx={{
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)'
                },
                ...(button.color === 'error' && {
                  backgroundColor: theme.palette.error.main,
                  '&:hover': {
                    backgroundColor: theme.palette.error.dark
                  }
                }),
                ...(button.color === 'primary' && {
                  backgroundColor: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark
                  }
                })
              }}
              size={isMobile ? 'small' : 'medium'}
            >
              {button.icon}
            </IconButton>
          </Tooltip>
        ))}

      {/* Leave Meeting Button */}
      <Box sx={{ ml: 2, pl: 2, borderLeft: '1px solid rgba(255, 255, 255, 0.2)' }}>
        <Tooltip title="Leave Meeting">
          <IconButton
            onClick={onLeaveMeeting}
            sx={{
              color: 'white',
              backgroundColor: theme.palette.error.main,
              '&:hover': {
                backgroundColor: theme.palette.error.dark
              }
            }}
            size={isMobile ? 'small' : 'medium'}
          >
            <ExitToApp />
          </IconButton>
        </Tooltip>
      </Box>
    </Paper>
  );
};

export default MeetingControls;
