'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  Alert
} from '@mui/material';
import { Delete, Warning } from '@mui/icons-material';
import { ZoomMeeting } from '@/types/zoom/meeting.types';

interface DeleteConfirmationDialogProps {
  open: boolean;
  meeting: ZoomMeeting | null;
  onClose: () => void;
  onConfirm: (meeting: ZoomMeeting) => void;
  loading?: boolean;
  error?: string;
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  open,
  meeting,
  onClose,
  onConfirm,
  loading = false,
  error
}) => {
  const handleConfirm = () => {
    if (meeting) {
      onConfirm(meeting);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Warning color="warning" />
          Delete Meeting
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        <Typography variant="body1" sx={{ mb: 2 }}>
          Are you sure you want to delete this meeting?
        </Typography>
        
        {meeting && (
          <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" fontWeight="bold">
              {meeting.topic}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {new Date(meeting.startTime).toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Duration: {meeting.duration} minutes
            </Typography>
          </Box>
        )}
        
        <Alert severity="warning" sx={{ mt: 2 }}>
          This action cannot be undone. All participants will lose access to this meeting.
        </Alert>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleConfirm} 
          variant="contained"
          color="error"
          disabled={loading || !meeting}
          startIcon={<Delete />}
        >
          {loading ? 'Deleting...' : 'Delete Meeting'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteConfirmationDialog;
