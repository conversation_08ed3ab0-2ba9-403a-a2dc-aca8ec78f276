'use client';

import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Box,
  Grid,
  Typography,
  Alert
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useCreateMeetingMutation } from '@/services/zoom/meetingApi';
import { CreateMeetingRequest, MeetingType } from '@/types/zoom/meeting.types';

interface CreateMeetingDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CreateMeetingDialog: React.FC<CreateMeetingDialogProps> = ({
  open,
  onClose,
  onSuccess
}) => {
  const [createMeeting, { isLoading, error }] = useCreateMeetingMutation();

  const [formData, setFormData] = useState<CreateMeetingRequest>({
    topic: '',
    description: '',
    type: 'class',
    startTime: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
    duration: 60,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    password: '',
    maxParticipants: 100,
    isRecordingEnabled: false,
    isWaitingRoomEnabled: true,
    isMuteOnEntry: true,
  });

  const handleInputChange = (field: keyof CreateMeetingRequest) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    let value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;

    // Convert to number for specific fields
    if (field === 'duration' || field === 'maxParticipants') {
      value = Number(value);
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateTimeChange = (date: Date | null) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        startTime: date.toISOString()
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      await createMeeting(formData).unwrap();
      onSuccess?.();
      onClose();
      // Reset form
      setFormData({
        topic: '',
        description: '',
        type: 'class',
        startTime: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
        duration: 60,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        password: '',
        maxParticipants: 100,
        isRecordingEnabled: false,
        isWaitingRoomEnabled: true,
        isMuteOnEntry: true,
      });
    } catch (err) {
      console.error('Failed to create meeting:', err);
    }
  };

  const meetingTypes: { value: MeetingType; label: string }[] = [
    { value: 'class', label: 'Class' },
    { value: 'webinar', label: 'Webinar' },
    { value: 'office_hours', label: 'Office Hours' },
    { value: 'group_study', label: 'Group Study' },
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>Create New Meeting</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              Failed to create meeting. Please try again.
            </Alert>
          )}

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meeting Topic"
                value={formData.topic}
                onChange={handleInputChange('topic')}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={handleInputChange('description')}
                multiline
                rows={3}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Meeting Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={handleInputChange('type')}
                  label="Meeting Type"
                >
                  {meetingTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label="Start Time"
                value={new Date(formData.startTime)}
                onChange={handleDateTimeChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration (minutes)"
                type="number"
                value={formData.duration}
                onChange={handleInputChange('duration')}
                inputProps={{ min: 15, max: 480 }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Max Participants"
                type="number"
                value={formData.maxParticipants}
                onChange={handleInputChange('maxParticipants')}
                inputProps={{ min: 1, max: 1000 }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Meeting Password (Optional)"
                type="password"
                value={formData.password}
                onChange={handleInputChange('password')}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Meeting Settings
              </Typography>
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isWaitingRoomEnabled}
                      onChange={handleInputChange('isWaitingRoomEnabled')}
                    />
                  }
                  label="Enable Waiting Room"
                />
              </Box>
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isMuteOnEntry}
                      onChange={handleInputChange('isMuteOnEntry')}
                    />
                  }
                  label="Mute Participants on Entry"
                />
              </Box>
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isRecordingEnabled}
                      onChange={handleInputChange('isRecordingEnabled')}
                    />
                  }
                  label="Enable Recording"
                />
              </Box>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading || !formData.topic.trim()}
          >
            {isLoading ? 'Creating...' : 'Create Meeting'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default CreateMeetingDialog;
