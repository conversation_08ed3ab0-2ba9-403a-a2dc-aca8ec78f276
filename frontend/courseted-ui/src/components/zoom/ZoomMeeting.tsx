'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  // Videocam,
  // VideocamOff,
  Mic,
  MicOff,
  CallEnd,
  // ScreenShare,
  // StopScreenShare
} from '@mui/icons-material';
import ZoomMtgEmbedded from '@zoom/meetingsdk/embedded';
import { ZoomMeeting } from '@/types/zoom/meeting.types';
import { User } from '@/types/auth.types';
import { useGetMeetingSignatureQuery } from '@/services/zoom/meetingApi';

export interface ZoomMeetingProps {
  meeting: ZoomMeeting;
  currentUser: User;
  onLeave?: () => void;
}

interface MeetingControls {
  mute?: () => Promise<void>;
  unmute?: () => Promise<void>;
  startVideo?: () => Promise<void>;
  stopVideo?: () => Promise<void>;
  startShare?: () => Promise<void>;
  stopShare?: () => Promise<void>;
  leave?: () => Promise<void>;
}

const ZoomMeetingComponent: React.FC<ZoomMeetingProps> = ({
  meeting,
  currentUser,
  onLeave
}) => {
  // State management
  const [isJoining, setIsJoining] = useState(false);
  const [inSession, setInSession] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [client, setClient] = useState<any>(null);
  const [controls, setControls] = useState<MeetingControls | null>(null);

  // Media states
  // const [isVideoMuted, setIsVideoMuted] = useState(true);
  const [isAudioMuted, setIsAudioMuted] = useState(true);
  // const [isScreenSharing, setIsScreenSharing] = useState(false);

  // Refs
  const meetingContainerRef = useRef<HTMLDivElement>(null);

  // Determine user role (0 = participant, 1 = host)
  const role = currentUser.id === meeting.hostId ? 1 : 0;

  // Fetch meeting signature
  const {
    data: signatureData,
    error: signatureError,
    isLoading: signatureLoading
  } = useGetMeetingSignatureQuery({
    meetingId: meeting.id,
    role
  });

  // Handle signature error
  useEffect(() => {
    if (signatureError) {
      setError('Failed to get meeting signature');

    }
  }, [signatureError]);

  // Join meeting function
  const joinMeeting = async () => {
    if (!signatureData?.signature || !meetingContainerRef.current) {
      setError('Meeting signature or container not available');
      return;
    }

    try {
      setIsJoining(true);
      setError(null);

      console.log('Initializing Zoom Meeting SDK...');

      // Create Zoom client
      const zoomClient = ZoomMtgEmbedded.createClient();
      setClient(zoomClient);

      // Initialize the SDK
      await zoomClient.init({
        zoomAppRoot: meetingContainerRef.current,
        language: 'en-US',
        patchJsMedia: true,
        leaveOnPageUnload: true
      });

      console.log('Joining meeting...');

      // Join the meeting
      await zoomClient.join({
        sdkKey: process.env.NEXT_PUBLIC_ZOOM_SDK_KEY!,
        signature: signatureData.signature,
        meetingNumber: meeting.meetingId,
        password: meeting.password || '',
        userName: currentUser.profile.firstName + ' ' + currentUser.profile.lastName,
        userEmail: currentUser.email,
        tk: '',
        zak: ''
      });

      console.log('Successfully joined meeting');

      // Set up controls
      const meetingControls: MeetingControls = {
        mute: async () => {
          await zoomClient.mute(true);
          setIsAudioMuted(true);
        },
        // unmute: async () => {
        //   await zoomClient.unmute();
        //   setIsAudioMuted(false);
        // },
        // startVideo: async () => {
        //   await zoomClient.startVideo();
        //   setIsVideoMuted(false);
        // },
        // stopVideo: async () => {
        //   await zoomClient.stopVideo();
        //   setIsVideoMuted(true);
        // },
        // startShare: async () => {
        //   await zoomClient.startShare();
        //   setIsScreenSharing(true);
        // },
        // stopShare: async () => {
        //   await zoomClient.stopShare();
        //   setIsScreenSharing(false);
        // },
        // leave: async () => {
        //   await zoomClient.leave();
        //   setInSession(false);
        //   onLeave?.();
        // }
      };

      setControls(meetingControls);
      setInSession(true);
      setIsJoining(false);

    } catch (err) {
      console.error('Error joining meeting:', err);
      setError(err instanceof Error ? err.message : 'Failed to join meeting');
      setIsJoining(false);
    }
  };

  // Leave meeting function
  const leaveMeeting = async () => {
    try {
      if (controls) {
        await controls.leave();
      }
    } catch (err) {
      console.error('Error leaving meeting:', err);
      onLeave?.();
    }
  };

  // Media control functions
  // const toggleVideo = async () => {
  //   if (!controls) return;

  //   try {
  //     if (isVideoMuted) {
  //       await controls.startVideo();
  //     } else {
  //       await controls.stopVideo();
  //     }
  //   } catch (err) {
  //     console.error('Error toggling video:', err);
  //   }
  // };

  const toggleAudio = async () => {
    if (!controls) return;

    try {
      if (isAudioMuted) {
        await controls.unmute();
      } else {
        await controls.mute();
      }
    } catch (err) {
      console.error('Error toggling audio:', err);
    }
  };

  // const toggleScreenShare = async () => {
  //   if (!controls) return;

  //   try {
  //     if (isScreenSharing) {
  //       await controls.stopShare();
  //     } else {
  //       await controls.startShare();
  //     }
  //   } catch (err) {
  //     console.error('Error toggling screen share:', err);
  //   }
  // };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (client) {
        try {
          client.leave();
        } catch (err) {
          console.error('Error during cleanup:', err);
        }
      }
    };
  }, [client]);

  // Error state
  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        p={3}
      >
        <Alert severity="error" sx={{ mb: 2, maxWidth: 400 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    );
  }

  // Loading state
  if (signatureLoading || isJoining) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        p={3}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {signatureLoading ? 'Preparing Meeting...' : 'Joining Meeting...'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we set up your meeting
        </Typography>
      </Box>
    );
  }

  // In session view
  if (inSession) {
    return (
      <Box sx={{ height: '100vh', width: '100%', position: 'relative', bgcolor: '#000' }}>
        {/* Meeting container */}
        <div
          ref={meetingContainerRef}
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0
          }}
        />

        {/* Meeting controls */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 20,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: 2,
            bgcolor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: 2,
            p: 2
          }}
        >
          <Tooltip title={isAudioMuted ? 'Unmute' : 'Mute'}>
            <IconButton
              onClick={toggleAudio}
              sx={{
                bgcolor: isAudioMuted ? 'error.main' : 'primary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: isAudioMuted ? 'error.dark' : 'primary.dark'
                }
              }}
            >
              {isAudioMuted ? <MicOff /> : <Mic />}
            </IconButton>
          </Tooltip>

          {/* <Tooltip title={isVideoMuted ? 'Start Video' : 'Stop Video'}>
            <IconButton
              onClick={toggleVideo}
              sx={{
                bgcolor: isVideoMuted ? 'error.main' : 'primary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: isVideoMuted ? 'error.dark' : 'primary.dark'
                }
              }}
            >
              {isVideoMuted ? <VideocamOff /> : <Videocam />}
            </IconButton>
          </Tooltip> */}

          {/* <Tooltip title={isScreenSharing ? 'Stop Sharing' : 'Share Screen'}>
            <IconButton
              onClick={toggleScreenShare}
              sx={{
                bgcolor: isScreenSharing ? 'warning.main' : 'grey.600',
                color: 'white',
                '&:hover': {
                  bgcolor: isScreenSharing ? 'warning.dark' : 'grey.700'
                }
              }}
            >
              {isScreenSharing ? <StopScreenShare /> : <ScreenShare />}
            </IconButton>
          </Tooltip> */}

          <Tooltip title="Leave Meeting">
            <IconButton
              onClick={leaveMeeting}
              sx={{
                bgcolor: 'error.main',
                color: 'white',
                '&:hover': {
                  bgcolor: 'error.dark'
                }
              }}
            >
              <CallEnd />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    );
  }

  // Pre-join view
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      p={3}
    >
      <Paper elevation={3} sx={{ p: 4, maxWidth: 500, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          {meeting.topic}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Meeting ID: {meeting.meetingId}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Host: {meeting.hostEmail}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
          Ready to join the meeting? Click the button below to start.
        </Typography>

        <Button
          variant="contained"
          size="large"
          onClick={joinMeeting}
          disabled={!signatureData?.signature}
          sx={{ mb: 2 }}
        >
          Join Meeting
        </Button>
      </Paper>

      {/* Hidden meeting container for initialization */}
      <div
        ref={meetingContainerRef}
        style={{
          width: '100%',
          height: '400px',
          position: 'absolute',
          top: '-500px',
          left: 0,
          visibility: 'hidden'
        }}
      />
    </Box>
  );
};

export default ZoomMeetingComponent;
