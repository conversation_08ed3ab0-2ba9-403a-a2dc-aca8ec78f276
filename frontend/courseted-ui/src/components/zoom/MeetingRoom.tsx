'use client';

import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';

// Extend window interface for React compatibility
declare global {
  interface Window {
    React?: any;
  }
}
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Paper
} from '@mui/material';
import { ZoomMeeting } from '@/types/zoom/meeting.types';
import { User } from '@/types/auth.types';
import { useGetMeetingSignatureQuery } from '@/services/zoom/meetingApi';

interface MeetingRoomProps {
  meeting: ZoomMeeting;
  currentUser: User;
  onLeave?: () => void;
}

const MeetingRoom: React.FC<MeetingRoomProps> = ({
  meeting,
  currentUser,
  onLeave
}) => {
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasJoined, setHasJoined] = useState(false);
  const [client, setClient] = useState<any>(null);
  const [isEmbedded, setIsEmbedded] = useState(false);
  const meetingSDKElement = useRef<HTMLDivElement>(null);
  const [containerReady, setContainerReady] = useState(true);

  // Check if the container is ready
  // useEffect(() => {
  //   if (meetingSDKElement.current) {
  //     setContainerReady(true);
  //   }
  // }, [meetingSDKElement]);

  useLayoutEffect(() => {
    if (meetingSDKElement.current) {
      setContainerReady(true);
    }
  }, []);

  useEffect(() => {
    console.log('meetingSDKElement.current ready:', meetingSDKElement.current);
  }, [containerReady]);

  // Determine user role (0 = participant, 1 = host)
  const role = currentUser.id === meeting.hostId ? 1 : 0;

  const { data: signatureData, error: signatureError, isLoading: signatureLoading } = useGetMeetingSignatureQuery({
    meetingId: meeting.id,
    role
  });

  // Remove automatic joining - let user click the button
  // useEffect(() => {
  //   if (signatureData?.signature && !hasJoined && !isJoining) {
  //     initializeAndJoinMeeting();
  //   }
  // }, [signatureData, hasJoined, isJoining]);

  useEffect(() => {
    if (signatureError) {
      setError('Failed to get meeting signature');
    }
  }, [signatureError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (client) {
        try {
          client.leave();
        } catch (err) {
          console.error('Error during cleanup:', err);
        }
      }
    };
  }, [client]);

  const initializeAndJoinMeeting = async () => {
    console.log('Initializing and joining meeting...');

    if (!signatureData?.signature) {
      console.error('No signature data available');
      setError('Meeting signature not available');
      return;
    }

    console.log('Initializing Zoom Meeting SDK...');
    try {
      setIsJoining(true);
      setError(null);

      // Ensure the element is properly positioned and visible for SDK initialization
      if (meetingSDKElement.current) {
        meetingSDKElement.current.style.width = '100%';
        meetingSDKElement.current.style.height = '100vh';
        meetingSDKElement.current.style.position = 'fixed';
        meetingSDKElement.current.style.top = '0';
        meetingSDKElement.current.style.left = '0';
        meetingSDKElement.current.style.zIndex = '1000';
        meetingSDKElement.current.style.backgroundColor = '#000';
        meetingSDKElement.current.style.display = 'block';
      }

      console.log('Meeting SDK element:', meetingSDKElement.current);

      if (!meetingSDKElement.current) {
        console.error('Meeting SDK element not found');
        setError('Meeting container not ready. Please try again.');
        setIsJoining(false);
        return;
      }


      // Dynamically import React and patch Zoom's requirement
      const ReactModule = await import('react');
      const ZoomMtgEmbedded = (await import('@zoom/meetingsdk/embedded')).default;

      // Monkey-patch React internals
      if (typeof window !== 'undefined') {
        (window as any).React = ReactModule.default;
        (window as any).React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
          ReactCurrentOwner: { current: null },
          ReactCurrentDispatcher: { current: null },
          ReactCurrentBatchConfig: { transition: null },
        };
      }

      const zoomClient = ZoomMtgEmbedded.createClient();
      setClient(zoomClient);

      // Initialize the SDK
      console.log('Zoom client created, initializing...');
      console.log('Meeting SDK element:', meetingSDKElement.current);
      // Initialize the SDK
      await zoomClient.init({
        zoomAppRoot: meetingSDKElement.current,
        // language: 'en-US',
        // patchJsMedia: true,
        // leaveOnPageUnload: true
        language: 'en-US',
        customize: {
          video: {
            isResizable: true,
          },
        },
      });

      console.log('Zoom SDK initialized, joining meeting...');

      // Join the meeting
      await zoomClient.join({
        sdkKey: process.env.NEXT_PUBLIC_ZOOM_SDK_KEY!,
        signature: signatureData.signature,
        meetingNumber: meeting.meetingId,
        password: meeting.password || '',
        userName: currentUser?.profile?.firstName,
        userEmail: currentUser.email,
        tk: '', // Leave empty for most use cases
        zak: '', // Leave empty for most use cases
      });

      console.log('Successfully joined meeting');
      setHasJoined(true);
      setIsEmbedded(true);
      setIsJoining(false);
    } catch (err) {
      console.error('Error joining meeting:', err);

      // Fall back to web client
      console.log('Falling back to Zoom web client...');
      const joinUrl = `https://zoom.us/wc/join/${meeting.meetingId}`;
      const params = new URLSearchParams({
        pwd: meeting.password || '',
        uname: currentUser?.profile?.firstName,
        email: currentUser.email || ''
      });
      const fullUrl = `${joinUrl}?${params.toString()}`;
      // window.open(fullUrl, '_blank', 'width=1200,height=800');
      window.open(fullUrl, '_blank');
      setHasJoined(true);
      setIsEmbedded(false);
      setIsJoining(false);
    }
  };

  const handleLeaveMeeting = async () => {
    try {
      if (client) {
        await client.leave();
      }
      setHasJoined(false);
      onLeave?.();
    } catch (err) {
      console.error('Error leaving meeting:', err);
      onLeave?.();
    }
  };

  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="400px"
        p={3}
      >
        <Alert severity="error" sx={{ mb: 2, maxWidth: 400 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    );
  }

  if (signatureLoading || isJoining) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="400px"
        p={3}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {signatureLoading ? 'Preparing Meeting...' : 'Joining Meeting...'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we set up your meeting
        </Typography>
      </Box>
    );
  }

  if (hasJoined) {
    if (isEmbedded) {
      return (
        <Box sx={{ height: '100vh', width: '100%', position: 'relative' }}>
          {/* Meeting container */}
          <div
            ref={meetingSDKElement}
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              top: 0,
              left: 0
            }}
          />

          {/* Leave meeting button */}
          <Button
            variant="contained"
            color="error"
            onClick={handleLeaveMeeting}
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              zIndex: 1001
            }}
          >
            Leave Meeting
          </Button>
        </Box>
      );
    } else {
      // Web client fallback message
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="400px"
          p={3}
        >
          <Paper elevation={3} sx={{ p: 4, maxWidth: 500, textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom color="success.main">
              Meeting Opened Successfully!
            </Typography>
            <Typography variant="body1" sx={{ mb: 3 }}>
              Your meeting has been opened in a new window using Zoom's web client.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              You can now close this tab and use the Zoom web client to participate in the meeting.
            </Typography>
            <Button
              variant="outlined"
              onClick={() => window.close()}
              sx={{ mr: 2 }}
            >
              Close This Tab
            </Button>
            <Button
              variant="contained"
              onClick={handleLeaveMeeting}
            >
              Back to Meetings
            </Button>
          </Paper>
        </Box>
      );
    }
  }

  // Show the join button and meeting info
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="400px"
      p={3}
    >
      <Paper elevation={3} sx={{ p: 4, maxWidth: 500, textAlign: 'center' }}>
        <Typography variant="h5" gutterBottom>
          {meeting.topic}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Meeting ID: {meeting.meetingId}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Host: {meeting.hostEmail}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
          Click the button below to join the meeting.
        </Typography>

        <Button
          variant="contained"
          size="large"
          onClick={initializeAndJoinMeeting}
          disabled={isJoining || !signatureData?.signature}
          sx={{ mb: 2 }}
        >
          Join Meeting
        </Button>
      </Paper>

      {/* Hidden meeting SDK container for initialization */}
      <div
        ref={meetingSDKElement}
        id="zoom-meeting-sdk-root"
        style={{
          width: '100%',
          height: '400px',
          position: 'absolute',
          top: '-500px',
          left: 0,
          zIndex: -1,
          visibility: 'hidden'
        }}
      />
    </Box>

  );
};

export default MeetingRoom;
