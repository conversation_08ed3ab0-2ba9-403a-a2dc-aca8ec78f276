'use client';

import React, { useRef, useEffect } from 'react';

// Extend window interface for React compatibility
declare global {
  interface Window {
    React?: any;
  }
}
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Videocam,
  VideocamOff,
  Mic,
  MicOff,
  CallEnd,
  ScreenShare,
  StopScreenShare
} from '@mui/icons-material';
import { ZoomMeeting } from '@/types/zoom/meeting.types';
import { User } from '@/types/auth.types';
import { useGetMeetingSignatureQuery } from '@/services/zoom/meetingApi';
import { useZoomSDK } from '../zoom-integration';

export interface ZoomMeetingSimpleProps {
  meeting: ZoomMeeting;
  currentUser: User;
  onLeave?: () => void;
}

const ZoomMeetingSimple: React.FC<ZoomMeetingSimpleProps> = ({
  meeting,
  currentUser,
  onLeave
}) => {
  const meetingContainerRef = useRef<HTMLDivElement>(null);

  // Determine user role (0 = participant, 1 = host)
  const role = currentUser.id === meeting.hostId ? 1 : 0;

  // Fetch meeting signature
  const {
    data: signatureData,
    error: signatureError,
    isLoading: signatureLoading
  } = useGetMeetingSignatureQuery({
    meetingId: meeting.id,
    role
  });

  // Use Zoom SDK hook
  const [zoomState, zoomActions] = useZoomSDK();

  // Initialize SDK when container is ready
  useEffect(() => {
    if (meetingContainerRef.current && !zoomState.isInitialized) {
      zoomActions.initializeSDK(meetingContainerRef.current);
    }
  }, [zoomState.isInitialized, zoomActions]);

  // Handle join meeting
  const handleJoinMeeting = async () => {
    console.log('signatureData?.signature:::', signatureData?.signature);

    if (!signatureData?.signature) {
      return;
    }

    try {
      // Set up React compatibility for Zoom SDK
      console.log("typeof window !== 'undefined' && !window.React:::", typeof window !== 'undefined', !window.React);

      if (typeof window !== 'undefined' && !window.React) {
        const ReactModule = await import('react');
        window.React = ReactModule.default;

        // Add React internals that Zoom SDK expects
        if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
          window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
            ReactCurrentOwner: { current: null },
            ReactCurrentDispatcher: { current: null },
            ReactCurrentBatchConfig: { transition: null },
            ReactDebugCurrentFrame: { getCurrentStack: null },
            ReactCurrentActQueue: { current: null, isBatchingLegacy: false, didScheduleLegacyUpdate: false }
          };
        }
      }

      await zoomActions.joinMeeting({
        sdkKey: process.env.NEXT_PUBLIC_ZOOM_SDK_KEY!,
        signature: signatureData.signature,
        meetingNumber: meeting.meetingId,
        password: meeting.password || '',
        userName: currentUser.profile.firstName,
        userEmail: currentUser.email
      });
    } catch (error) {
      console.error('Error in handleJoinMeeting:', error);
      // If there's still a React compatibility issue, try a fallback
      if (error instanceof Error && error.message.includes('ReactCurrentOwner')) {
        console.log('React compatibility issue detected, trying fallback...');
        // Fallback to web client
        const joinUrl = `https://zoom.us/wc/join/${meeting.meetingId}`;
        const params = new URLSearchParams({
          pwd: meeting.password || '',
          uname: currentUser.profile.firstName,
          email: currentUser.email || ''
        });
        const fullUrl = `${joinUrl}?${params.toString()}`;
        window.open(fullUrl, '_blank', 'width=1200,height=800');
      }
    }
  };

  // Handle leave meeting
  const handleLeaveMeeting = async () => {
    await zoomActions.leaveMeeting();
    onLeave?.();
  };

  // Error state
  const error = signatureError || zoomState.error;
  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        p={3}
      >
        <Alert severity="error" sx={{ mb: 2, maxWidth: 400 }}>
          {typeof error === 'string' ? error : 'An error occurred'}
        </Alert>
        <Button
          variant="outlined"
          onClick={() => {
            zoomActions.clearError();
            window.location.reload();
          }}
        >
          Retry
        </Button>
      </Box>
    );
  }

  // Loading state
  if (signatureLoading || zoomState.isJoining || !zoomState.isInitialized) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        p={3}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {signatureLoading
            ? 'Preparing Meeting...'
            : zoomState.isJoining
              ? 'Joining Meeting...'
              : 'Initializing...'
          }
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we set up your meeting
        </Typography>
      </Box>
    );
  }

  // In session view
  if (zoomState.inSession) {
    return (
      <Box sx={{ height: '100vh', width: '100%', position: 'relative', bgcolor: '#000' }}>
        {/* Meeting container */}
        <div
          ref={meetingContainerRef}
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0
          }}
        />

        {/* Meeting controls */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 20,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: 2,
            bgcolor: 'rgba(0, 0, 0, 0.8)',
            borderRadius: 3,
            p: 2,
            backdropFilter: 'blur(10px)'
          }}
        >
          <Tooltip title={zoomState.isAudioMuted ? 'Unmute' : 'Mute'}>
            <IconButton
              onClick={zoomActions.toggleAudio}
              sx={{
                bgcolor: zoomState.isAudioMuted ? 'error.main' : 'success.main',
                color: 'white',
                width: 48,
                height: 48,
                '&:hover': {
                  bgcolor: zoomState.isAudioMuted ? 'error.dark' : 'success.dark'
                }
              }}
            >
              {zoomState.isAudioMuted ? <MicOff /> : <Mic />}
            </IconButton>
          </Tooltip>

          <Tooltip title={zoomState.isVideoMuted ? 'Start Video' : 'Stop Video'}>
            <IconButton
              onClick={zoomActions.toggleVideo}
              sx={{
                bgcolor: zoomState.isVideoMuted ? 'error.main' : 'success.main',
                color: 'white',
                width: 48,
                height: 48,
                '&:hover': {
                  bgcolor: zoomState.isVideoMuted ? 'error.dark' : 'success.dark'
                }
              }}
            >
              {zoomState.isVideoMuted ? <VideocamOff /> : <Videocam />}
            </IconButton>
          </Tooltip>

          <Tooltip title={zoomState.isScreenSharing ? 'Stop Sharing' : 'Share Screen'}>
            <IconButton
              onClick={zoomActions.toggleScreenShare}
              sx={{
                bgcolor: zoomState.isScreenSharing ? 'warning.main' : 'grey.600',
                color: 'white',
                width: 48,
                height: 48,
                '&:hover': {
                  bgcolor: zoomState.isScreenSharing ? 'warning.dark' : 'grey.700'
                }
              }}
            >
              {zoomState.isScreenSharing ? <StopScreenShare /> : <ScreenShare />}
            </IconButton>
          </Tooltip>

          <Tooltip title="Leave Meeting">
            <IconButton
              onClick={handleLeaveMeeting}
              sx={{
                bgcolor: 'error.main',
                color: 'white',
                width: 48,
                height: 48,
                '&:hover': {
                  bgcolor: 'error.dark'
                }
              }}
            >
              <CallEnd />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Meeting info overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 20,
            left: 20,
            bgcolor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            p: 2,
            borderRadius: 2,
            backdropFilter: 'blur(10px)'
          }}
        >
          <Typography variant="h6" sx={{ mb: 1 }}>
            {meeting.topic}
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            Meeting ID: {meeting.meetingId}
          </Typography>
        </Box>
      </Box>
    );
  }

  // Pre-join view
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      p={3}
      sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}
    >
      <Paper
        elevation={6}
        sx={{
          p: 4,
          maxWidth: 500,
          textAlign: 'center',
          bgcolor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRadius: 3
        }}
      >
        <Typography variant="h4" gutterBottom color="primary">
          {meeting.topic}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Meeting ID: {meeting.meetingId}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Host: {meeting.hostEmail}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
          Ready to join the meeting? Click the button below to start.
        </Typography>

        <Button
          variant="contained"
          size="large"
          onClick={handleJoinMeeting}
          disabled={!signatureData?.signature || !zoomState.isInitialized}
          sx={{
            mb: 2,
            py: 1.5,
            px: 4,
            fontSize: '1.1rem',
            borderRadius: 2
          }}
        >
          Join Meeting
        </Button>

        {!zoomState.isInitialized && (
          <Typography variant="caption" color="text.secondary" display="block">
            Initializing Zoom SDK...
          </Typography>
        )}
      </Paper>

      {/* Hidden meeting container for initialization */}
      <div
        ref={meetingContainerRef}
        style={{
          width: '100%',
          height: '400px',
          position: 'absolute',
          top: '-500px',
          left: 0,
          visibility: 'hidden'
        }}
      />
    </Box>
  );
};

export default ZoomMeetingSimple;
