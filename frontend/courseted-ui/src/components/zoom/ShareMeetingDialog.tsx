'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  TextField,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Share,
  ContentCopy,
  Email,
  // Link as LinkIcon,
  WhatsApp,
  Facebook,
  Twitter,
  Check
} from '@mui/icons-material';
import { ZoomMeeting } from '@/types/zoom/meeting.types';

interface ShareMeetingDialogProps {
  open: boolean;
  meeting: ZoomMeeting | null;
  onClose: () => void;
  onCopyLink?: (meeting: ZoomMeeting) => void;
}

const ShareMeetingDialog: React.FC<ShareMeetingDialogProps> = ({
  open,
  meeting,
  onClose,
  onCopyLink
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopyLink = async () => {
    if (meeting) {
      try {
        await navigator.clipboard.writeText(meeting.joinUrl);
        setCopied(true);
        onCopyLink?.(meeting);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy link:', err);
      }
    }
  };

  const handleEmailShare = () => {
    if (meeting) {
      const subject = encodeURIComponent(`Join Meeting: ${meeting.topic}`);
      const body = encodeURIComponent(
        `You're invited to join a meeting:\n\n` +
        `Topic: ${meeting.topic}\n` +
        `Time: ${new Date(meeting.startTime).toLocaleString()}\n` +
        `Duration: ${meeting.duration} minutes\n\n` +
        `Join URL: ${meeting.joinUrl}\n` +
        (meeting.password ? `Password: ${meeting.password}\n` : '') +
        `\nJoin from your computer, tablet or smartphone.`
      );
      window.open(`mailto:?subject=${subject}&body=${body}`);
    }
  };

  const handleWhatsAppShare = () => {
    if (meeting) {
      const text = encodeURIComponent(
        `Join Meeting: ${meeting.topic}\n` +
        `Time: ${new Date(meeting.startTime).toLocaleString()}\n` +
        `${meeting.joinUrl}`
      );
      window.open(`https://wa.me/?text=${text}`);
    }
  };

  const handleTwitterShare = () => {
    if (meeting) {
      const text = encodeURIComponent(
        `Join my meeting: ${meeting.topic} - ${meeting.joinUrl}`
      );
      window.open(`https://twitter.com/intent/tweet?text=${text}`);
    }
  };

  const handleFacebookShare = () => {
    if (meeting) {
      const url = encodeURIComponent(meeting.joinUrl);
      window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`);
    }
  };

  const shareOptions = [
    {
      icon: <Email />,
      label: 'Email',
      onClick: handleEmailShare,
      color: 'primary'
    },
    {
      icon: <WhatsApp />,
      label: 'WhatsApp',
      onClick: handleWhatsAppShare,
      color: 'success'
    },
    {
      icon: <Twitter />,
      label: 'Twitter',
      onClick: handleTwitterShare,
      color: 'info'
    },
    {
      icon: <Facebook />,
      label: 'Facebook',
      onClick: handleFacebookShare,
      color: 'primary'
    }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Share />
          Share Meeting
        </Box>
      </DialogTitle>

      <DialogContent>
        {meeting && (
          <>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                {meeting.topic}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {new Date(meeting.startTime).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Duration: {meeting.duration} minutes
              </Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Meeting Link
              </Typography>
              <Box display="flex" gap={1}>
                <TextField
                  fullWidth
                  value={meeting.joinUrl}
                  InputProps={{
                    readOnly: true,
                  }}
                  size="small"
                />
                <IconButton
                  onClick={handleCopyLink}
                  color={copied ? 'success' : 'primary'}
                  size="small"
                >
                  {copied ? <Check /> : <ContentCopy />}
                </IconButton>
              </Box>
              {copied && (
                <Typography variant="caption" color="success.main" sx={{ mt: 1 }}>
                  Link copied to clipboard!
                </Typography>
              )}
            </Box>

            {meeting.password && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Meeting Password
                </Typography>
                <TextField
                  fullWidth
                  value={meeting.password}
                  InputProps={{
                    readOnly: true,
                  }}
                  size="small"
                />
              </Box>
            )}

            <Divider sx={{ my: 2 }} />

            <Typography variant="subtitle2" gutterBottom>
              Share via
            </Typography>
            <List dense>
              {shareOptions.map((option, index) => (
                <ListItem
                  key={index}
                  component="button"
                  onClick={option.onClick}
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    textAlign: 'left',
                    background: 'none',
                    border: 'none',
                    padding: 0,
                  }}
                >
                  <ListItemIcon sx={{ color: `${option.color}.main` }}>
                    {option.icon}
                  </ListItemIcon>
                  <ListItemText primary={option.label} />
                </ListItem>
              ))}
            </List>
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ShareMeetingDialog;
