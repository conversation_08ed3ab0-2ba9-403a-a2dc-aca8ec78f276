'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  Divider,
  Badge,
  Tooltip
} from '@mui/material';
import {
  MoreVert,
  Mic,
  MicOff,
  Videocam,
  VideocamOff,
  PanTool,
  ScreenShare,
  PersonAdd,
  Block,
  Star,
  StarBorder
} from '@mui/icons-material';
import { MeetingParticipant, ParticipantListProps } from '@/types/zoom';

const ParticipantList: React.FC<ParticipantListProps> = ({
  participants,
  waitingRoomParticipants,
  currentUser,
  onParticipantAction
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedParticipant, setSelectedParticipant] = useState<MeetingParticipant | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, participant: MeetingParticipant) => {
    setAnchorEl(event.currentTarget);
    setSelectedParticipant(participant);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedParticipant(null);
  };

  const handleParticipantAction = (action: string) => {
    if (selectedParticipant) {
      onParticipantAction?.(action, selectedParticipant.id);
    }
    handleMenuClose();
  };

  const getParticipantIcon = (participant: MeetingParticipant) => {
    if (participant.isScreenSharing) return <ScreenShare fontSize="small" />;
    if (participant.isHandRaised) return <PanTool fontSize="small" />;
    return null;
  };

  const isHost = currentUser.role === 'host' || currentUser.role === 'co_host';

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6">
          Participants ({participants.length})
        </Typography>
      </Box>

      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {/* Active Participants */}
        <List dense>
          {participants.map((participant) => (
            <ListItem key={participant.id}>
              <ListItemAvatar>
                <Badge
                  badgeContent={getParticipantIcon(participant)}
                  color="primary"
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                >
                  <Avatar sx={{ width: 32, height: 32 }}>
                    {participant.userName.charAt(0).toUpperCase()}
                  </Avatar>
                </Badge>
              </ListItemAvatar>

              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2" noWrap>
                      {participant.userName}
                      {participant.id === currentUser.id && ' (You)'}
                    </Typography>
                    {participant.role === 'host' && (
                      <Chip label="Host" size="small" color="primary" />
                    )}
                    {participant.role === 'co_host' && (
                      <Chip label="Co-Host" size="small" color="secondary" />
                    )}
                  </Box>
                }
                secondary={
                  <Box display="flex" alignItems="center" gap={0.5}>
                    <Tooltip title={participant.isAudioMuted ? 'Muted' : 'Unmuted'}>
                      {participant.isAudioMuted ? (
                        <MicOff fontSize="small" color="error" />
                      ) : (
                        <Mic fontSize="small" color="success" />
                      )}
                    </Tooltip>
                    <Tooltip title={participant.isVideoOn ? 'Video On' : 'Video Off'}>
                      {participant.isVideoOn ? (
                        <Videocam fontSize="small" color="success" />
                      ) : (
                        <VideocamOff fontSize="small" color="error" />
                      )}
                    </Tooltip>
                  </Box>
                }
              />

              {isHost && participant.id !== currentUser.id && (
                <ListItemSecondaryAction>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, participant)}
                  >
                    <MoreVert />
                  </IconButton>
                </ListItemSecondaryAction>
              )}
            </ListItem>
          ))}
        </List>

        {/* Waiting Room Participants */}
        {waitingRoomParticipants.length > 0 && (
          <>
            <Divider />
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Waiting Room ({waitingRoomParticipants.length})
              </Typography>
            </Box>
            <List dense>
              {waitingRoomParticipants.map((participant) => (
                <ListItem key={participant.id}>
                  <ListItemAvatar>
                    <Avatar sx={{ width: 32, height: 32 }}>
                      {participant.userName.charAt(0).toUpperCase()}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={participant.userName}
                    secondary={`Waiting since ${new Date(participant.waitingSince).toLocaleTimeString()}`}
                  />
                  {isHost && (
                    <ListItemSecondaryAction>
                      <Tooltip title="Admit">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => onParticipantAction?.('admit', participant.id)}
                        >
                          <PersonAdd />
                        </IconButton>
                      </Tooltip>
                    </ListItemSecondaryAction>
                  )}
                </ListItem>
              ))}
            </List>
          </>
        )}
      </Box>

      {/* Participant Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {selectedParticipant && (
          <>
            <MenuItem onClick={() => handleParticipantAction('mute')}>
              <MicOff sx={{ mr: 1 }} />
              {selectedParticipant.isAudioMuted ? 'Unmute' : 'Mute'}
            </MenuItem>
            <MenuItem onClick={() => handleParticipantAction('stopVideo')}>
              <VideocamOff sx={{ mr: 1 }} />
              Stop Video
            </MenuItem>
            <MenuItem onClick={() => handleParticipantAction('makeCoHost')}>
              {selectedParticipant.role === 'co_host' ? <StarBorder sx={{ mr: 1 }} /> : <Star sx={{ mr: 1 }} />}
              {selectedParticipant.role === 'co_host' ? 'Remove Co-Host' : 'Make Co-Host'}
            </MenuItem>
            <MenuItem onClick={() => handleParticipantAction('putInWaitingRoom')}>
              <Block sx={{ mr: 1 }} />
              Put in Waiting Room
            </MenuItem>
            <MenuItem onClick={() => handleParticipantAction('remove')} sx={{ color: 'error.main' }}>
              <Block sx={{ mr: 1 }} />
              Remove from Meeting
            </MenuItem>
          </>
        )}
      </Menu>
    </Box>
  );
};

export default ParticipantList;
