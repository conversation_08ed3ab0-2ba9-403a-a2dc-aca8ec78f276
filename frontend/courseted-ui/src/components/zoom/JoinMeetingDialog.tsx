'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  Button,
  Box,
  Typography,
  Alert
} from '@mui/material';
import { VideoCall } from '@mui/icons-material';
import { useJoinMeetingMutation } from '@/services/zoom/meetingApi';

interface JoinMeetingDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (joinUrl: string) => void;
}

const JoinMeetingDialog: React.FC<JoinMeetingDialogProps> = ({
  open,
  onClose,
  onSuccess
}) => {
  const [joinMeeting, { isLoading, error }] = useJoinMeetingMutation();
  
  const [formData, setFormData] = useState({
    meetingId: '',
    password: '',
    userName: '',
    userEmail: ''
  });

  const handleInputChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSubmit = async () => {
    try {
      const result = await joinMeeting(formData).unwrap();
      onSuccess?.(result.data.joinUrl);
      onClose();
      // Reset form
      setFormData({
        meetingId: '',
        password: '',
        userName: '',
        userEmail: ''
      });
    } catch (err) {
      console.error('Failed to join meeting:', err);
    }
  };

  const isFormValid = formData.meetingId.trim() && formData.userName.trim() && formData.userEmail.trim();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <VideoCall />
          Join Meeting
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Failed to join meeting. Please check your meeting ID and try again.
          </Alert>
        )}
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Enter the meeting details to join an existing meeting.
        </Typography>
        
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            fullWidth
            label="Meeting ID"
            value={formData.meetingId}
            onChange={handleInputChange('meetingId')}
            placeholder="123-456-789"
            required
            helperText="Enter the 9, 10, or 11-digit meeting ID"
          />
          
          <TextField
            fullWidth
            label="Meeting Password"
            type="password"
            value={formData.password}
            onChange={handleInputChange('password')}
            placeholder="Optional"
            helperText="Enter the meeting password if required"
          />
          
          <TextField
            fullWidth
            label="Your Name"
            value={formData.userName}
            onChange={handleInputChange('userName')}
            required
            helperText="This name will be displayed to other participants"
          />
          
          <TextField
            fullWidth
            label="Your Email"
            type="email"
            value={formData.userEmail}
            onChange={handleInputChange('userEmail')}
            required
            helperText="Your email address"
          />
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={isLoading || !isFormValid}
          startIcon={<VideoCall />}
        >
          {isLoading ? 'Joining...' : 'Join Meeting'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default JoinMeetingDialog;
