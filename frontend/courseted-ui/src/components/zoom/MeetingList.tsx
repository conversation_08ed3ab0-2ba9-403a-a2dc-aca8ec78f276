'use client';

import React from 'react';
import {
  Grid,
  Box,
  Typography,
  CircularProgress,
  Alert
} from '@mui/material';
import { User } from '@/types/auth.types';
import MeetingCard from './MeetingCard';
import { ZoomMeeting } from '@/types/zoom/meeting.types';

interface MeetingListProps {
  meetings: ZoomMeeting[];
  currentUser: User;
  loading?: boolean;
  error?: string;
  onJoinMeeting?: (meeting: ZoomMeeting) => void;
  onEditMeeting?: (meeting: ZoomMeeting) => void;
  onDeleteMeeting?: (meeting: ZoomMeeting) => void;
  onShareMeeting?: (meeting: ZoomMeeting) => void;
  onCopyMeetingLink?: (meeting: ZoomMeeting) => void;
}

const MeetingList: React.FC<MeetingListProps> = ({
  meetings,
  currentUser,
  loading = false,
  error,
  onJoinMeeting,
  onEditMeeting,
  onDeleteMeeting,
  onShareMeeting,
  onCopyMeetingLink
}) => {
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (meetings.length === 0) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="200px"
        textAlign="center"
        p={3}
      >
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No meetings found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Create your first meeting to get started
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      {meetings.map((meeting) => (
        <Grid item xs={12} sm={6} md={4} key={meeting.id}>
          <MeetingCard
            meeting={meeting}
            currentUser={currentUser}
            onJoin={onJoinMeeting}
            onEdit={onEditMeeting}
            onDelete={onDeleteMeeting}
            onShare={onShareMeeting}
            onCopyLink={onCopyMeetingLink}
          />
        </Grid>
      ))}
    </Grid>
  );
};

export default MeetingList;
