'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControlLabel,
  Switch,
  Box,
  Grid,
  Typography,
  Alert
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Edit } from '@mui/icons-material';
import { useUpdateMeetingMutation } from '@/services/zoom/meetingApi';
import { UpdateMeetingRequest, ZoomMeeting } from '@/types/zoom/meeting.types';

interface EditMeetingDialogProps {
  open: boolean;
  meeting: ZoomMeeting | null;
  onClose: () => void;
  onSuccess?: () => void;
}

const EditMeetingDialog: React.FC<EditMeetingDialogProps> = ({
  open,
  meeting,
  onClose,
  onSuccess
}) => {
  const [updateMeeting, { isLoading, error }] = useUpdateMeetingMutation();

  const [formData, setFormData] = useState<UpdateMeetingRequest>({
    topic: '',
    description: '',
    startTime: new Date().toISOString(),
    duration: 60,
    password: '',
    maxParticipants: 100,
    isRecordingEnabled: false,
    isWaitingRoomEnabled: true,
    isMuteOnEntry: true,
  });

  // Initialize form data when meeting changes
  useEffect(() => {
    if (meeting) {
      setFormData({
        topic: meeting.topic,
        description: meeting.description || '',
        startTime: meeting.startTime,
        duration: meeting.duration,
        password: meeting.password || '',
        maxParticipants: meeting.maxParticipants,
        isRecordingEnabled: meeting.isRecordingEnabled,
        isWaitingRoomEnabled: meeting.isWaitingRoomEnabled,
        isMuteOnEntry: meeting.isMuteOnEntry,
      });
    }
  }, [meeting]);

  const handleInputChange = (field: keyof UpdateMeetingRequest) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        startTime: date.toISOString()
      }));
    }
  };

  const handleSubmit = async () => {
    if (!meeting) return;

    try {
      await updateMeeting({
        id: meeting.id,
        data: formData
      }).unwrap();
      
      onSuccess?.();
      onClose();
    } catch (err) {
      console.error('Failed to update meeting:', err);
    }
  };

  const handleClose = () => {
    onClose();
    // Reset form data when closing
    if (meeting) {
      setFormData({
        topic: meeting.topic,
        description: meeting.description || '',
        startTime: meeting.startTime,
        duration: meeting.duration,
        password: meeting.password || '',
        maxParticipants: meeting.maxParticipants,
        isRecordingEnabled: meeting.isRecordingEnabled,
        isWaitingRoomEnabled: meeting.isWaitingRoomEnabled,
        isMuteOnEntry: meeting.isMuteOnEntry,
      });
    }
  };

  const isFormValid = formData.topic.trim() && formData.duration > 0;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <Edit />
            Edit Meeting
          </Box>
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              Failed to update meeting. Please try again.
            </Alert>
          )}

          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meeting Topic"
                value={formData.topic}
                onChange={handleInputChange('topic')}
                required
                helperText="Enter a descriptive title for your meeting"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={handleInputChange('description')}
                multiline
                rows={3}
                helperText="Optional description or agenda for the meeting"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label="Start Time"
                value={new Date(formData.startTime)}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration (minutes)"
                type="number"
                value={formData.duration}
                onChange={handleInputChange('duration')}
                required
                inputProps={{ min: 15, max: 480 }}
                helperText="15 minutes to 8 hours"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Meeting Password"
                value={formData.password}
                onChange={handleInputChange('password')}
                helperText="Optional password for meeting security"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Max Participants"
                type="number"
                value={formData.maxParticipants}
                onChange={handleInputChange('maxParticipants')}
                inputProps={{ min: 2, max: 1000 }}
                helperText="Maximum number of participants"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Meeting Settings
              </Typography>
              <Box display="flex" flexDirection="column" gap={1}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isRecordingEnabled}
                      onChange={handleInputChange('isRecordingEnabled')}
                    />
                  }
                  label="Enable Recording"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isWaitingRoomEnabled}
                      onChange={handleInputChange('isWaitingRoomEnabled')}
                    />
                  }
                  label="Enable Waiting Room"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isMuteOnEntry}
                      onChange={handleInputChange('isMuteOnEntry')}
                    />
                  }
                  label="Mute Participants on Entry"
                />
              </Box>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={isLoading || !isFormValid}
            startIcon={<Edit />}
          >
            {isLoading ? 'Updating...' : 'Update Meeting'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default EditMeetingDialog;
