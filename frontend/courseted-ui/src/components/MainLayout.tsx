'use client';
import { Box } from '@mui/material';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { MainLayoutProps } from '@/types/components/layout.types';

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Header />
      <Box component="main" sx={{ flex: 1 }}>
        {children}
      </Box>
      <Footer />
    </Box>
  );
}
