import { useState, useRef, useCallback, useEffect } from 'react';
import ZoomMtgEmbedded from '@zoom/meetingsdk/embedded';

// Extend window interface for React compatibility
declare global {
  interface Window {
    React?: any;
  }
}

interface ZoomSDKConfig {
  sdkKey: string;
  signature: string;
  meetingNumber: string;
  password?: string;
  userName: string;
  userEmail: string;
}

interface ZoomSDKState {
  isInitialized: boolean;
  isJoining: boolean;
  inSession: boolean;
  error: string | null;
  isVideoMuted: boolean;
  isAudioMuted: boolean;
  isScreenSharing: boolean;
}

interface ZoomSDKActions {
  initializeSDK: (container: HTMLElement) => Promise<void>;
  joinMeeting: (config: ZoomSDKConfig) => Promise<void>;
  leaveMeeting: () => Promise<void>;
  toggleVideo: () => Promise<void>;
  toggleAudio: () => Promise<void>;
  toggleScreenShare: () => Promise<void>;
  clearError: () => void;
}

export const useZoomSDK = (): [ZoomSDKState, ZoomSDKActions] => {
  const [state, setState] = useState<ZoomSDKState>({
    isInitialized: false,
    isJoining: false,
    inSession: false,
    error: null,
    isVideoMuted: true,
    isAudioMuted: true,
    isScreenSharing: false,
  });

  const clientRef = useRef<any>(null);
  const containerRef = useRef<HTMLElement | null>(null);

  // Update state helper
  const updateState = useCallback((updates: Partial<ZoomSDKState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Initialize SDK
  const initializeSDK = useCallback(async (container: HTMLElement) => {
    try {
      updateState({ error: null });

      if (!container) {
        throw new Error('Container element is required');
      }

      containerRef.current = container;

      // Set up React compatibility for Zoom SDK
      if (typeof window !== 'undefined') {
        if (!window.React) {
          const ReactModule = await import('react');
          window.React = ReactModule.default;
        }

        // Add React internals that Zoom SDK expects
        if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
          window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
            ReactCurrentOwner: { current: null },
            ReactCurrentDispatcher: { current: null },
            ReactCurrentBatchConfig: { transition: null },
            ReactDebugCurrentFrame: { getCurrentStack: null },
            ReactCurrentActQueue: { current: null, isBatchingLegacy: false, didScheduleLegacyUpdate: false }
          };
        }
      }

      // Create Zoom client
      const client = ZoomMtgEmbedded.createClient();
      clientRef.current = client;

      // Initialize the SDK
      await client.init({
        zoomAppRoot: container,
        language: 'en-US',
        patchJsMedia: true,
        leaveOnPageUnload: true
      });

      updateState({ isInitialized: true });
      console.log('Zoom SDK initialized successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize Zoom SDK';
      updateState({ error: errorMessage });
      console.error('Zoom SDK initialization error:', error);
    }
  }, [updateState]);

  // Join meeting
  const joinMeeting = useCallback(async (config: ZoomSDKConfig) => {
    if (!clientRef.current || !state.isInitialized) {
      updateState({ error: 'SDK not initialized' });
      return;
    }

    try {
      updateState({ isJoining: true, error: null });

      await clientRef.current.join({
        sdkKey: config.sdkKey,
        signature: config.signature,
        meetingNumber: config.meetingNumber,
        password: config.password || '',
        userName: config.userName,
        userEmail: config.userEmail,
        tk: '',
        zak: ''
      });

      updateState({ 
        isJoining: false, 
        inSession: true,
        isVideoMuted: true,
        isAudioMuted: true 
      });
      
      console.log('Successfully joined meeting');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to join meeting';
      updateState({ 
        isJoining: false, 
        error: errorMessage 
      });
      console.error('Join meeting error:', error);
    }
  }, [state.isInitialized, updateState]);

  // Leave meeting
  const leaveMeeting = useCallback(async () => {
    if (!clientRef.current) return;

    try {
      await clientRef.current.leave();
      updateState({ 
        inSession: false,
        isVideoMuted: true,
        isAudioMuted: true,
        isScreenSharing: false 
      });
      console.log('Left meeting successfully');
      
    } catch (error) {
      console.error('Leave meeting error:', error);
      // Still update state even if leave fails
      updateState({ 
        inSession: false,
        isVideoMuted: true,
        isAudioMuted: true,
        isScreenSharing: false 
      });
    }
  }, [updateState]);

  // Toggle video
  const toggleVideo = useCallback(async () => {
    if (!clientRef.current || !state.inSession) return;

    try {
      if (state.isVideoMuted) {
        await clientRef.current.startVideo();
        updateState({ isVideoMuted: false });
      } else {
        await clientRef.current.stopVideo();
        updateState({ isVideoMuted: true });
      }
    } catch (error) {
      console.error('Toggle video error:', error);
      updateState({ error: 'Failed to toggle video' });
    }
  }, [state.isVideoMuted, state.inSession, updateState]);

  // Toggle audio
  const toggleAudio = useCallback(async () => {
    if (!clientRef.current || !state.inSession) return;

    try {
      if (state.isAudioMuted) {
        await clientRef.current.unmute();
        updateState({ isAudioMuted: false });
      } else {
        await clientRef.current.mute();
        updateState({ isAudioMuted: true });
      }
    } catch (error) {
      console.error('Toggle audio error:', error);
      updateState({ error: 'Failed to toggle audio' });
    }
  }, [state.isAudioMuted, state.inSession, updateState]);

  // Toggle screen share
  const toggleScreenShare = useCallback(async () => {
    if (!clientRef.current || !state.inSession) return;

    try {
      if (state.isScreenSharing) {
        await clientRef.current.stopShare();
        updateState({ isScreenSharing: false });
      } else {
        await clientRef.current.startShare();
        updateState({ isScreenSharing: true });
      }
    } catch (error) {
      console.error('Toggle screen share error:', error);
      updateState({ error: 'Failed to toggle screen share' });
    }
  }, [state.isScreenSharing, state.inSession, updateState]);

  // Clear error
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (clientRef.current) {
        try {
          clientRef.current.leave();
        } catch (error) {
          console.error('Cleanup error:', error);
        }
      }
    };
  }, []);

  const actions: ZoomSDKActions = {
    initializeSDK,
    joinMeeting,
    leaveMeeting,
    toggleVideo,
    toggleAudio,
    toggleScreenShare,
    clearError,
  };

  return [state, actions];
};

export default useZoomSDK;
