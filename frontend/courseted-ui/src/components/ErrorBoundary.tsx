'use client';

import React, { Component, ErrorInfo } from 'react';
import { Box, Typography, Button, Alert, Container } from '@mui/material';
import { RefreshRounded, BugReport } from '@mui/icons-material';
import { ErrorBoundaryProps, ErrorBoundaryState } from '@/types/components/error.types';

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
    
    // Log to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            minHeight="50vh"
            gap={3}
            textAlign="center"
          >
            <BugReport color="error" sx={{ fontSize: 80 }} />
            
            <Typography variant="h4" component="h1" fontWeight={600}>
              Something went wrong
            </Typography>
            
            <Alert severity="error" sx={{ maxWidth: 600, width: '100%' }}>
              <Typography variant="body1" gutterBottom>
                We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
              </Typography>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <Box mt={2}>
                  <Typography variant="body2" component="pre" sx={{ 
                    fontSize: '0.75rem',
                    backgroundColor: 'rgba(0,0,0,0.05)',
                    p: 1,
                    borderRadius: 1,
                    overflow: 'auto',
                    maxHeight: 200,
                    textAlign: 'left'
                  }}>
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </Typography>
                </Box>
              )}
            </Alert>
            
            <Box display="flex" gap={2} flexDirection={{ xs: 'column', sm: 'row' }}>
              <Button
                variant="contained"
                startIcon={<RefreshRounded />}
                onClick={this.handleReset}
                size="large"
              >
                Try Again
              </Button>
              <Button
                variant="outlined"
                onClick={() => window.location.href = '/'}
                size="large"
              >
                Go Home
              </Button>
            </Box>
          </Box>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
