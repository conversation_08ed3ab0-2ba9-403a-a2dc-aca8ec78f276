import Checkbox, { CheckboxProps } from '@mui/material/Checkbox';
import Icon from '@/components/ui/Icon';
import theme from '@/theme';

type Status = 'default' | 'checked' | 'indeterminate';
type State = 'default' | 'hover' | 'disabled';
interface CustomCheckBoxProps extends CheckboxProps {
  checked?: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
}
const CheckBox = ({
  checked = false,
  indeterminate = false,
  disabled = false,
  ...props
}: CustomCheckBoxProps) => {
  const status: Status = indeterminate ? 'indeterminate' : checked ? 'checked' : 'default';

  const baseStyles = checkboxStatus(status);
  const hoverStyles = checkboxState('hover', status);
  const disabledStyles = checkboxState('disabled', status);

  return (
    <Checkbox
      checked={checked}
      indeterminate={indeterminate}
      disabled={disabled}
      sx={{
        ...props.sx,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '4px',
        width: '20px',
        height: '20px',
        boxSizing: 'border-box',
        padding: 0,
        ...baseStyles,
        '&:hover': hoverStyles,
        '&.Mui-disabled': disabledStyles,
      }}
      checkedIcon={<Icon name="Tick_02" height={12} width={10} />}
      indeterminateIcon={<Icon name="Remove_01" height={10} width={10} />}
      {...props}
    />
  );
};

function checkboxStatus(status: Status) {
  const grey = theme.palette.grey;

  switch (status) {
    case 'checked':
      return {
        border: 'none',
        background: grey[900],
      };
    case 'indeterminate':
      return {
        border: 'none',
        background: grey[900],
      };
    case 'default':
    default:
      return {
        border: `1px ${grey[300]}`,
        background: 'white',
      };
  }
}
function checkboxState(state: State, status: Status) {
  const grey = theme.palette.grey;

  if (state === 'hover') {
    return {
      background: status === 'default' ? grey[200] : grey[800],
    };
  }

  if (state === 'disabled') {
    return {
      background: status === 'default' ? grey[200] : grey[500],
      border: status === 'default' ? `1px solid ${grey[300]}` : 'none',
    };
  }

  return {};
}

export default CheckBox;
