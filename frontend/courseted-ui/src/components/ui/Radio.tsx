
import Radio from '@mui/material/Radio';
import RadioGroup, { RadioGroupProps } from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import theme from '@/theme';

interface RadioProps extends RadioGroupProps {
  label: string;
  options: { label: string; value: string }[];
  disabled?: boolean;
}

export default function RadioButtonsGroup({ label = '', options = [], disabled = false, ...props }: RadioProps) {
  return (
    <FormControl>
      <FormLabel
        sx={{
          color: 'grey.900',
          fontSize: '18px',
          lineHeight: '20px',
          fontWeight: 500,
        }}
      >
        {label}
      </FormLabel>

      <RadioGroup
        row
        aria-labelledby="demo-row-radio-buttons-group-label"
        name="row-radio-buttons-group"
        {...props}
      >
        {/* <FormControlLabel value="female" control={<Radio />} label="Female" />
        <FormControlLabel value="male" control={<Radio />} label="Male" />
        <FormControlLabel value="other" control={<Radio />} label="Other" /> */}

        {options.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={<Radio sx={{
              color: theme.palette.grey[900],
              '&.Mui-checked': {
                color: theme.palette.primary[600],
              },
            }} />}
            label={option.label}
            sx={{ color: 'grey.900' }}
          />
        ))}

        {disabled && (
          <FormControlLabel
            value="disabled"
            disabled
            control={<Radio />}
            label="other"
          />
        )}
      </RadioGroup>
    </FormControl>
  );
}
