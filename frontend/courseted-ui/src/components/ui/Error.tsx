import { Box, Container, Typography, Grid } from '@mui/material';
import Button from './Button';

export default function Error() {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        padding: { xs: 2, sm: 3 }
      }}
    >
      <Container maxWidth="md">
        <Grid container spacing={{ xs: 3, sm: 4 }} alignItems="center">
          <Grid xs={12} md={6}>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '4rem', sm: '6rem', md: '8rem' },
                textAlign: { xs: 'center', md: 'left' },
                fontWeight: 'bold',
                color: 'primary.main'
              }}
            >
              404
            </Typography>
            <Typography
              variant="h6"
              sx={{
                fontSize: { xs: '1rem', sm: '1.25rem' },
                textAlign: { xs: 'center', md: 'left' },
                mb: 3,
                color: 'text.secondary'
              }}
            >
              The page you're looking for doesn't exist.
            </Typography>
            <Box sx={{ textAlign: { xs: 'center', md: 'left' } }}>
              <Button variant="primary" href='/'>Back Home</Button>
            </Box>
          </Grid>
          <Grid xs={12} md={6}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                mt: { xs: 2, md: 0 }
              }}
            >
              <img
                src="https://cdn.pixabay.com/photo/2017/03/09/12/31/error-2129569__340.jpg"
                alt="404 Error"
                style={{
                  width: '100%',
                  maxWidth: '500px',
                  height: 'auto',
                  borderRadius: '8px'
                }}
              />
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}
