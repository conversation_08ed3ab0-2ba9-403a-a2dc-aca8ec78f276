import React, { useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import Stepper from './Stepper';

// Example usage of the reusable Stepper component
const StepperExample: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      step: 1,
      label: 'Student Information',
      description: 'Fill out your basic information including name, phone, and country.',
    },
    {
      step: 2,
      label: 'Educational Background',
      description: 'Provide details about your educational qualifications and experience.',
    },
    {
      step: 3,
      label: 'Course Preferences',
      description: 'Select your preferred courses and learning schedule.',
    },
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  return (
    <Box sx={{ maxWidth: 400, margin: 'auto', padding: 4 }}>
      <Typography variant="h5" gutterBottom>
        Stepper Component Example
      </Typography>
      
      {/* Vertical Stepper */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Vertical Stepper
        </Typography>
        <Stepper
          activeStep={activeStep}
          steps={steps}
          orientation="vertical"
          showLastStepLabel={true}
          lastStepLabelText="Final step"
        />
      </Box>

      {/* Horizontal Stepper */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Horizontal Stepper
        </Typography>
        <Stepper
          activeStep={activeStep}
          steps={steps}
          orientation="horizontal"
          showLastStepLabel={false}
        />
      </Box>

      {/* Controls */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
          variant="outlined"
        >
          Back
        </Button>
        <Button
          disabled={activeStep >= steps.length}
          onClick={handleNext}
          variant="contained"
        >
          {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
        </Button>
        <Button onClick={handleReset} variant="text">
          Reset
        </Button>
      </Box>

      <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
        Current Step: {activeStep + 1} / {steps.length}
      </Typography>
    </Box>
  );
};

export default StepperExample;
