import theme from '@/theme';
import { Chip as BaseChip } from '@mui/material';
import Icon from '@/components/ui/Icon';
import { CustomChipProps } from '@/types/ui/chip.types';
const Chip = ({ bgColor, textColor, customDeleteIcon, ...props }: CustomChipProps) => {
  const background = bgColor || theme.palette.grey[200];
  const color = textColor || theme.palette.grey[900];
  return (
    <BaseChip
      sx={{ background, color, ...props.sx }}
      {...props}
      deleteIcon={customDeleteIcon || <Icon name="CrossIcon" sx={{ mx: 1 }} />}
    />
  );
};

export default Chip;
