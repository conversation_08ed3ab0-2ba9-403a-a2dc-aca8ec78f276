import { Box, Container } from '@mui/material';
import { SystemProps } from '@mui/system';
type SectionLayoutProps = {
  backgroundColor?: string;
  py: SystemProps['py'];
  maxWidth?: 'xl' | 'lg' | 'md' | 'sm' | 'xs';
  children: React.ReactNode;
} & SystemProps;
const SectionLayout = ({
  backgroundColor,
  py,
  maxWidth,
  children,
  ...props
}: SectionLayoutProps) => {
  return (
    <Box sx={{ backgroundColor: backgroundColor || 'white', py }} {...props}>
      <Container maxWidth={maxWidth || 'xl'}>{children}</Container>
    </Box>
  );
};

export default SectionLayout;
