import { Card, CardContent, Grid, Skeleton, Stack, Box, Divider } from '@mui/material';

const CourseCardSkeleton = () => {
  return (
    <Grid item xs={12} sm={6} md={4} sx={{ display: 'flex' }} flex={1}>
      <Card
        elevation={0}
        sx={{
          borderRadius: '16px',
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          flexGrow: 1,
          width: '100%',
        }}
      >
        <Skeleton variant="rectangular" sx={{ borderRadius: 6, p: 2, m: 2 }} height={180} />
        <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          <Box
            sx={{
              position: 'absolute',
              top: 25,
              right: 25,
              backgroundColor: 'transparent', 
              p: 1,
              borderRadius: '100px',
              px: 2,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Skeleton variant="text" width={40} sx={{ mr: 0.5 }} />
            <Skeleton variant="circular" width={20} height={20} sx={{ ml: 0.5 }} />
          </Box>
          <Skeleton variant="text" sx={{ fontSize: '1.25rem', width: '80%' }} />
          <Skeleton variant="text" sx={{ fontSize: '1rem', width: '90%', mt: 1 }} />
          <Skeleton variant="text" sx={{ fontSize: '1rem', width: '70%', mt: 0.5 }} />

          <Divider sx={{ my: 1, opacity: 0.7 }} />
          <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
            <Stack direction="row" spacing={1} alignItems="center">
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width={80} />
            </Stack>
            <Stack direction="row" spacing={1} alignItems="center">
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width={60} />
            </Stack>
            <Stack direction="row" spacing={1} alignItems="center">
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width={90} />
            </Stack>
          </Stack>
          <Divider sx={{ my: 2, opacity: 0.7 }} />
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Stack direction="row" spacing={1} alignItems="center">
              <Skeleton variant="circular" width={40} height={40} />
              <Skeleton variant="text" width={100} />
            </Stack>
            <Skeleton variant="text" width={50} sx={{ fontSize: '1.25rem' }} />
          </Stack>
        </CardContent>
      </Card>
    </Grid>
  );
};

export default CourseCardSkeleton;
