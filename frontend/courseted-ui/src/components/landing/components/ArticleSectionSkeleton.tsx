import { Card, CardContent, Grid, Skeleton, Stack } from '@mui/material';
import SectionLayout from '@/components/landing/components/SectionLayout';
import SectionHeader from '@/components/landing/components/SectionHeader';
import theme from '@/theme'; 

const ArticleSectionSkeleton = () => {
  return (
    <SectionLayout py={{ xs: 5, sm: 10 }} maxWidth="xl">
      <SectionHeader title="articles" />

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card
            elevation={0}
            sx={{
              borderRadius: '24px',
              backgroundColor: 'grey.100',
              p: { xs: 2, sm: 3 },
            }}
          >
            <Skeleton
              variant="rectangular"
              sx={{
                height: { xs: 200, sm: 300 },
                borderRadius: 4,
              }}
            />
            <CardContent>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Skeleton
                  variant="text"
                  sx={{ fontSize: theme.typography.header2xs.fontSize }}
                  width="70%"
                />
                <Skeleton variant="circular" width={40} height={40} />
              </Stack>

              <Stack direction="row" alignItems="center" spacing={1} my={3}>
                <Skeleton variant="circular" width={36} height={36} />
                <Skeleton
                  variant="text"
                  sx={{ fontSize: theme.typography.textLg.fontSize }}
                  width="40%"
                />
                <Skeleton
                  variant="text"
                  sx={{ fontSize: theme.typography.textSm.fontSize }}
                  width="30%"
                />
              </Stack>

              <Skeleton variant="text" sx={{ fontSize: theme.typography.textMd.fontSize, mt: 2 }} />
              <Skeleton
                variant="text"
                sx={{ fontSize: theme.typography.textMd.fontSize }}
                width="80%"
              />
              <Skeleton
                variant="text"
                sx={{ fontSize: theme.typography.textMd.fontSize, mt: 2 }}
                width="50%"
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Stack spacing={{ xs: 1, sm: 2 }}>
            {[1, 2].map(item => (
              <Card
                elevation={0}
                key={item}
                sx={{
                  borderRadius: '24px',
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  alignItems: 'center',
                  p: { xs: 2, sm: 3 },
                  gap: { xs: 2, sm: 3 },
                  backgroundColor: 'grey.100',
                }}
              >
                <Skeleton
                  variant="rectangular"
                  sx={{
                    height: { xs: 200, sm: 259 },
                    width: { xs: '100%', sm: 259 },
                    minWidth: { sm: 259 },
                    borderRadius: '16px',
                  }}
                />
                <CardContent sx={{ flex: 1, width: '100%' }}>
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{ mb: 2 }}
                  >
                    <Skeleton
                      variant="text"
                      sx={{ fontSize: theme.typography.header3xs.fontSize }}
                      width="60%"
                    />
                    <Skeleton variant="circular" width={32} height={32} />
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={2} my={2}>
                    <Skeleton variant="circular" width={32} height={32} />
                    <Skeleton
                      variant="text"
                      sx={{ fontSize: theme.typography.textLg.fontSize }}
                      width="50%"
                    />
                  </Stack>
                  <Skeleton
                    variant="text"
                    sx={{ fontSize: theme.typography.textMd.fontSize, my: 1 }}
                  />
                  <Skeleton
                    variant="text"
                    sx={{ fontSize: theme.typography.textMd.fontSize }}
                    width="70%"
                  />

                  <Skeleton
                    variant="text"
                    sx={{ fontSize: theme.typography.textMd.fontSize, mt: 3 }}
                    width="40%"
                  />
                </CardContent>
              </Card>
            ))}
          </Stack>
        </Grid>
      </Grid>
    </SectionLayout>
  );
};

export default ArticleSectionSkeleton;
