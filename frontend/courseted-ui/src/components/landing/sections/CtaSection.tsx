import Icon from '@/components/ui/Icon';
import { palette } from '@/theme/palette';
import { Box, Button, Stack, Typography } from '@mui/material';
import SectionLayout from '@/components/landing/components/SectionLayout';

const CtaSection = () => {
  return (
    <SectionLayout py={{ xs: 6, md: 10 }} maxWidth="xl">
      <Box
        sx={{
          background: palette.primary[100],
          width: '100%',
          borderRadius: '24px',
          minHeight: { xs: 'auto', md: '440px' },
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          position: 'relative',
          p: { xs: 4, sm: 6, md: 8 },
          overflow: 'hidden',
        }}
      >
        <Icon
          name="CtaArrow"
          sx={{
            position: 'absolute',
            top: '45%',
            left: '35%',
            transform: 'translate(-50%, -50%) rotate(-6deg)',
            width: { xs: '150px', sm: '180px', md: '216px' },
            height: { xs: '90px', sm: '110px', md: '136px' },
            opacity: { xs: 0.3, md: 1 },
            zIndex: 0,
            display: { xs: 'none', sm: 'block' },
          }}
        />
        <Stack
          spacing={{ xs: 2, md: 4 }}
          alignItems="center"
          maxWidth="704px"
          width="100%"
          sx={{ zIndex: 1 }}
        >
          <Typography
            variant="headerMd"
            color="grey.900"
            sx={{
              fontWeight: 'bold',
              textAlign: 'center',
            }}
          >
            Let's get in touch
          </Typography>
          <Typography
            variant="textLg"
            color="grey.900"
            sx={{
              maxWidth: '100%',
            }}
          >
            Having trained over 30,000 QA professionals, we share our extensive knowledge and
            experience through a wide variety of testing courses. Our team are comm...
          </Typography>
          <Button
            variant="contained"
            sx={{
              borderRadius: 100,
              backgroundColor: 'grey.900',
              color: 'white',
              textTransform: 'none',
              px: { xs: 3, sm: 4 },
              py: { xs: 1, sm: 1.5 },
              fontSize: { xs: '1rem', sm: '1.125rem' },
              '&:hover': { backgroundColor: 'grey.800' },
              mt: { xs: 2, md: 3 },
            }}
          >
            Join us today !
          </Button>
        </Stack>
      </Box>
    </SectionLayout>
  );
};

export default CtaSection;
