import { <PERSON><PERSON>, <PERSON>rid2, <PERSON><PERSON>, Typography } from '@mui/material';
import { courses as mockCourses } from '@/data/mockData';
import SectionLayout from '@/components/landing/components/SectionLayout';
import { palette } from '@/theme/palette';
import { DEFAULT_SECTION_VISIBLE } from '@/types/section.types';

import CourseCard from '../components/CourseCard';
import GreenSpan from '../components/GreenSpan';
import Icon from '@/components/ui/Icon';

interface Course {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: string;
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  time: string;
  avatar: string;
  iconName: string;
}

interface CoursesSectionProps {
  courses?: Course[];
}

const CoursesSection = ({ courses = mockCourses }: CoursesSectionProps) => {
  // Transform course data to match CourseCard interface
  const transformCourse = (course: Course) => ({
    ...course,
    id: course.id,
    price: course.price, // Keep as string since CourseCard expects string
  });

  return (
    <SectionLayout py={{ xs: 4, sm: 6, md: 10 }} maxWidth="xl" backgroundColor={palette.grey[200]}>
      {/* <SectionHeader title="courses" /> */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 0 },
          mb: { xs: 4, sm: 8 },
        }}
      >
        <Typography
          variant="headerMd"
          sx={{
            textAlign: { xs: 'center', sm: 'left' },
            fontWeight: 'fontWeightBold',
          }}
        >
          Our recent <GreenSpan>courses</GreenSpan>
        </Typography>

        <Button
          variant="contained"
          sx={{
            borderRadius: 100,
            backgroundColor: 'black',
            color: 'white',
            textTransform: 'none',
            px: { xs: 4, sm: 4 },
            py: { xs: 1, sm: 2 },
            fontSize: { xs: '14px', sm: '16px' },
            '&:hover': { backgroundColor: 'grey.800' },
            display: { xs: 'none', sm: 'flex' },
          }}
          endIcon={
            <Icon
              name="ArrayUpRight"
              sx={{
                color: 'white',
                width: { xs: 12, sm: 16 },
                height: { xs: 20, sm: 27 },
              }}
            />
          }
        >
          View all courses
        </Button>
      </Stack>

      <Grid2
        container
        spacing={{ xs: 2, sm: 3, md: 4 }}
        sx={{
          px: { xs: 1, sm: 0 },
        }}
      >
        {courses.map((course, idx) => {
          return (
            <Grid2
              key={idx}
              size={{ xs: 12, sm: 6, md: 4 }}
              display={'flex'}
              justifyContent={'center'}
              sx={{
                '& > *': {
                  width: '100%',
                  maxWidth: { xs: '100%', sm: '400px', md: 'none' },
                },
              }}
            >
              <CourseCard
                course={transformCourse(course)}
                sectionVisible={DEFAULT_SECTION_VISIBLE}
              />
            </Grid2>
          )
        })}
      </Grid2>

      <Button
        variant="contained"
        sx={{
          borderRadius: 100,
          backgroundColor: 'black',
          color: 'white',
          textTransform: 'none',
          px: { xs: 4, sm: 4 },
          py: { xs: 1, sm: 2 },
          fontSize: { xs: '14px', sm: '16px' },
          '&:hover': { backgroundColor: 'grey.800' },
          display: { xs: 'flex', sm: 'none' },
          mt: { xs: 4, sm: 0 },
          width: '100%',
        }}
        endIcon={
          <Icon
            name="ArrayUpRight"
            sx={{
              color: 'white',
              width: { xs: 12, sm: 16 },
              height: { xs: 20, sm: 27 },
            }}
          />
        }
      >
        View all courses
      </Button>
    </SectionLayout>
  );
};

export default CoursesSection;
