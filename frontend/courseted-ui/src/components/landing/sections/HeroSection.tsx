import {
  Typo<PERSON>,
  <PERSON>ton,
  Stack,
  Paper,
  Avatar,
  Box,
  Grid,
} from '@mui/material';
import Image from 'next/image';
import avatarImage from '@/assets/images/avatar.webp';
import Icon from '@/components/ui/Icon';
import SectionLayout from '@/components/landing/components/SectionLayout';

// interface HeroSectionProps {
//   stats?: {
//     totalStudents: number;
//     totalCourses: number;
//     totalInstructors: number;
//     successRate: number;
//   };
// }

const HeroSection = () => {
  // const theme = useTheme();
  // const isMdUp = useMediaQuery(theme.breakpoints.up('md'), { noSsr: true });

  return (
    <SectionLayout maxWidth="xl" backgroundColor="grey.200" py={{ xs: 6, sm: 8, md: 10 }}>
      <Grid container spacing={{ xs: 3, sm: 4, md: 2 }} alignItems="center" justifyContent="center">
        <Grid item xs={12} md={5} xl={7}>
          <Stack
            spacing={{ xs: 3, sm: 4, md: 4 }}
            sx={{
              // maxHeight: { md: '510px' },
              maxWidth: { md: '728px' },
              textAlign: { xs: 'center', md: 'left' },
              px: { xs: 2, sm: 0 },
            }}
          >
            <Typography
              variant="textXl"
              color="primary.600"
              sx={{
                gap: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: { xs: 'center', md: 'flex-start' },
                '&:hover': {
                  textDecoration: 'underline',
                  textDecorationColor: 'green.600',
                  textDecorationThickness: 2,
                  cursor: 'pointer',
                  textUnderlineOffset: 3,
                  textDecorationStyle: 'solid',
                },
              }}
            >
              <span>✨</span> Join our online community
            </Typography>
            <Typography
              variant={'headerLg'}
              sx={{
                fontWeight: 'bold',
              }}
            >
              Courseted is the best place for learning automation
            </Typography>
            <Typography variant="textLg" color="grey.700" sx={{ fontWeight: 'fontWeightLight' }}>
              Having trained over 30,000 QA professionals, we share our extensive knowledge and
              experience through a wide variety of testing courses. Our team is committed to
              assisting you with your goals, through instructor-led and coaching options.
            </Typography>
            <Stack direction="row" justifyContent={{ xs: 'center', md: 'flex-start' }} spacing={2}>
              <Button
                variant="contained"
                color="primary"
                sx={{
                  borderRadius: 100,
                  height: { xs: '48px', sm: '56px', md: '60px' },
                  width: { xs: '100%', sm: '200px', md: '187px' },
                  maxWidth: { xs: '300px', sm: 'none' },
                  backgroundColor: 'black',
                  fontSize: { xs: 14, sm: 15 },
                  mx: { xs: 'auto', md: 0 },
                }}
                endIcon={
                  <Icon name="ArrayUpRight" sx={{ color: 'white', width: 16, height: 27 }} />
                }
              >
                Get started
              </Button>
            </Stack>
          </Stack>
        </Grid>

        <Grid item xs={12} md={7} xl={5} sx={{ position: 'relative' }}>
          <Box sx={{ display: { xs: 'block', md: 'block' } }}>
            <Stack direction={'row'} spacing={2} sx={{ justifyContent: { xs: 'center', lg: 'end' } }}>
              <Stack direction={'column'} spacing={2}>
                <Paper
                  elevation={0}
                  sx={{
                    borderRadius: '24px',
                    backgroundColor: 'white',
                    maxWidth: '264px',
                    width: '100%',
                    height: '168px',
                    p: 3,
                    position: 'relative',
                  }}
                >
                  <Box position={'absolute'} right={15} top={15}>
                    <Icon name="SprayedDesignLanding" />
                  </Box>
                  <Typography variant="textXl" fontWeight="bold">
                    We have 40+{' '}
                  </Typography>
                  <Typography variant="textXl" fontWeight="bold">
                    Professional Teachers
                  </Typography>
                  <Stack direction="row" spacing={1} mt={1}>
                    {Array.from({ length: 4 }).map((_, index) => (
                      <Avatar key={index} sx={{ width: 44, height: 44 }} src={avatarImage} />
                    ))}
                  </Stack>

                  <Icon
                    name="HeroStarIcon"
                    sx={{
                      position: 'absolute',
                      top: '-25px',
                      left: '-10px',
                      transform: 'translateX(-50%)',
                      display: { xs: 'block', lg: 'block' },
                    }}
                  />
                </Paper>
                <Box
                  sx={{
                    maxWidth: '264px',
                    width: '100%',
                    height: '360px',
                    borderRadius: '16px',
                    overflow: 'hidden',
                    position: 'relative',
                  }}
                >
                  <Image
                    src={'/images/landingCard.webp'}
                    alt="Online training session"
                    fill
                    style={{
                      objectFit: 'cover',
                    }}
                  />
                </Box>
              </Stack>

              <Stack direction="column" spacing={2}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '24px',
                    backgroundColor: 'primary.600',
                    color: 'white',
                    textAlign: 'center',
                    maxWidth: '264px',
                    width: '100%',
                    height: '264px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <Typography fontSize={64} sx={{ fontWeight: 'fontWeightMedium' }}>
                    100+
                  </Typography>
                  <Typography variant="textXl" sx={{ fontWeight: 'fontWeightMedium' }}>
                    professional courses
                  </Typography>
                </Paper>

                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '24px',
                    backgroundColor: 'white',
                    maxWidth: '264px',
                    width: '100%',
                    height: '264px',
                    position: 'relative',
                  }}
                >
                  <Stack
                    direction="row"
                    justifyContent={'space-between'}
                    alignItems="flex-start"
                    mb={3}
                  >
                    <Icon name="VideoCameraIconLanding" />
                    <Icon name="SprayedDesignLanding" />
                  </Stack>
                  <Stack spacing={1}>
                    <Typography variant="header3xs" fontWeight="bold">
                      Join our online training sessions
                    </Typography>
                    <Typography variant="textSm" color="grey.700">
                      Over 500 students trained successfully
                    </Typography>
                  </Stack>

                  <Icon
                    name="HeroStarIconBig"
                    sx={{
                      position: 'absolute',
                      bottom: '-40px',
                      right: '-40px',
                      display: { xs: 'block', lg: 'block' },
                    }}
                  />
                  <Icon
                    name="HeroStarIconSmall"
                    sx={{
                      position: 'absolute',
                      bottom: '20px',
                      right: '-50px',
                      opacity: 0.7,
                      width: '20px',
                      height: '20px',
                      display: { xs: 'block', lg: 'block' },
                    }}
                  />
                </Paper>
              </Stack>
            </Stack>
          </Box>
        </Grid>
      </Grid>
    </SectionLayout>
  );
};

export default HeroSection;
