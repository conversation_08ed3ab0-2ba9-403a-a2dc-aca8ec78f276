import Icon from '@/components/ui/Icon';
import {
  Avatar,
  Button,
  Card,
  CardContent,
  CardMedia,
  Grid,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { articles as mockArticles } from '@/data/mockData';
import SectionLayout from '@/components/landing/components/SectionLayout';
import GreenSpan from '../components/GreenSpan';

interface Article {
  id: number;
  title: string;
  description: string;
  author: string;
  date?: string;
  image: string;
  avatar: string;
}

interface ArticleSectionProps {
  articles?: Article[];
}

const ArticleSection = ({ articles = mockArticles }: ArticleSectionProps) => {

  return (
    <SectionLayout py={{ xs: 4, sm: 6, md: 10 }} maxWidth="xl">
      {/* <SectionHeader title="articles" /> */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 0 },
          mb: { xs: 4, sm: 8 },
        }}
      >
        <Typography
          variant="headerMd"
          sx={{
            textAlign: { xs: 'center', sm: 'left' },
            fontWeight: 'fontWeightBold',
          }}
        >
          Our recent <GreenSpan>articles</GreenSpan>
        </Typography>

        <Button
          variant="contained"
          sx={{
            borderRadius: 100,
            backgroundColor: 'black',
            color: 'white',
            textTransform: 'none',
            px: { xs: 4, sm: 4 },
            py: { xs: 1, sm: 2 },
            fontSize: { xs: '14px', sm: '16px' },
            '&:hover': { backgroundColor: 'grey.800' },
            display: { xs: 'none', sm: 'flex' },
          }}
          endIcon={
            <Icon
              name="ArrayUpRight"
              sx={{
                color: 'white',
                width: { xs: 12, sm: 16 },
                height: { xs: 20, sm: 27 },
              }}
            />
          }
        >
          View all courses
        </Button>
      </Stack>

      <Grid
        container
        spacing={{ xs: 2, sm: 3 }}
        sx={{
          px: { xs: 1, sm: 0 },
        }}
      >
        <Grid item xs={12} md={6}>
          <Card
            elevation={0}
            sx={{
              borderRadius: { xs: '16px', sm: '24px' },
              backgroundColor: 'grey.100',
              p: { xs: 2, sm: 3 },
            }}
          >
            <CardMedia
              component="img"
              image={articles[0].image}
              alt={articles[0].title}
              sx={{
                height: { xs: 180, sm: 240, md: 300 },
                borderRadius: { xs: 2, sm: 4 },
                objectFit: 'cover',
              }}
            />
            <CardContent>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="header2xs" sx={{ fontWeight: 'fontWeightMedium' }}>
                  {articles[0].title}
                </Typography>
                <IconButton>
                  <Icon name="BookmarkAdd2" />
                </IconButton>
              </Stack>

              <Stack direction="row" alignItems="center" spacing={1} my={3}>
                {' '}
                <Avatar
                  sx={{
                    width: { xs: 30, sm: 36 },
                    height: { xs: 30, sm: 36 },
                  }}
                  src={articles[0].avatar}
                />
                <Typography sx={{ fontWeight: 'fontWeightMedium' }} variant="textLg">
                  {articles[0].author}
                </Typography>
                <Typography
                  sx={{ fontWeight: 'fontWeightLight' }}
                  variant="textSm"
                  color="grey.600"
                >
                  • Published on {articles[0].date}
                </Typography>
              </Stack>

              <Typography
                sx={{ fontWeight: 'fontWeightLight' }}
                variant="textMd"
                color="grey.700"
                mt={2}
              >
                {articles[0].description}
              </Typography>

              <Typography
                variant="textMd"
                mt={2}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  fontWeight: 'fontWeightBold',
                  color: 'primary.600',
                }}
              >
                Read full article <Icon name="ArrowRightGreen" sx={{ ml: 1 }} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {articles.length > 1 && (
          <Grid item xs={12} md={6}>
            <Stack spacing={{ xs: 1, sm: 2 }}>
              {articles.slice(1, 3).map(
                (
                  article
                ) => (
                  <Card
                    elevation={0}
                    key={article.id}
                    sx={{
                      borderRadius: '24px',
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      alignItems: 'center',
                      p: { xs: 2, sm: 3 },
                      gap: { xs: 2, sm: 3 },
                      backgroundColor: 'grey.100',
                    }}
                  >
                    <CardMedia
                      component="img"
                      image={article.image}
                      alt={article.title}
                      sx={{
                        height: { xs: 200, sm: 259 },
                        width: { xs: '100%', sm: 259 },
                        minWidth: { sm: 259 },
                        borderRadius: '16px',
                      }}
                    />
                    <CardContent sx={{ flex: 1, width: '100%' }}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        justifyContent="space-between"
                        sx={{ mb: 2 }}
                      >
                        <Typography
                          variant="header3xs"
                          sx={{ fontWeight: 'fontWeightMedium' }}
                          color="grey.900"
                        >
                          {article.title}
                        </Typography>
                        <IconButton>
                          <Icon name="BookmarkAdd2" />
                        </IconButton>
                      </Stack>
                      <Stack direction="row" alignItems="center" spacing={2} my={2}>
                        <Avatar
                          sx={{
                            width: { xs: 28, sm: 32 },
                            height: { xs: 28, sm: 32 },
                          }}
                          src={article.avatar}
                        />
                        <Typography
                          variant="textLg"
                          color="grey.900"
                          sx={{ fontWeight: 'fontWeightMedium' }}
                        >
                          {article.author}
                        </Typography>
                      </Stack>
                      <Typography
                        variant="textMd"
                        color="grey.700"
                        sx={{ fontWeight: 'fontWeightLight' }}
                        my={1}
                      >
                        {article.description}
                      </Typography>
                      <Typography
                        variant="textMd"
                        mt={3}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer',
                          fontWeight: 'fontWeightRegular',
                          color: 'primary.600',
                        }}
                      >
                        Read full article <Icon name="ArrowRightGreen" sx={{ ml: 1 }} />
                      </Typography>
                    </CardContent>
                  </Card>
                )
              )}
            </Stack>
          </Grid>
        )}
      </Grid>

      <Button
        variant="contained"
        sx={{
          borderRadius: 100,
          backgroundColor: 'black',
          color: 'white',
          textTransform: 'none',
          px: { xs: 4, sm: 4 },
          py: { xs: 1, sm: 2 },
          fontSize: { xs: '14px', sm: '16px' },
          '&:hover': { backgroundColor: 'grey.800' },
          display: { xs: 'flex', sm: 'none' },
          mt: { xs: 4, sm: 0 },
          width: '100%',
        }}
        endIcon={
          <Icon
            name="ArrayUpRight"
            sx={{
              color: 'white',
              width: { xs: 12, sm: 16 },
              height: { xs: 20, sm: 27 },
            }}
          />
        }
      >
        View all articles
      </Button>
    </SectionLayout>
  );
};

export default ArticleSection;
