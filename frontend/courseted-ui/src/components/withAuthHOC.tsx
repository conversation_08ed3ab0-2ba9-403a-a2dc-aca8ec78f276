'use client';

import React from 'react';
import WithAuth from './WithAuth';

interface WithAuthOptions {
  redirectTo?: string;
  errorBoundary?: boolean;
}

/** * Higher-Order Component for authentication protection *  * Usage Examples: *  * Basic usage: * export default withAuth(YourComponent); *  * With custom redirect: * export default withAuth(YourComponent, { redirectTo: '/login' }); *  * With ErrorBoundary protection: * export default withAuth(YourComponent, { errorBoundary: true });
 *
 * With both options:
 * export default withAuth(YourComponent, {
 *   redirectTo: '/custom-login',
 *   errorBoundary: true
 * });
 */
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options: WithAuthOptions = {}
) => {
  const { redirectTo = '/auth/login', errorBoundary = false } = options;

  const WrappedComponent = (props: P) => {
    return (
      <WithAuth redirectTo={redirectTo} errorBoundary={errorBoundary}>
        <Component {...props} />
      </WithAuth>
    );
  };

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

export default withAuth;
