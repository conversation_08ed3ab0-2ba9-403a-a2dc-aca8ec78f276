import { Box } from '@mui/material';
import { Outlet, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import Header from './Header';
import Footer from './Footer';

export const MainLayout = () => {
  const location = useLocation();
  return (
    <AnimatePresence mode="wait">
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <Header />
        <Outlet key={location.pathname} />
        <Footer />
      </Box>
    </AnimatePresence>
  );
};
