import Icon from '@/components/ui/Icon';
import {
  Dialog,
  Box,
  Typography,
  InputBase,
  Stack,
  Divider,
  ButtonBase,
  styled,
  DialogContent,
} from '@mui/material';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: theme.shape.borderRadius * 4,
    marginTop: theme.spacing(10), // Default margin for larger screens
    overflow: 'visible',
    [theme.breakpoints.down('sm')]: {
      marginTop: theme.spacing(4), // Reduced margin for mobile
      marginRight: theme.spacing(2),
      marginLeft: theme.spacing(2),
    },
  },
  '& .MuiBackdrop-root': {
    backdropFilter: 'blur(8px)',
    backgroundColor: 'rgba(30, 30, 40, 0.15)',
  },
}));

const SearchInputWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(1.5, 2),
}));

const TrendingList = styled(Stack)(({ theme }) => ({
  padding: theme.spacing(3), // Default padding
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2.5), // Reduced padding for mobile
  },
}));

const TrendingLink = styled(ButtonBase)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius * 2,
  width: '100%',
  textDecoration: 'none',
  transition: theme.transitions.create('background-color', {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    backgroundColor: theme.palette.grey[100],
    '& .arrow-icon': {
      color: theme.palette.grey[800],
    },
  },
}));

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
}

const TRENDING_LINKS = [
  { label: 'Courses', href: '/courses' },
  { label: 'Talents for hire', href: '/talent-hub' },
  { label: 'Contact us', href: '/contact' },
];

const SearchDialog: React.FC<SearchDialogProps> = ({ open, onClose }) => {
  const router = useRouter();
  const [search, setSearch] = useState('');

  return (
    <StyledDialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogContent sx={{ p: 0, '&:first-of-type': { pt: 0 } }}>
        <SearchInputWrapper>
          <Icon name="Search" sx={{ color: 'grey.700', mr: 2 }} />
          <InputBase
            autoFocus
            fullWidth
            placeholder="Search"
            value={search}
            onChange={e => setSearch(e.target.value)}
            sx={{ fontSize: { xs: '16px', sm: '18px' } }} // Responsive font size
          />
        </SearchInputWrapper>

        <Divider />

        <TrendingList>
          <Typography variant="textSm" color="grey.600" fontWeight={600} sx={{ mb: 1 }}>
            Trending
          </Typography>
          <Stack spacing={1}>
            {TRENDING_LINKS.map(link => (
              <TrendingLink key={link.label} onClick={() => router.push(link.href)}>
                <Typography variant="textMd" fontWeight={500} color="grey.900">
                  {link.label}
                </Typography>
                "use client";
                <Icon
                  name="ArrowRight2"
                  className="arrow-icon"
                  sx={{ color: 'grey.400', width: 20, transition: 'color 0.2s' }}
                />
              </TrendingLink>
            ))}
          </Stack>
        </TrendingList>
      </DialogContent>
    </StyledDialog>
  );
};

export default SearchDialog;
