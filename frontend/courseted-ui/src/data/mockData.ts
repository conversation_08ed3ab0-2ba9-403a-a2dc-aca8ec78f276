export const courses = [
  {
    id: 1,
    title: 'Fundamentals of Web Development',
    description: 'Master the basics of HTML, CSS, and JavaScript to build modern web applications.',
    lessons: 35,
    duration: '7h 15m',
    students: 45,
    iconName: "ArrowUpRight01",
    price: '$39.99',
    rating: 4.8,
    instructor: '<PERSON>',
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    category: 'Web Development',
    time: '1h 30m',
  },
  {
    id: 2,
    title: 'Advanced JavaScript Concepts',
    description: 'Deep dive into ES6+, asynchronous programming, and advanced JS patterns.',
    lessons: 28,
    duration: '6h 10m',
    students: 32,
    price: '$34.99',
    rating: 4.6,
    instructor: 'Michael Lee',
    iconName: "ArrowUpRight01",
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    category: 'Web Development',
    time: '1h 30m',
  },
  {
    id: 3,
    title: 'React for Beginners',
    description: 'Learn how to build interactive UIs using React and modern frontend tools.',
    lessons: 42,
    duration: '9h 00m',
    students: 27,
    price: '$44.99',
    rating: 4.9,
    instructor: 'Sara Kim',
    iconName: "ArrowUpRight01",
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    category: 'Web Development',
    time: '1h 30m',
  },
  {
    id: 4,
    title: 'JavaScript for Beginners',
    description: 'Learn how to build interactive UIs using React and modern frontend tools.',
    lessons: 42,
    duration: '9h 00m',
    students: 27,
    price: '$44.99',
    rating: 4.9,
    instructor: 'Sara Kim',
    iconName: "ArrowUpRight01",
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    category: 'Web Development',
    time: '1h 30m',
  },
];

export const articles = [
  {
    id: 1,
    title: 'Getting Started with Frontend Development',
    description:
      'Explore the essential tools and frameworks every frontend developer should know in 2024.',
    author: 'Alice Johnson',
    date: '12 February, 2025',
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 2,
    title: 'Mastering CSS Grid and Flexbox',
    description: 'A comprehensive guide to modern layout techniques for responsive web design.',
    author: 'Michael Lee',
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 3,
    title: 'Understanding TypeScript for JavaScript Developers',
    description: 'Learn how TypeScript can improve your code quality and developer experience.',
    author: 'Sara Kim',
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
];


export default { courses }
