import { useState, useCallback } from 'react';
import * as yup from 'yup';

export interface ValidationErrors {
  [key: string]: string | undefined;
}

export interface UseFormValidationReturn<T> {
  errors: ValidationErrors;
  validateField: (fieldName: keyof T, value: any, formData?: Partial<T>) => Promise<string | undefined>;
  validateForm: (formData: T) => Promise<{ isValid: boolean; errors: ValidationErrors }>;
  clearError: (fieldName: keyof T) => void;
  clearAllErrors: () => void;
  setError: (fieldName: keyof T, message: string) => void;
}

export function useFormValidation<T>(
  schema: yup.ObjectSchema<T>
): UseFormValidationReturn<T> {
  const [errors, setErrors] = useState<ValidationErrors>({});

  const validateField = useCallback(
    async (fieldName: keyof T, value: any, formData?: Partial<T>): Promise<string | undefined> => {
      try {
        const fieldSchema = schema.fields[fieldName as string];
        if (!fieldSchema) return undefined;

        // For fields that depend on other fields (like confirmPassword), we need the full form data
        if (formData && fieldName === 'correctPassword') {
          await schema.validateAt(fieldName as string, { ...formData, [fieldName]: value });
        } else {
          await fieldSchema.validate(value);
        }

        // Clear error if validation passes
        setErrors(prev => ({ ...prev, [fieldName]: undefined }));
        return undefined;
      } catch (error) {
        if (error instanceof yup.ValidationError) {
          setErrors(prev => ({ ...prev, [fieldName]: error.message }));
          return error.message;
        }
        return undefined;
      }
    },
    [schema]
  );

  const validateForm = useCallback(
    async (formData: T): Promise<{ isValid: boolean; errors: ValidationErrors }> => {
      try {
        await schema.validate(formData, { abortEarly: false });
        setErrors({});
        return { isValid: true, errors: {} };
      } catch (error) {
        if (error instanceof yup.ValidationError) {
          const newErrors: ValidationErrors = {};
          error.inner.forEach((err) => {
            if (err.path) {
              newErrors[err.path] = err.message;
            }
          });
          setErrors(newErrors);
          return { isValid: false, errors: newErrors };
        }
        return { isValid: false, errors: {} };
      }
    },
    [schema]
  );

  const clearError = useCallback((fieldName: keyof T) => {
    setErrors(prev => ({ ...prev, [fieldName]: undefined }));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const setError = useCallback((fieldName: keyof T, message: string) => {
    setErrors(prev => ({ ...prev, [fieldName]: message }));
  }, []);

  return {
    errors,
    validateField,
    validateForm,
    clearError,
    clearAllErrors,
    setError,
  };
}
