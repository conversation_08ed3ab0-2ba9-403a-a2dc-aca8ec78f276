import { useState, useEffect, useCallback } from 'react';
import { zoomSDKService } from '@/services/zoom/sdkService';
import { ZoomSDKConfig, MeetingJoinOptions, MeetingControls } from '@/types/zoom';

interface UseZoomOptions {
  config: ZoomSDKConfig;
  autoInit?: boolean;
}

interface UseZoomReturn {
  isInitialized: boolean;
  isJoining: boolean;
  isJoined: boolean;
  error: string | null;
  controls: MeetingControls | null;
  initializeSDK: () => Promise<void>;
  joinMeeting: (options: MeetingJoinOptions) => Promise<void>;
  leaveMeeting: () => Promise<void>;
  clearError: () => void;
}

export const useZoom = (options: UseZoomOptions): UseZoomReturn => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [isJoined, setIsJoined] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [controls, setControls] = useState<MeetingControls | null>(null);

  const initializeSDK = useCallback(async () => {
    try {
      setError(null);
      await zoomSDKService.initializeMeetingSDK(options.config);
      setIsInitialized(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize Zoom SDK');
      setIsInitialized(false);
    }
  }, [options.config]);

  const joinMeeting = useCallback(async (joinOptions: MeetingJoinOptions) => {
    if (!isInitialized) {
      setError('SDK not initialized');
      return;
    }

    try {
      setIsJoining(true);
      setError(null);

      await zoomSDKService.joinMeeting({
        ...joinOptions,
        success: () => {
          setIsJoined(true);
          setIsJoining(false);
          const meetingControls = zoomSDKService.getMeetingControls();
          setControls(meetingControls);
        },
        error: () => {
          setError('Failed to join meeting');
          setIsJoining(false);
          setIsJoined(false);
        }
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join meeting');
      setIsJoining(false);
      setIsJoined(false);
    }
  }, [isInitialized]);

  const leaveMeeting = useCallback(async () => {
    try {
      await zoomSDKService.leaveMeeting();
      setIsJoined(false);
      setControls(null);
    } catch (err) {
      console.error('Error leaving meeting:', err);
      // Still reset state even if leave fails
      setIsJoined(false);
      setControls(null);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-initialize if requested
  useEffect(() => {
    if (options.autoInit && !isInitialized && !error) {
      initializeSDK();
    }
  }, [options.autoInit, isInitialized, error, initializeSDK]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isJoined) {
        zoomSDKService.leaveMeeting().catch(console.error);
      }
      zoomSDKService.cleanup();
    };
  }, [isJoined]);

  return {
    isInitialized,
    isJoining,
    isJoined,
    error,
    controls,
    initializeSDK,
    joinMeeting,
    leaveMeeting,
    clearError,
  };
};
