import { useTheme, useMediaQuery, Breakpoint } from '@mui/material';
import { useMemo } from 'react';

export interface ResponsiveHookReturn {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isSmallScreen: boolean;
  isLargeScreen: boolean;
  currentBreakpoint: Breakpoint;
  isBreakpoint: (breakpoint: Breakpoint) => boolean;
  isAboveBreakpoint: (breakpoint: Breakpoint) => boolean;
  isBelowBreakpoint: (breakpoint: Breakpoint) => boolean;
}

export const useResponsive = (): ResponsiveHookReturn => {
  const theme = useTheme();
  
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('lg'));

  const isMedium = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isLarge = useMediaQuery(theme.breakpoints.between('lg', 'xl'));

  const currentBreakpoint = useMemo((): Breakpoint => {
    if (isMobile) return 'xs';
    if (isTablet) return 'sm';
    if (isMedium) return 'md';
    if (isLarge) return 'lg';
    return 'xl';
  }, [isMobile, isTablet, isMedium, isLarge]);

  // Create hook-based functions that use the already computed values
  const isBreakpoint = useMemo(() => {
    return (breakpoint: Breakpoint): boolean => {
      return currentBreakpoint === breakpoint;
    };
  }, [currentBreakpoint]);

  const isAboveBreakpoint = useMemo(() => {
    return (breakpoint: Breakpoint): boolean => {
      const breakpoints: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl'];
      const currentIndex = breakpoints.indexOf(currentBreakpoint);
      const targetIndex = breakpoints.indexOf(breakpoint);
      return currentIndex >= targetIndex;
    };
  }, [currentBreakpoint]);

  const isBelowBreakpoint = useMemo(() => {
    return (breakpoint: Breakpoint): boolean => {
      const breakpoints: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl'];
      const currentIndex = breakpoints.indexOf(currentBreakpoint);
      const targetIndex = breakpoints.indexOf(breakpoint);
      return currentIndex < targetIndex;
    };
  }, [currentBreakpoint]);

  return {
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,
    currentBreakpoint,
    isBreakpoint,
    isAboveBreakpoint,
    isBelowBreakpoint,
  };
};

// Utility hook for responsive values
export const useResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
}): T | undefined => {
  const { currentBreakpoint } = useResponsive();
  
  return useMemo(() => {
    // Return the value for current breakpoint or fallback to smaller breakpoints
    return (
      values[currentBreakpoint] ||
      (currentBreakpoint === 'xl' && (values.lg || values.md || values.sm || values.xs)) ||
      (currentBreakpoint === 'lg' && (values.md || values.sm || values.xs)) ||
      (currentBreakpoint === 'md' && (values.sm || values.xs)) ||
      (currentBreakpoint === 'sm' && values.xs) ||
      values.xs
    );
  }, [currentBreakpoint, values]);
};
