import { ThemeProvider } from '@mui/material/styles';
import { Provider } from 'react-redux';
import { RouterProvider } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import theme from '@theme';
import { store } from '@app/store';
import { router } from '@routes';
import { CssBaseline } from '@mui/material';

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AnimatePresence mode="wait">
          <RouterProvider router={router} />
        </AnimatePresence>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
