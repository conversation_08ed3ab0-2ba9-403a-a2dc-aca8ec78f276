import { configureStore } from '@reduxjs/toolkit';
import { baseApi } from '@/api/baseApi';
import { meetingApi } from '@/services/zoom/meetingApi';

export const store = configureStore({
  reducer: {
    [baseApi.reducerPath]: baseApi.reducer,
    [meetingApi.reducerPath]: meetingApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'api/executeQuery/fulfilled',
          'api/executeQuery/rejected',
        ],
      },
    }).concat(
      baseApi.middleware,
      meetingApi.middleware
    ),
  devTools: typeof window !== 'undefined' && process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
