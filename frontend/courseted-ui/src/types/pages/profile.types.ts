/**
 * Step label props interface for profile setup
 */
export interface StepLabelProps {
  step: number;
  label: string;
  description: string;
}

/**
 * Profile setup layout props interface
 */
export interface ProfileSetupLayoutProps {
  children: React.ReactNode;
  currentStep: number;
  totalSteps: number;
  stepLabels: StepLabelProps[];
  title: string;
  subtitle?: string;
}

/**
 * Profile form data interface
 */
export interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryId: string;
  bio?: string;
  avatar?: string;
  skills?: string[];
  experience?: string;
  education?: string;
}

/**
 * Profile setup step interface
 */
export interface ProfileSetupStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<any>;
  isCompleted: boolean;
  isOptional?: boolean;
}

/**
 * Student information data interface
 */
export interface StudentInformationData {
  profilePhoto: File | null;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  selectedCountry: any | null; // Country type from countries.types
}

/**
 * Education skills data interface
 */
export interface EducationSkillsData {
  linkedinUrl: string;
  githubUrl: string;
  educationLevel: string;
  major: string;
}

/**
 * Resume upload data interface
 */
export interface ResumeUploadData {
  resumeFile: File | null;
}

/**
 * Complete profile setup data interface
 */
export interface ProfileSetupData {
  studentInformation: StudentInformationData;
  educationSkills: EducationSkillsData;
  resumeUpload: ResumeUploadData;
}

/**
 * Student information step props interface
 */
export interface StudentInformationStepProps {
  data: StudentInformationData;
  onDataChange: (data: StudentInformationData) => void;
  onNext: () => void;
}

/**
 * Education skills step props interface
 */
export interface EducationSkillsStepProps {
  data: EducationSkillsData;
  onDataChange: (data: EducationSkillsData) => void;
  onNext: () => void;
  onPrevious: () => void;
}

/**
 * Resume upload step props interface
 */
export interface ResumeUploadStepProps {
  data: ResumeUploadData;
  onDataChange: (data: ResumeUploadData) => void;
  onPrevious: () => void;
  onFinish: () => void;
}
