/**
 * Article page props interface
 */
export interface ArticlePageProps {
  params: {
    id: string;
  };
}

/**
 * Article metadata interface
 */
export interface ArticleMetadata {
  title: string;
  description: string;
  image?: string;
  author?: string;
  publishedAt?: string;
  tags?: string[];
}

/**
 * Article page data interface
 */
export interface ArticlePageData {
  article: {
    id: number;
    title: string;
    description: string;
    image: string;
    category: string;
    rating: number;
    price: string;
    lessons: number;
    duration: string;
    students: number;
    instructor: string;
    time: string;
    avatar: string;
    iconName: string;
  };
  relatedArticles?: any[];
}
