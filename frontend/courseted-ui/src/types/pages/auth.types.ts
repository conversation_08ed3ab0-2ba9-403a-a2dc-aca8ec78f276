/**
 * Login form data interface
 */
export interface LoginFormData {
  email: string;
  password: string;
}

/**
 * Register form data interface
 */
export interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  countryId: string;
}

/**
 * Sign up form data interface (alternative register form)
 */
export interface SignUpFormData {
  email: string;
  password: string;
  correctPassword: string;
  phoneNumber: string;
  selectedCountry: any | null; // Country type from countries.types
}

/**
 * Forgot password form data interface
 */
export interface ForgotPasswordFormData {
  email: string;
}

/**
 * Reset password form data interface
 */
export interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
  token: string;
}

/**
 * Auth page props interface
 */
export interface AuthPageProps {
  searchParams?: {
    redirect?: string;
    error?: string;
    message?: string;
  };
}
