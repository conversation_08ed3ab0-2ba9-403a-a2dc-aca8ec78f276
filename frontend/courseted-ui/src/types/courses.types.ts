/**
 * Course level types
 */
export type CourseLevel = 'beginner' | 'intermediate' | 'advanced';

/**
 * Main course interface
 */
export interface Course {
  id: string;
  title: string;
  description: string;
  image: string;
  price: number;
  rating: number;
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  avatar: string;
  category: string;
  level: CourseLevel;
  tags: string[];
  isFeatured: boolean;
  isEnrolled?: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Course data for cards and components (with string price)
 */
export interface CourseData {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: string; // String format like "$39.99"
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  time: string;
  avatar: string;
  iconName?: string;
}

/**
 * Create course request interface
 */
export interface CreateCourseRequest {
  title: string;
  description: string;
  image: string;
  price: number;
  category: string;
  level: CourseLevel;
  tags: string[];
  isFeatured?: boolean;
}

/**
 * Course section props interface
 */
export interface CourseSectionProps {
  courses?: CourseData[];
}

/**
 * Course group props interface
 */
export interface CourseGroupProps {
  title: string;
  courses: CourseData[];
  sectionVisible: any; // Will be imported from section.types
}

export type UpdateCourseRequest = Partial<CreateCourseRequest>;

export type PaginationParams = object
