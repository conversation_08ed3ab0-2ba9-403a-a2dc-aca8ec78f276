/**
 * Common interface for course data used in cards and components
 */
export interface CourseCardData {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: string; // String format like "$39.99"
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  time: string;
  avatar: string;
}

/**
 * Common interface for controlling visibility of different sections in course/content cards
 */
export interface SectionVisible {
  rating: boolean;
  category: boolean;
  price: boolean;
  lessons: boolean;
  duration: boolean;
  students: boolean;
  instructor: boolean;
  time: boolean;
  avatar: boolean;
  description: boolean;
}

/**
 * Default visibility configuration - shows all sections
 */
export const DEFAULT_SECTION_VISIBLE: SectionVisible = {
  rating: true,
  category: true,
  price: true,
  lessons: true,
  duration: true,
  students: true,
  instructor: true,
  time: true,
  avatar: true,
  description: true,
};

/**
 * Minimal visibility configuration - shows only essential sections
 */
export const MINIMAL_SECTION_VISIBLE: SectionVisible = {
  rating: true,
  category: true,
  price: true,
  lessons: false,
  duration: false,
  students: false,
  instructor: false,
  time: false,
  avatar: false,
  description: true,
};

/**
 * Compact visibility configuration - for smaller card layouts
 */
export const COMPACT_SECTION_VISIBLE: SectionVisible = {
  rating: true,
  category: true,
  price: true,
  lessons: true,
  duration: true,
  students: false,
  instructor: false,
  time: false,
  avatar: false,
  description: false,
};

/**
 * Card style configuration interface
 */
export interface CardStyle {
  cardBackground: string;
}

/**
 * Props interface for course card components
 */
export interface CourseCardProps {
  course: CourseCardData;
  sectionVisible: SectionVisible;
  cardStyle?: CardStyle;
}
