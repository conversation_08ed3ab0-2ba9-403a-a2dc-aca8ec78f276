/**
 * Validation rule types
 */
export type ValidationRule = 
  | 'required'
  | 'email'
  | 'password'
  | 'phone'
  | 'url'
  | 'number'
  | 'min'
  | 'max'
  | 'minLength'
  | 'maxLength'
  | 'pattern'
  | 'custom';

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  rule: ValidationRule;
  message: string;
  value?: any;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validation schema interface
 */
export interface ValidationSchema {
  [fieldName: string]: {
    rules: ValidationRule[];
    messages?: { [rule: string]: string };
    required?: boolean;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

/**
 * Form validation hook return interface
 */
export interface UseFormValidationReturn {
  validate: (data: Record<string, any>) => ValidationResult;
  validateField: (fieldName: string, value: any) => ValidationError | null;
  getFieldError: (fieldName: string, errors: ValidationError[]) => string | null;
}
