/**
 * Form field types
 */
export type FormFieldType = 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';

/**
 * Form field interface
 */
export interface FormField {
  name: string;
  label: string;
  type: FormFieldType;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  helperText?: string;
  options?: { label: string; value: string | number }[];
  validation?: {
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

/**
 * Form configuration interface
 */
export interface FormConfig {
  fields: FormField[];
  submitLabel?: string;
  resetLabel?: string;
  onSubmit: (data: Record<string, any>) => void | Promise<void>;
  onReset?: () => void;
  loading?: boolean;
  disabled?: boolean;
}

/**
 * Form state interface
 */
export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

/**
 * Form hook return interface
 */
export interface UseFormReturn {
  formState: FormState;
  handleChange: (name: string, value: any) => void;
  handleBlur: (name: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
  handleReset: () => void;
  setFieldValue: (name: string, value: any) => void;
  setFieldError: (name: string, error: string) => void;
  validateField: (name: string, value: any) => string | null;
  validateForm: () => boolean;
}
