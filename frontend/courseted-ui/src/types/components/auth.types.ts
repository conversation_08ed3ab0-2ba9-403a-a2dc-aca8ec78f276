import { ReactNode } from 'react';

/**
 * WithAuth component props interface
 */
export interface WithAuthProps {
  children: ReactNode;
  redirectTo?: string;
  fallback?: ReactNode;
  errorBoundary?: boolean;
}

/**
 * Auth guard props interface
 */
export interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  roles?: string[];
}

/**
 * Auth layout props interface
 */
export interface AuthLayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
  backgroundImage?: string;
}
