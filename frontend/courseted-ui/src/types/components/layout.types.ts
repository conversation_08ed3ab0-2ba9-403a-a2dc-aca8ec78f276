import { ReactNode } from 'react';

/**
 * Main layout props interface
 */
export interface MainLayoutProps {
  children: ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  headerProps?: any;
  footerProps?: any;
}

/**
 * Client only component props interface
 */
export interface ClientOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Page wrapper props interface
 */
export interface PageWrapperProps {
  children: ReactNode;
  title?: string;
  description?: string;
  className?: string;
}
