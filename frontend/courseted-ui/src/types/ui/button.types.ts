import { ButtonProps } from '@mui/material';

/**
 * Custom button variant types
 */
export type ButtonVariant = 'primary' | 'outlined' | 'text';

/**
 * Custom button size types
 */
export type ButtonSize = 'xl' | 'lg' | 'md' | 'sm';

/**
 * Extended button props interface
 */
export interface CustomButtonProps extends Omit<ButtonProps, 'variant' | 'size'> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  responsive?: boolean;
  fullWidthOnMobile?: boolean;
}
