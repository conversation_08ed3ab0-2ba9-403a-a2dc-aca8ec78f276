import { LinkProps } from '@mui/material';

/**
 * Link size types
 */
export type LinkSize = 'lg' | 'md';

/**
 * Link type variants
 */
export type LinkType = 'primary' | 'secondary';

/**
 * Link state types
 */
export type LinkState = 'focus' | 'hover' | 'default' | 'disabled';

/**
 * Custom link props interface
 */
export interface CustomLinkProps extends LinkProps {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  customSize?: LinkSize;
  customType?: LinkType;
}
