// import { SelectProps } from '@mui/material';

import { SelectInputProps } from "@mui/material/Select/SelectInput";

/**
 * Select option interface
 */
export interface SelectOption {
  label: string;
  value: string | number;
}

/**
 * Custom select props interface
 */
export interface CustomSelectProps extends SelectInputProps {
  label: string;
  value: string | number;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  width?: string;
  height?: string;
  options: SelectOption[];
  required?: boolean;
}
