import { BoxProps, ContainerProps, GridProps, StackProps } from '@mui/material';
import { ImageProps } from 'next/image';

/**
 * Responsive breakpoint types
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * Responsive hook return interface
 */
export interface ResponsiveHookReturn {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  currentBreakpoint: Breakpoint;
  isBreakpoint: (breakpoint: Breakpoint) => boolean;
  isAboveBreakpoint: (breakpoint: Breakpoint) => boolean;
  isBelowBreakpoint: (breakpoint: Breakpoint) => boolean;
}

/**
 * Responsive container props
 */
export interface ResponsiveContainerProps extends ContainerProps {
  children: React.ReactNode;
  mobilePadding?: number | string;
  tabletPadding?: number | string;
  desktopPadding?: number | string;
  fullWidthOnMobile?: boolean;
}

/**
 * Responsive box props
 */
export interface ResponsiveBoxProps extends BoxProps {
  children: React.ReactNode;
  mobilePadding?: number | string;
  tabletPadding?: number | string;
  desktopPadding?: number | string;
  mobileMargin?: number | string;
  tabletMargin?: number | string;
  desktopMargin?: number | string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  hideOnDesktop?: boolean;
  showOnlyMobile?: boolean;
  showOnlyTablet?: boolean;
  showOnlyDesktop?: boolean;
}

/**
 * Responsive grid props
 */
export interface ResponsiveGridProps extends GridProps {
  children: React.ReactNode;
  tabletColumns?: number;
  desktopColumns?: number;
  mobileSpacing?: number;
  tabletSpacing?: number;
  desktopSpacing?: number;
}

/**
 * Responsive stack props
 */
export interface ResponsiveStackProps extends StackProps {
  children: React.ReactNode;
  mobileDirection?: 'row' | 'column';
  tabletDirection?: 'row' | 'column';
  desktopDirection?: 'row' | 'column';
  mobileSpacing?: number;
  tabletSpacing?: number;
  desktopSpacing?: number;
  mobileAlign?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  tabletAlign?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  desktopAlign?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  mobileJustify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  tabletJustify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  desktopJustify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
}

/**
 * Responsive image props
 */
export interface ResponsiveImageProps extends Omit<ImageProps, 'width' | 'height'> {
  mobileWidth?: number;
  mobileHeight?: number;
  tabletWidth?: number;
  tabletHeight?: number;
  desktopWidth?: number;
  desktopHeight?: number;
  aspectRatio?: string;
  borderRadius?: string | number;
  containerProps?: BoxProps;
}

/**
 * Breakpoint provider props
 */
export interface BreakpointProviderProps {
  children: React.ReactNode;
}
