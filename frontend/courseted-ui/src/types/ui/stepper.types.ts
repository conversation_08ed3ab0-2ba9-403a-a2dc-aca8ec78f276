/**
 * Step data interface for stepper components
 */
export interface StepData {
  step: number;
  label: string;
  description: string;
}

/**
 * Stepper component props interface
 */
export interface StepperProps {
  activeStep: number;
  steps: StepData[];
  orientation?: 'horizontal' | 'vertical';
  showLastStepLabel?: boolean;
  lastStepLabelText?: string;
}

/**
 * Profile setup step labels interface
 */
export interface ProfileSetupStepLabel {
  step: number;
  label: string;
  description: string;
}
