import { SxProps } from '@mui/system';

/**
 * Icon name type - based on available icons in the IconComponentMappings
 */
export type IconName =
  | 'ArrowUpRight01'
  | 'Tick_02'
  | 'Menu'
  | 'SprayedDesignLanding'
  | 'VideoCameraIconLanding'
  | 'HeroStarIcon'
  | 'HeroStarIconBig'
  | 'HeroStarIconSmall'
  | 'ArrayUpRight'
  | 'CrossIcon'
  | string; // Allow string for extensibility

/**
 * Icon component props interface
 */
export interface IconProps extends React.SVGAttributes<SVGElement> {
  name: IconName;
  className?: string;
  sx?: SxProps;
}
