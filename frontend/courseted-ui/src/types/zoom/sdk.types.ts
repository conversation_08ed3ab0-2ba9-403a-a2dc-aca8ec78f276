/**
 * Zoom SDK configuration interface
 */
export interface ZoomSDKConfig {
  sdkKey: string;
  sdkSecret: string;
  webEndpoint?: string;
  language?: string;
}

/**
 * SDK initialization options
 */
export interface SDKInitOptions {
  leaveUrl?: string;
  success?: () => void;
  error?: (error: any) => void;
}

/**
 * Meeting join options interface
 */
export interface MeetingJoinOptions {
  meetingNumber: string;
  signature: string;
  userName: string;
  userEmail: string;
  password?: string;
  success?: () => void;
  error?: (error: any) => void;
}

/**
 * Video SDK join options interface
 */
export interface VideoSDKJoinOptions {
  sessionName: string;
  token: string;
  userName: string;
  password?: string;
}

/**
 * SDK event handler interface
 */
export interface SDKEventHandler {
  onMeetingStatus?: (status: any) => void;
  onUserJoin?: (user: any) => void;
  onUserLeave?: (user: any) => void;
  onShareScreen?: (data: any) => void;
  onRecordingStatus?: (status: any) => void;
}

/**
 * Meeting controls interface
 */
export interface MeetingControls {
  mute: () => Promise<void>;
  unmute: () => Promise<void>;
  startVideo: () => Promise<void>;
  stopVideo: () => Promise<void>;
  startShare: () => Promise<void>;
  stopShare: () => Promise<void>;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  leaveMeeting: () => Promise<void>;
  endMeeting: () => Promise<void>;
}

/**
 * Zoom Meeting SDK token interface
 */
export interface ZoomMeetingSDKToken {
  signature: string;
  expiresAt: string;
}

/**
 * Zoom Video SDK token interface
 */
export interface ZoomVideoSDKToken {
  token: string;
  expiresAt: string;
}

/**
 * Meeting signature request interface
 */
export interface MeetingSignatureRequest {
  meetingId: string;
  role: number; // 0 for participant, 1 for host
}

/**
 * Video SDK token request interface
 */
export interface VideoSDKTokenRequest {
  sessionName: string;
  role: number;
}
