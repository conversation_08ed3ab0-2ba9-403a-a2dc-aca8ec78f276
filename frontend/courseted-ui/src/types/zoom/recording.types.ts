/**
 * Recording status types
 */
export type RecordingStatus = 'recording' | 'paused' | 'stopped' | 'processing' | 'completed' | 'failed';

/**
 * Recording type enum
 */
export type RecordingType = 'cloud' | 'local';

/**
 * Meeting recording interface
 */
export interface MeetingRecording {
  id: string;
  meetingId: string;
  meetingTopic: string;
  recordingType: RecordingType;
  status: RecordingStatus;
  startTime: string;
  endTime?: string;
  duration?: number; // in seconds
  fileSize?: number; // in bytes
  downloadUrl?: string;
  playUrl?: string;
  password?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Recording file interface
 */
export interface RecordingFile {
  id: string;
  recordingId: string;
  fileName: string;
  fileType: 'MP4' | 'M4A' | 'CHAT' | 'TRANSCRIPT' | 'CSV';
  fileSize: number;
  downloadUrl: string;
  playUrl?: string;
  createdAt: string;
}

/**
 * Recording settings interface
 */
export interface RecordingSettings {
  autoRecording: boolean;
  cloudRecording: boolean;
  localRecording: boolean;
  recordAudio: boolean;
  recordVideo: boolean;
  recordSharedScreen: boolean;
  recordChat: boolean;
  separateAudio: boolean;
}

/**
 * Recording controls interface
 */
export interface RecordingControls {
  startRecording: (type: RecordingType) => Promise<void>;
  stopRecording: () => Promise<void>;
  pauseRecording: () => Promise<void>;
  resumeRecording: () => Promise<void>;
  getRecordingStatus: () => Promise<RecordingStatus>;
}

/**
 * Recording list response interface
 */
export interface RecordingListResponse {
  recordings: MeetingRecording[];
  totalCount: number;
  page: number;
  limit: number;
}
