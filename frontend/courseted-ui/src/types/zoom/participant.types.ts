/**
 * Participant role types
 */
export type ParticipantRole = 'host' | 'co_host' | 'participant' | 'attendee';

/**
 * Participant status types
 */
export type ParticipantStatus = 'joined' | 'left' | 'waiting' | 'admitted';

/**
 * Meeting participant interface
 */
export interface MeetingParticipant {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  role: ParticipantRole;
  status: ParticipantStatus;
  isAudioMuted: boolean;
  isVideoOn: boolean;
  isHandRaised: boolean;
  isScreenSharing: boolean;
  joinedAt: string;
  leftAt?: string;
}

/**
 * Participant controls interface
 */
export interface ParticipantControls {
  mute: (participantId: string) => Promise<void>;
  unmute: (participantId: string) => Promise<void>;
  stopVideo: (participantId: string) => Promise<void>;
  remove: (participantId: string) => Promise<void>;
  makeHost: (participantId: string) => Promise<void>;
  makeCoHost: (participantId: string) => Promise<void>;
  admitFromWaitingRoom: (participantId: string) => Promise<void>;
  putInWaitingRoom: (participantId: string) => Promise<void>;
}

/**
 * Waiting room participant interface
 */
export interface WaitingRoomParticipant {
  id: string;
  userName: string;
  userEmail: string;
  waitingSince: string;
}

/**
 * Participant list props interface
 */
export interface ParticipantListProps {
  participants: MeetingParticipant[];
  waitingRoomParticipants: WaitingRoomParticipant[];
  currentUser: MeetingParticipant;
  controls?: ParticipantControls;
  onParticipantAction?: (action: string, participantId: string) => void;
}
