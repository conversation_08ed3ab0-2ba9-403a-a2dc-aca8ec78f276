/**
 * Meeting status types
 */
export type MeetingStatus = 'scheduled' | 'live' | 'ended' | 'cancelled';

/**
 * Meeting type enum
 */
export type MeetingType = 'class' | 'webinar' | 'office_hours' | 'group_study';

/**
 * Zoom meeting interface
 */
export interface ZoomMeeting {
  id: string;
  meetingId: string;
  topic: string;
  description?: string;
  type: MeetingType;
  status: MeetingStatus;
  startTime: string;
  duration: number; // in minutes
  timezone: string;
  password?: string;
  joinUrl: string;
  startUrl: string;
  hostId: string;
  hostEmail: string;
  maxParticipants: number;
  isRecordingEnabled: boolean;
  isWaitingRoomEnabled: boolean;
  isMuteOnEntry: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create meeting request interface
 */
export interface CreateMeetingRequest {
  topic: string;
  description?: string;
  type: MeetingType;
  startTime: string;
  duration: number;
  timezone?: string;
  password?: string;
  maxParticipants?: number;
  isRecordingEnabled?: boolean;
  isWaitingRoomEnabled?: boolean;
  isMuteOnEntry?: boolean;
  courseId?: string;
  invitedUserIds?: string[];
}

/**
 * Update meeting request interface
 */
export interface UpdateMeetingRequest {
  topic?: string;
  description?: string;
  startTime?: string;
  duration?: number;
  password?: string;
  maxParticipants?: number;
  isRecordingEnabled?: boolean;
  isWaitingRoomEnabled?: boolean;
  isMuteOnEntry?: boolean;
}

/**
 * Meeting list response interface
 */
export interface MeetingListResponse {
  meetings: ZoomMeeting[];
  totalCount: number;
  page: number;
  limit: number;
}

/**
 * Meeting filters interface
 */
export interface MeetingFilters {
  status?: MeetingStatus;
  type?: MeetingType;
  hostId?: string;
  courseId?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * Join meeting request interface
 */
export interface JoinMeetingRequest {
  meetingId: string;
  password?: string;
  userName: string;
  userEmail: string;
}

/**
 * Meeting invitation interface
 */
export interface MeetingInvitation {
  id: string;
  meetingId: string;
  userId: string;
  userEmail: string;
  userName: string;
  invitedAt: string;
  status: 'pending' | 'accepted' | 'declined';
}
