/**
 * Base API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp?: string;
}

/**
 * Paginated response interface
 */
export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * API error response interface
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp?: string;
}

/**
 * File upload response interface
 */
export interface FileUploadResponse {
  success: boolean;
  data: {
    url: string;
    fileName: string;
    size: number;
    mimeType: string;
    uploadedAt: string;
  };
}

/**
 * Bulk operation response interface
 */
export interface BulkOperationResponse<T = any> {
  success: boolean;
  data: {
    successful: T[];
    failed: Array<{
      item: T;
      error: string;
    }>;
    total: number;
    successCount: number;
    failureCount: number;
  };
}
