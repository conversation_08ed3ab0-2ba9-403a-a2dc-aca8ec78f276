/**
 * API error codes
 */
export type ApiErrorCode = 
  | 'VALIDATION_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'AUTHORIZATION_ERROR'
  | 'NOT_FOUND'
  | 'CONFLICT'
  | 'RATE_LIMIT_EXCEEDED'
  | 'SERVER_ERROR'
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * API error interface
 */
export interface ApiError {
  code: ApiErrorCode;
  message: string;
  details?: any;
  statusCode?: number;
  timestamp?: string;
}

/**
 * Validation error details
 */
export interface ValidationErrorDetails {
  field: string;
  message: string;
  value?: any;
}

/**
 * Error boundary props interface
 */
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Error page props interface
 */
export interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}
