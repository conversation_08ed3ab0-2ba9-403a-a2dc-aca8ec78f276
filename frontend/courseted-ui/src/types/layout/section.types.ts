import { ContainerProps } from '@mui/material';

/**
 * Section layout props interface
 */
export interface SectionLayoutProps extends ContainerProps {
  children: React.ReactNode;
  backgroundColor?: string;
  fullWidth?: boolean;
  noPadding?: boolean;
}

/**
 * Section header props interface
 */
export interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  centered?: boolean;
  action?: React.ReactNode;
}

/**
 * Hero section props interface
 */
export interface HeroSectionProps {
  title: string;
  subtitle?: string;
  description?: string;
  backgroundImage?: string;
  actions?: React.ReactNode[];
  stats?: {
    totalStudents: number;
    totalCourses: number;
    totalInstructors: number;
    successRate: number;
  };
}

/**
 * CTA section props interface
 */
export interface CtaSectionProps {
  title: string;
  description?: string;
  primaryAction?: {
    label: string;
    href: string;
  };
  secondaryAction?: {
    label: string;
    href: string;
  };
  backgroundImage?: string;
}
