/**
 * Navigation link interface
 */
export interface NavLink {
  label: string;
  href: string;
  icon?: string;
  isExternal?: boolean;
  children?: NavLink[];
}

/**
 * Header props interface
 */
export interface HeaderProps {
  navLinks?: NavLink[];
  showSearch?: boolean;
  showAuth?: boolean;
  transparent?: boolean;
}

/**
 * Search dialog props interface
 */
export interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
}
