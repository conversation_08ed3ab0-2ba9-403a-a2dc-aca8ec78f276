/**
 * Navigation item interface
 */
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: string;
  badge?: string | number;
  isActive?: boolean;
  children?: NavigationItem[];
}

/**
 * Sidebar navigation props interface
 */
export interface SidebarNavigationProps {
  items: NavigationItem[];
  collapsed?: boolean;
  onItemClick?: (item: NavigationItem) => void;
}

/**
 * Breadcrumb item interface
 */
export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

/**
 * Breadcrumb props interface
 */
export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
}
