export interface Country {
  id: string; // Unique identifier for the country
  name: string; // Name of the country
  code: string; // ISO 3166-1 alpha-2 country code (e.g., US)
  iso3: string; // ISO 3166-1 alpha-3 country code (e.g., USA)
  phoneCode: string; // International dialing code (e.g., +1)
  flag: string; // Emoji flag of the country (e.g., 🇺🇸)
  createdAt: string; // Date when the country was created (ISO format)
  updatedAt: string; // Date when the country was last updated (ISO format)
}
