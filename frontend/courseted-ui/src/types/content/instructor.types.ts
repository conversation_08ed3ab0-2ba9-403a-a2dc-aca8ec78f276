/**
 * Instructor data interface
 */
export interface Instructor {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  expertise: string[];
  rating: number;
  totalStudents: number;
  totalCourses: number;
  experience: string;
  isVerified: boolean;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Instructor card props interface
 */
export interface InstructorCardProps {
  instructor: Instructor;
  showStats?: boolean;
  compact?: boolean;
}
