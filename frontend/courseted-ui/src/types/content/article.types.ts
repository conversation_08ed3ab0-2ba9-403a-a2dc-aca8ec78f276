/**
 * Article data interface
 */
export interface Article {
  id: number;
  title: string;
  description: string;
  author: string;
  date?: string;
  image: string;
  avatar: string;
  category?: string;
  rating?: number;
  price?: string;
  lessons?: number;
  duration?: string;
  students?: number;
  instructor?: string;
  time?: string;
  iconName?: string;
}

/**
 * Article section props interface
 */
export interface ArticleSectionProps {
  articles?: Article[];
}

/**
 * Article page props interface
 */
export interface ArticlePageProps {
  params: {
    id: string;
  };
}

/**
 * Article card props interface
 */
export interface ArticleCardProps {
  article: Article;
  onClick?: (article: Article) => void;
}

/**
 * Article detail interface (for individual article pages)
 */
export interface ArticleDetail {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: string;
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  time: string;
  avatar: string;
  iconName: string;
}
