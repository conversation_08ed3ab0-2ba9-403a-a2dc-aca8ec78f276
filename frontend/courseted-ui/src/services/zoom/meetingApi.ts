import { createApi } from '@reduxjs/toolkit/query/react';
import {
  ZoomMeeting,
  CreateMeetingRequest,
  UpdateMeetingRequest,
  MeetingFilters,
  JoinMeetingRequest,
  MeetingInvitation,
} from '@/types/zoom/meeting.types';
import { ApiResponse, PaginatedResponse } from '@/types/api';
import { MeetingSignatureRequest, VideoSDKTokenRequest, ZoomVideoSDKToken } from '@/types/zoom/sdk.types';
import { RecordingListResponse } from '@/types/zoom/recording.types';
import { axiosBaseQuery } from '@/api/baseQuery';

const getApiBaseUrl = () => {
  if (typeof window === 'undefined') {
    // Server-side: use internal URL or default
    return process.env.API_URL || 'http://localhost:4000';
  }
  // Client-side: use public env var or default
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
};

// Create the meeting API slice
export const meetingApi = createApi({
  reducerPath: 'meetingApi',
  baseQuery: axiosBaseQuery({
    baseUrl: `${getApiBaseUrl()}/v1/meetings`,
  }),
  tagTypes: ['Meeting', 'Invitation', 'Recording'],
  endpoints: (builder) => ({
    // Get all meetings with filters
    getMeetings: builder.query<PaginatedResponse<any>, {
      page?: number;
      limit?: number;
      filters?: MeetingFilters;
    }>({
      query: ({ page = 1, limit = 10, filters = {} }) => ({
        url: '',
        method: 'GET',
        params: { page, limit, ...filters },
      }),
      providesTags: ['Meeting'],
    }),

    // Get meeting by ID
    getMeetingById: builder.query<ZoomMeeting, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => {
        console.log('result:::', result, 'error:::', error, 'id:::', id);
        return [{ type: 'Meeting', id }]
      },
    }),

    // Create new meeting (Admin only)
    createMeeting: builder.mutation<ApiResponse<ZoomMeeting>, CreateMeetingRequest>({
      query: (meetingData) => ({
        url: '',
        method: 'POST',
        data: meetingData,
      }),
      invalidatesTags: ['Meeting'],
    }),

    // Update meeting (Admin/Host only)
    updateMeeting: builder.mutation<ApiResponse<ZoomMeeting>, {
      id: string;
      data: UpdateMeetingRequest;
    }>({
      query: ({ id, data }) => ({
        url: `/${id}`,
        method: 'PUT',
        data: data,
      }),
      invalidatesTags: (result, error, { id }) => {
        console.log('result:::', result, 'error:::', error, 'id:::', id);
        return [{ type: 'Meeting', id }]
      },
    }),

    // Delete meeting (Admin/Host only)
    deleteMeeting: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => {
        console.log('result:::', result, 'error:::', error, 'id:::', id);
        return [{ type: 'Meeting', id }]
      },
    }),

    // Get meeting signature for SDK
    getMeetingSignature: builder.query<{ signature: string }, MeetingSignatureRequest>({
      query: ({ meetingId, role }) => ({
        url: `/signature/${meetingId}`,
        method: 'GET',
        params: { role },
      }),
    }),

    // Get Video SDK token
    getVideoSDKToken: builder.query<ApiResponse<ZoomVideoSDKToken>, VideoSDKTokenRequest>({
      query: ({ sessionName, role }) => ({
        url: '/video-sdk/token',
        method: 'GET',
        params: { sessionName, role },
      }),
    }),

    // Join meeting
    joinMeeting: builder.mutation<ApiResponse<{ joinUrl: string }>, JoinMeetingRequest>({
      query: (joinData) => ({
        url: '/join',
        method: 'POST',
        data: joinData,
      }),
    }),

    // Get meeting invitations
    getMeetingInvitations: builder.query<ApiResponse<MeetingInvitation[]>, string>({
      query: (meetingId) => ({
        url: `/invitations/${meetingId}`,
        method: 'GET',
      }),
      providesTags: ['Invitation'],
    }),

    // Send meeting invitations (Admin/Host only)
    sendInvitations: builder.mutation<ApiResponse<void>, {
      meetingId: string;
      userIds: string[];
    }>({
      query: ({ meetingId, userIds }) => ({
        url: `/invitations/${meetingId}`,
        method: 'POST',
        data: { userIds },
      }),
      invalidatesTags: ['Invitation'],
    }),

    // Get user's upcoming meetings
    getUpcomingMeetings: builder.query<ApiResponse<ZoomMeeting[]>, void>({
      query: () => ({
        url: '/upcoming',
        method: 'GET',
      }),
      providesTags: ['Meeting'],
    }),

    // Get user's meeting history
    getMeetingHistory: builder.query<PaginatedResponse<ZoomMeeting>, {
      page?: number;
      limit?: number;
    }>({
      query: ({ page = 1, limit = 10 }) => ({
        url: '/history',
        method: 'GET',
        params: { page, limit },
      }),
    }),

    // Get meeting recordings
    getMeetingRecordings: builder.query<RecordingListResponse, {
      meetingId?: string;
      page?: number;
      limit?: number;
    }>({
      query: ({ meetingId, page = 1, limit = 10 }) => ({
        url: '/recordings',
        method: 'GET',
        params: { meetingId, page, limit },
      }),
      providesTags: ['Recording'],
    }),

    // Start meeting recording
    startRecording: builder.mutation<ApiResponse<void>, {
      meetingId: string;
      type: 'cloud' | 'local';
    }>({
      query: ({ meetingId, type }) => ({
        url: `/recordings/${meetingId}/start`,
        method: 'POST',
        data: { type },
      }),
      invalidatesTags: ['Recording'],
    }),

    // Stop meeting recording
    stopRecording: builder.mutation<ApiResponse<void>, string>({
      query: (meetingId) => ({
        url: `/recordings/${meetingId}/stop`,
        method: 'POST',
      }),
      invalidatesTags: ['Recording'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetMeetingsQuery,
  useGetMeetingByIdQuery,
  useCreateMeetingMutation,
  useUpdateMeetingMutation,
  useDeleteMeetingMutation,
  useGetMeetingSignatureQuery,
  useGetVideoSDKTokenQuery,
  useJoinMeetingMutation,
  useGetMeetingInvitationsQuery,
  useSendInvitationsMutation,
  useGetUpcomingMeetingsQuery,
  useGetMeetingHistoryQuery,
  useGetMeetingRecordingsQuery,
  useStartRecordingMutation,
  useStopRecordingMutation,
} = meetingApi;
