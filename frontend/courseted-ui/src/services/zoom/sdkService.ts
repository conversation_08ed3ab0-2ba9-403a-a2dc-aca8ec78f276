import {
  ZoomSDKConfig,
  SDKInitOptions,
  MeetingJoinOptions,
  VideoSDKJoinOptions,
  MeetingControls
} from '@/types/zoom';

declare global {
  interface Window {
    ZoomMtg: any;
    ZoomVideo: any;
  }
}

class ZoomSDKService {
  private isInitialized = false;
  private meetingSDK: any = null;
  private videoSDK: any = null;
  private eventHandlers: Map<string, ((...args: any[]) => void)[]> = new Map();

  /**
   * Initialize Zoom Meeting SDK
   */
  async initializeMeetingSDK(config: ZoomSDKConfig, options?: SDKInitOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        reject(new Error('Zoom SDK can only be initialized in browser environment'));
        return;
      }

      console.log('Initializing Zoom SDK with config:', config);
      console.log('SDK options:', options);

      // Try to use a simpler approach first - just resolve as initialized
      // The actual meeting joining will be handled via redirect to Zoom's web client
      this.isInitialized = true;
      console.log('Zoom SDK marked as initialized (using web client fallback)');
      resolve();
    });
  }



  /**
   * Initialize Zoom Video SDK
   */
  async initializeVideoSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        reject(new Error('Zoom Video SDK can only be initialized in browser environment'));
        return;
      }

      // Load Zoom Video SDK if not already loaded
      if (!window.ZoomVideo) {
        const script = document.createElement('script');
        script.src = 'https://source.zoom.us/videosdk/web/2.2.5/lib/index.js';
        script.onload = () => {
          this.videoSDK = window.ZoomVideo.createClient();
          resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
      } else {
        this.videoSDK = window.ZoomVideo.createClient();
        resolve();
      }
    });
  }

  /**
   * Join a meeting using web client fallback
   */
  async joinMeeting(options: MeetingJoinOptions): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Meeting SDK not initialized');
    }

    console.log('Joining meeting with options:', options);

    // Use Zoom's web client as a fallback
    const joinUrl = `https://zoom.us/wc/join/${options.meetingNumber}`;
    const params = new URLSearchParams({
      pwd: options.password || '',
      uname: options.userName,
      email: options.userEmail || ''
    });

    const fullUrl = `${joinUrl}?${params.toString()}`;

    // Open in a new window/tab
    window.open(fullUrl, '_blank', 'width=1200,height=800');

    // Call success callback
    options.success?.();

    return Promise.resolve();
  }

  /**
   * Join a session using Video SDK
   */
  async joinVideoSession(options: VideoSDKJoinOptions): Promise<void> {
    if (!this.videoSDK) {
      throw new Error('Video SDK not initialized');
    }

    try {
      await this.videoSDK.join({
        sessionName: options.sessionName,
        token: options.token,
        userName: options.userName,
        password: options.password
      });
    } catch (error) {
      throw new Error(`Failed to join video session: ${error}`);
    }
  }

  /**
   * Leave current meeting (fallback implementation)
   */
  async leaveMeeting(): Promise<void> {
    console.log('Leave meeting action - user should close the Zoom web client window');
    return Promise.resolve();
  }

  /**
   * Leave current video session
   */
  async leaveVideoSession(): Promise<void> {
    if (this.videoSDK) {
      await this.videoSDK.leave();
    }
  }

  /**
   * Get meeting controls (fallback implementation)
   */
  getMeetingControls(): MeetingControls | null {
    if (!this.isInitialized) return null;

    // Return mock controls since we're using web client fallback
    return {
      mute: async () => {
        console.log('Mute action - handled by Zoom web client');
        return Promise.resolve();
      },
      unmute: async () => {
        console.log('Unmute action - handled by Zoom web client');
        return Promise.resolve();
      },
      startVideo: async () => {
        console.log('Start video action - handled by Zoom web client');
        return Promise.resolve();
      },
      stopVideo: async () => {
        console.log('Stop video action - handled by Zoom web client');
        return Promise.resolve();
      },
      startShare: async () => {
        console.log('Start share action - handled by Zoom web client');
        return Promise.resolve();
      },
      stopShare: async () => {
        console.log('Stop share action - handled by Zoom web client');
        return Promise.resolve();
      },
      startRecording: async () => {
        console.log('Start recording action - handled by Zoom web client');
        return Promise.resolve();
      },
      stopRecording: async () => {
        console.log('Stop recording action - handled by Zoom web client');
        return Promise.resolve();
      },
      leaveMeeting: async () => {
        return this.leaveMeeting();
      },
      endMeeting: async () => {
        console.log('End meeting action - handled by Zoom web client');
        return Promise.resolve();
      }
    };
  }

  /**
   * Add event handler
   */
  addEventListener(event: string, handler: (...args: any[]) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);

    // Register with SDK if available
    if (this.meetingSDK) {
      this.meetingSDK.on(event, handler);
    }
  }

  /**
   * Remove event handler
   */
  removeEventListener(event: string, handler: (...args: any[]) => void): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }

    // Unregister from SDK if available
    if (this.meetingSDK) {
      this.meetingSDK.off(event, handler);
    }
  }

  /**
   * Cleanup SDK resources
   */
  cleanup(): void {
    this.eventHandlers.clear();
    this.isInitialized = false;
    this.meetingSDK = null;
    this.videoSDK = null;
  }

  /**
   * Check if SDK is initialized
   */
  get initialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const zoomSDKService = new ZoomSDKService();
