import { createBrowserRouter } from 'react-router-dom';
// import { PageTransition } from '@components/transitions/PageTransition';
// import { MainLayout } from '@/components/common/MainLayout';
// import Error from '@/components/Error';

// // Pages
// import LandingPage from '@/pages/landing';
// import AcademyPage from '@/pages/academy/AcademyPage';
// import TalentHubPage from '@/pages/talenthub/TalentHubPage';
// import WebinarsPage from '@/pages/webinars/WebinarsPage';
// import ContactPage from '@/pages/contact/ContactPage';
// import LoginPage from '@/pages/authentication/LoginPage';
// import RegisterPage from '@/pages/authentication/RegisterPage';
// import ForgotPasswordPage from '@/pages/authentication/ForgotPasswordPage';
// import VerifyMailPage from '@/pages/authentication/VerifyMailPage';
// import ResetPasswordPage from '@/pages/authentication/ResetPasswordPage';
// import ArticlesPage from '@/pages/Articles/ArticlesPage';
// import ProfileSetupPage from '@/pages/profile-setup/ProfileSetupPage';

// // Define routes
// const mainRoutes = [
//   { index: true, element: <LandingPage /> },
//   { path: '/academy', element: <AcademyPage /> },
//   { path: '/talent-hub', element: <TalentHubPage /> },
//   { path: '/webinars', element: <WebinarsPage /> },
//   { path: '/articles', element: <ArticlesPage /> },
//   { path: '/contact', element: <ContactPage /> },
//   { path: '/profile-setup', element: <ProfileSetupPage /> },
// ];

// const authRoutes = [
//   { path: '/login', element: <PageTransition><LoginPage /></PageTransition> },
//   { path: '/register', element: <RegisterPage /> },
//   { path: '/forgot-password', element: <ForgotPasswordPage /> },
//   { path: '/email/verify', element: <VerifyMailPage /> },
//   { path: '/reset-password', element: <ResetPasswordPage /> },
// ];

export const router = createBrowserRouter([
  {
    path: '/',
    // element: <MainLayout />,
    // children: mainRoutes,
  },
  // ...authRoutes,
  // {
  //   path: '*',
  //   element: <Error />,
  // },
]);
