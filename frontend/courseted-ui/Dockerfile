FROM node:24-alpine

WORKDIR /app

# Copy package files for better caching
COPY package*.json ./

# Install dependencies (including dev dependencies for development)
RUN npm ci \
    && npm cache clean --force

# Copy only essential source files
COPY src ./src
COPY app ./app
COPY public ./public
COPY next.config.js tsconfig*.json ./

# Expose port for Next.js dev server
EXPOSE 3000

# Set environment variables
ENV PORT=3000 \
    HOSTNAME="0.0.0.0" \
    NODE_ENV=development \
    NEXT_TELEMETRY_DISABLED=1

# Start Next.js development server with hot reload
CMD ["npm", "run", "dev"]
