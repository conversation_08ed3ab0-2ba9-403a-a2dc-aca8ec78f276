#!/bin/bash

  <PERSON><PERSON>CH_NAME="CTED-30"
  BRANCH_NAME=$(echo $BRANCH_NAME | sed 's/\//-/g')
  IMAGE_TAG=$(aws ecr describe-images \
  --repository-name "courseted-ui" \
  --query 'sort_by(imageDetails,& imagePushedAt)[*].imageTags[*]' | grep -i "$BRANCH_NAME" | tail -1 | awk -F'["|"]' '{print $2}')
  if [ ! -n "${IMAGE_TAG}" ]
  then
  echo "Unable to pull image tag with prefix $BRANCH_NAME, please check branch or image $BRANCH_NAME exists"
  exit 1
  else
  echo "image_tag=${IMAGE_TAG}" >> $GITHUB_OUTPUT
  echo "Using image tag: ${IMAGE_TAG}"
  fi

  aws ecr get-login-password | docker login --username AWS --password-stdin 588738611701.dkr.ecr.us-east-1.amazonaws.com
