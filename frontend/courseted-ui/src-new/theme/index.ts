import { createTheme, responsiveFontSizes } from '@mui/material/styles';
import { palette } from './palette';

let theme = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
  spacing: 8, // Base spacing unit
  typography: {
    fontFamily: ['Rethink Sans'].join(','),
    button: {
      textTransform: 'none'
    },
    fontWeightBold: 700,
    fontWeightMedium: 600,
    fontWeightRegular: 500,
    fontWeightLight: 400,
    fontSize: 14,
    // Responsive headers
    headerXl: {
      fontSize: '80px',
      lineHeight: '88px',
    },
    headerLg: {
      fontSize: '64px',
      lineHeight: '72px',
    },
    headerMd: {
      fontSize: '56px',
      lineHeight: '64px',
    },
    headerSm: {
      fontSize: '48px',
      lineHeight: '56px',
    },
    headerXs: {
      fontSize: '40px',
      lineHeight: '48px',
    },
    header2xs: {
      fontSize: '32px',
      lineHeight: '40px',
    },
    header3xs: {
      fontSize: '24px',
      lineHeight: '32px',
    },
    // Responsive text
    textXl: {
      fontSize: '20px',
      lineHeight: '30px',
    },
    textLg: {
      fontSize: '18px',
      lineHeight: '28px',
    },
    textMd: {
      fontSize: '16px',
      lineHeight: '24px',
    },
    textSm: {
      fontSize: '14px',
      lineHeight: '20px',
    },
    textXs: {
      fontSize: '12px',
      lineHeight: '18px',
    },
  },
  palette: {
    primary: palette.primary,
    error: palette.red,
    warning: palette.orange,
    success: palette.green,
    grey: palette.grey,
    green: palette.green,
    red: palette.red,
    orange: palette.orange,
    blue: palette.blue,
    others: palette.others,
  },
  components: {
    MuiButton: {
      defaultProps: {
        disableRipple: true,
      },
      styleOverrides: {
        root: {
          // Responsive button sizing
          '@media (max-width:600px)': {
            minHeight: '40px',
            fontSize: '14px',
            padding: '8px 16px',
          },
        },
      },
    },
    MuiCheckbox: {
      defaultProps: {
        disableRipple: true
      },
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            padding: '6px',
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            fontSize: '14px',
          },
        },
        asterisk: {
          color: palette.red[500]
        }
      }
    },
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: '16px',
          paddingRight: '16px',
          '@media (min-width:600px)': {
            paddingLeft: '24px',
            paddingRight: '24px',
          },
          '@media (min-width:900px)': {
            paddingLeft: '32px',
            paddingRight: '32px',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '16px',
          '@media (max-width:600px)': {
            borderRadius: '12px',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiInputBase-root': {
            '@media (max-width:600px)': {
              fontSize: '16px', // Prevents zoom on iOS
            },
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            padding: '8px',
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          // Ensure text is readable on mobile
          '@media (max-width:600px)': {
            wordBreak: 'break-word',
            hyphens: 'auto',
          },
        },
      },
    },
  },
});
theme = responsiveFontSizes(theme);

export default theme;
