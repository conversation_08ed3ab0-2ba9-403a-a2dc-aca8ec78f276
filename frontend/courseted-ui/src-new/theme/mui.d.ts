import '@mui/material/styles';

interface PaletteColor {
  main: string;
  light: string;
  dark: string;
  900: string;
  800?: string;
  700: string;
  600?: string;
  500: string;
  400?: string;
  300?: string;
  200: string;
  100: string;
  50?: string;
}

interface OtherPaletteColors {
  iris: string;
  tulipTree: string;
}

declare module '@mui/material/styles' {
  interface PaletteOptions {
    green?: CustomPaletteColor;
    red?: CustomPaletteColor;
    orange?: CustomPaletteColor;
    blue?: CustomPaletteColor;
    others?: OtherPaletteColors;
  }

  interface Palette {
    green: PaletteColor;
    red: PaletteColor;
    orange: PaletteColor;
    blue: PaletteColor;
    others: OtherPaletteColors;
  }
}

declare module '@mui/material/styles/createTypography' {
  interface TypographyOptions {
    xl?: React.CSSProperties;
    l?: React.CSSProperties;
    md?: React.CSSProperties;
    sm?: React.CSSProperties;
    xs?: React.CSSProperties;
  }
}

declare module '@mui/material/Typography/Typography' {
  interface TypographyPropsVariantOverrides {
    xl: true;
    l: true;
    md: true;
    sm: true;
    xs: true;
  }
}

import '@mui/material/styles';
import '@mui/material/Typography';
declare module '@mui/material/styles' {
  interface TypographyVariants {
    headerXl: React.CSSProperties;
    headerLg: React.CSSProperties;
    headerMd: React.CSSProperties;
    headerSm: React.CSSProperties;
    headerXs: React.CSSProperties;
    header2xs: React.CSSProperties;
    header3xs: React.CSSProperties;
    textXl: React.CSSProperties;
    textLg: React.CSSProperties;
    textMd: React.CSSProperties;
    textSm: React.CSSProperties;
    textXs: React.CSSProperties;
  }
  interface TypographyVariantsOptions {
    headerXl?: React.CSSProperties;
    headerLg?: React.CSSProperties;
    headerMd?: React.CSSProperties;
    headerSm?: React.CSSProperties;
    headerXs?: React.CSSProperties;
    header2xs?: React.CSSProperties;
    header3xs?: React.CSSProperties;
    textXl?: React.CSSProperties;
    textLg?: React.CSSProperties;
    textMd?: React.CSSProperties;
    textSm?: React.CSSProperties;
    textXs?: React.CSSProperties;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    headerXl: true;
    headerLg: true;
    headerMd: true;
    headerSm: true;
    headerXs: true;
    header2xs: true;
    header3xs: true;
    textXl: true;
    textLg: true;
    textMd: true;
    textSm: true;
    textXs: true;

  }
}

declare module '@mui/material/styles/createTypography' {
  interface TypographyOptions {
    headerXl?: React.CSSProperties;
    headerLg?: React.CSSProperties;
    headerMd?: React.CSSProperties;
    headerSm?: React.CSSProperties;
    headerXs?: React.CSSProperties;
    header2xs?: React.CSSProperties;
    header3xs?: React.CSSProperties;
    textXl?: React.CSSProperties;
    textLg?: React.CSSProperties;
    textMd?: React.CSSProperties;
    textSm?: React.CSSProperties;
    textXs?: React.CSSProperties;
  }
}