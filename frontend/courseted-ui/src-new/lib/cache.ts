// Cache configuration for Next.js

export const cacheConfig = {
  // Revalidate static pages every hour
  revalidate: 3600,
  
  // Cache tags for ISR
  tags: {
    courses: 'courses',
    articles: 'articles',
    users: 'users',
  },
};

export const getCacheHeaders = (maxAge: number = 3600) => ({
  'Cache-Control': `public, s-maxage=${maxAge}, stale-while-revalidate=${maxAge * 2}`,
});

export const revalidateTag = async (tag: string) => {
  if (process.env.NODE_ENV === 'production') {
    // In production, use Next.js revalidateTag
    const { revalidateTag } = await import('next/cache');
    revalidateTag(tag);
  }
};
