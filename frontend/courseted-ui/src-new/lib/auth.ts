// Auth configuration and utilities

export interface AuthConfig {
  tokenKey: string;
  refreshTokenKey: string;
  apiBaseUrl: string;
}

export const authConfig: AuthConfig = {
  tokenKey: 'courseted_token',
  refreshTokenKey: 'courseted_refresh_token',
  apiBaseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
};

export const getToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(authConfig.tokenKey);
};

export const setToken = (token: string): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem(authConfig.tokenKey, token);
};

export const removeToken = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(authConfig.tokenKey);
  localStorage.removeItem(authConfig.refreshTokenKey);
};

export const isAuthenticated = (): boolean => {
  return !!getToken();
};
