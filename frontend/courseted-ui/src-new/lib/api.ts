import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { tokenUtils } from '@/utils/cookies';

interface ApiError {
  message: string;
  code?: string;
  status?: number;
}

declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: number;
    };
  }
}

const DEFAULT_TIMEOUT = 10000;
const isDevelopment = typeof window !== 'undefined' ?
  process.env.NODE_ENV === 'development' :
  false;

const getBaseURL = () => {
  if (typeof window === 'undefined') {
    // Server-side: use internal URL or default
    return process.env.API_URL || 'http://localhost:8000';
  }
  // Client-side: use public env var or default
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
};

const axiosInstance = axios.create({
  baseURL: getBaseURL(),
  timeout: DEFAULT_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  decompress: true,
  validateStatus: (status: number) => status >= 200 && status < 300,
});

axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = tokenUtils.getAccessToken();

    if (token?.trim()) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    config.metadata = { startTime: Date.now() };
    return config;
  },
  (error: AxiosError) => {
    if (isDevelopment) {
      console.error('Request interceptor error:', error);
    }
    return Promise.reject(error);
  }
);

const logRequest = (response: AxiosResponse) => {
  if (!isDevelopment) return;

  console.log('Response received:', response);
  if (response.config.metadata?.startTime) {
    const duration = Date.now() - response.config.metadata.startTime;
    console.log(`API Request to ${response.config.url} took ${duration}ms`);
  }
};

const logError = (status: number, data: any, message: string) => {
  if (!isDevelopment) return;

  switch (status) {
    case 403:
      console.warn('Access forbidden:', data);
      break;
    case 429:
      console.warn('Rate limit exceeded');
      break;
    case 500:
      console.error('Server error:', data);
      break;
    default:
      console.error(`HTTP Error ${status}:`, data?.message || message);
  }
};

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    logRequest(response);
    return response;
  },
  (error: AxiosError): Promise<ApiError> => {
    const { response, request, message } = error;

    if (response) {
      const { status, data }: { status: number; data: any } = response;
      logError(status, data, message);

      if (status === 401) {
        tokenUtils.clearTokens();
      }

      return Promise.reject({
        message: data?.message || `HTTP Error ${status}`,
        code: data?.code,
        status,
      } as ApiError);
    }

    if (request) {
      if (isDevelopment) {
        console.error('Network error:', message);
      }
      return Promise.reject({
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR',
      } as ApiError);
    }

    if (isDevelopment) {
      console.error('Request setup error:', message);
    }
    return Promise.reject({
      message: message || 'Request configuration error',
      code: 'REQUEST_SETUP_ERROR',
    } as ApiError);
  }
);

if (isDevelopment) {
  axiosInstance.interceptors.request.use(request => {
    console.log('Starting Request:', {
      method: request.method?.toUpperCase(),
      url: request.url,
      baseURL: request.baseURL,
    });
    return request;
  });
}

export default axiosInstance;
