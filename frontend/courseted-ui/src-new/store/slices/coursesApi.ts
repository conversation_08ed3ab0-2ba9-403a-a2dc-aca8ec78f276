import { baseApi } from '../../api/baseApi';
import type { Course, CreateCourseRequest, UpdateCourseRequest } from '../../types/courses.types';
import type { ApiResponse, PaginationParams } from '../../types/common.types';

export const coursesApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    // Get all courses
    getCourses: build.query<ApiResponse<Course[]>, PaginationParams>({
      query: (params) => ({
        url: '/v1/courses',
        method: 'GET',
        params,
      }),
      providesTags: ['Course'],
    }),

    // Get course by ID
    getCourse: build.query<ApiResponse<Course>, string>({
      query: (id) => ({
        url: `/v1/courses/${id}`,
        method: 'GET',
      }),
      providesTags: (_, __, id) => [{ type: 'Course', id }],
    }),

    // Create course
    createCourse: build.mutation<ApiResponse<Course>, CreateCourseRequest>({
      query: (data) => ({
        url: '/v1/courses',
        method: 'POST',
        data,
      }),
      invalidatesTags: ['Course'],
    }),

    // Update course
    updateCourse: build.mutation<ApiResponse<Course>, { id: string; data: UpdateCourseRequest }>({
      query: ({ id, data }) => ({
        url: `/v1/courses/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: (_, __, { id }) => [{ type: 'Course', id }, 'Course'],
    }),

    // Delete course
    deleteCourse: build.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/v1/courses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_, __, id) => [{ type: 'Course', id }, 'Course'],
    }),

    // Get featured courses
    getFeaturedCourses: build.query<ApiResponse<Course[]>, void>({
      query: () => ({
        url: '/v1/courses/featured',
        method: 'GET',
      }),
      providesTags: ['Course'],
    }),

    // Enroll in course
    enrollInCourse: build.mutation<ApiResponse<void>, string>({
      query: (courseId) => ({
        url: `/v1/courses/${courseId}/enroll`,
        method: 'POST',
      }),
      invalidatesTags: (_, __, courseId) => [{ type: 'Course', id: courseId }],
    }),
  }),
});

export const {
  useGetCoursesQuery,
  useGetCourseQuery,
  useCreateCourseMutation,
  useUpdateCourseMutation,
  useDeleteCourseMutation,
  useGetFeaturedCoursesQuery,
  useEnrollInCourseMutation,
} = coursesApi;
