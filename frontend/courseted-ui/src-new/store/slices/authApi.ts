import { baseApi } from '../../api/baseApi';
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshTokenRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest
} from '@/types/auth.types';
import type { ApiResponse } from '@/types/common.types';
import { tokenUtils } from '@/utils/cookies';

export const authApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    // Login
    login: build.mutation<ApiResponse<AuthResponse>, LoginRequest>({
      query: (credentials) => ({
        url: '/v1/auth/login',
        method: 'POST',
        data: credentials,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<AuthResponse>) => {
        console.log('Login response:', response.data?.accessToken);
        // Store tokens in cookies
        if (response.data?.accessToken) {
          tokenUtils.setTokens(response.data.accessToken, response.data.refreshToken);
        }
        return response;
      },
    }),

    // Register
    register: build.mutation<ApiResponse<AuthResponse>, RegisterRequest>({
      query: (userData) => ({
        url: '/v1/auth/register',
        method: 'POST',
        data: userData,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<AuthResponse>) => {
        // Store tokens in cookies
        if (response.data?.accessToken) {
          tokenUtils.setTokens(response.data.accessToken, response.data.refreshToken);
        }
        return response;
      },
    }),

    // Logout
    logout: build.mutation<ApiResponse<void>, void>({
      query: () => ({
        url: '/v1/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['Auth'],
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
        } finally {
          // Clear tokens regardless of API response
          tokenUtils.clearTokens();
          // Clear all cached data
          dispatch(baseApi.util.resetApiState());
        }
      },
    }),

    // Get Current User
    getCurrentUser: build.query<ApiResponse<any>, void>({
      query: () => ({
        url: '/v1/auth/context',
        method: 'GET',
      }),
      providesTags: ['Auth'],
      transformResponse: (response: any) => {
        console.log('Current user response:', response.data);
        return response.data;
      },
    }),

    // Refresh Token
    refreshToken: build.mutation<ApiResponse<{ token: string }>, RefreshTokenRequest>({
      query: (data) => ({
        url: '/v1/auth/refresh',
        method: 'POST',
        data,
      }),
      transformResponse: (response: ApiResponse<{ token: string }>) => {
        if (response.data?.token) {
          tokenUtils.setTokens(response.data.token);
        }
        return response;
      },
    }),

    // Forgot Password
    forgotPassword: build.mutation<ApiResponse<void>, ForgotPasswordRequest>({
      query: (data) => ({
        url: '/v1/auth/forgot-password',
        method: 'POST',
        data,
      }),
    }),

    // Reset Password
    resetPassword: build.mutation<ApiResponse<void>, ResetPasswordRequest>({
      query: (data) => ({
        url: '/v1/auth/reset-password',
        method: 'POST',
        data,
      }),
    }),

    // Verify Email
    verifyEmail: build.mutation<ApiResponse<void>, { token: string }>({
      query: (data) => ({
        url: '/v1/auth/verify-email',
        method: 'POST',
        data,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useRefreshTokenMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyEmailMutation,
} = authApi;
