import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Article } from '../../types/article.types';

interface ArticleState {
  articles: Article[];
  selectedArticle: Article | null;
  loading: boolean;
  error: string | null;
}

const initialState: ArticleState = {
  articles: [],
  selectedArticle: null,
  loading: false,
  error: null,
};

const articleSlice = createSlice({
  name: 'article',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setArticles: (state, action: PayloadAction<Article[]>) => {
      state.articles = action.payload;
    },
    setSelectedArticle: (state, action: PayloadAction<Article | null>) => {
      state.selectedArticle = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setLoading, setArticles, setSelectedArticle, setError } = articleSlice.actions;
export default articleSlice.reducer;
