export interface Course {
  id: string;
  title: string;
  description: string;
  slug: string;
  price: number;
  duration: number;
  level: 'beginner' | 'intermediate' | 'advanced';
  instructor: {
    id: string;
    name: string;
    avatar?: string;
  };
  thumbnail?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CourseFilters {
  level?: string;
  priceRange?: [number, number];
  tags?: string[];
  search?: string;
}
