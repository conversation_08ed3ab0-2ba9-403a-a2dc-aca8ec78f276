// Client-side cookie utilities for Next.js
export const cookieUtils = {
  get: (name: string): string | undefined => {
    if (typeof window === 'undefined') return undefined;

    const value = document.cookie
      .split('; ')
      .find(row => row.startsWith(`${name}=`))
      ?.split('=')[1];

    return value;
  },

  set: (name: string, value: string, days = 7) => {
    if (typeof window === 'undefined') return;

    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);

    document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/; SameSite=Lax${process.env.NODE_ENV === 'production' ? '; Secure' : ''
      }`;
  },

  delete: (name: string) => {
    if (typeof window === 'undefined') return;

    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  },

  getDecodedToken: (name: string) => {
    const token = cookieUtils.get(name);
    if (!token) return null;

    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }
};

// Token management utilities
export const tokenUtils = {
  getAccessToken: () => cookieUtils.get('accessToken'),
  getRefreshToken: () => cookieUtils.get('refreshToken'),
  getDecodedToken: () => cookieUtils.getDecodedToken('accessToken'),

  setTokens: (accessToken: string, refreshToken?: string) => {
    cookieUtils.set('accessToken', accessToken, 1); // 1 day for access token
    if (refreshToken) {
      cookieUtils.set('refreshToken', refreshToken, 7); // 7 days for refresh token
    }
  },

  clearTokens: () => {
    cookieUtils.delete('accessToken');
    cookieUtils.delete('refreshToken');
  },
};
