// Utility function to extract error messages
export const getErrorMessage = (apiError: any, fallbackError?: any, defaultMessage = 'An error occurred. Please try again.'): string => {
  // Check API error first
  if (apiError?.data?.message) return apiError.data.message;
  if (apiError?.data && typeof apiError.data === 'string') return apiError.data;
  if (apiError?.message) return apiError.message;

  // Check fallback error
  if (fallbackError) {
    if (typeof fallbackError === 'object' && 'data' in fallbackError && fallbackError.data) {
      const errorData = fallbackError.data as any;
      if (errorData?.message) return errorData.message;
    }
    if (typeof fallbackError === 'object' && 'message' in fallbackError) {
      return String(fallbackError.message);
    }
  }

  return defaultMessage;
};
