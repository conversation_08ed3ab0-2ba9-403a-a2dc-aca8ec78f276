import * as yup from 'yup';

// Reusable validation rules
export const validationRules = {
  email: yup
    .string()
    .required('Email is required')
    .email('Please enter a valid email address'),

  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters long')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    ),

  confirmPassword: (passwordField: string = 'password') => yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref(passwordField)], 'Passwords do not match'),

  phoneNumber: yup
    .string()
    .required('Phone number is required')
    .matches(/^\d{7,15}$/, 'Please enter a valid phone number (7-15 digits)'),

  country: yup
    .object()
    .nullable()
    .required('Please select a country')
    .test('is-country', 'Please select a valid country', (value) => {
      return value !== null && typeof value === 'object' && 'id' in value;
    }),
};

// Register form schema
export const registerSchema = yup.object({
  email: validationRules.email,
  password: validationRules.password,
  correctPassword: validationRules.confirmPassword('password'),
  phoneNumber: validationRules.phoneNumber,
  selectedCountry: validationRules.country,
});

export const loginSchema = yup.object({
  email: validationRules.email,
  // password: validationRules.password,
});

export type RegisterFormData = yup.InferType<typeof registerSchema>;
