import {
  useGetCoursesQuery,
  useGetCourseQuery,
  useCreateCourseMutation,
  useUpdateCourseMutation,
  useDeleteCourseMutation,
  useGetFeaturedCoursesQuery,
  useEnrollInCourseMutation
} from '@features/courses/coursesApi';
import type { CreateCourseRequest, UpdateCourseRequest, PaginationParams } from '@/types/courses.types';

export const useCourses = (params?: PaginationParams) => {
  const coursesQuery = useGetCoursesQuery(params || {});
  const featuredCoursesQuery = useGetFeaturedCoursesQuery();

  const [createCourseMutation, createResult] = useCreateCourseMutation();
  const [updateCourseMutation, updateResult] = useUpdateCourseMutation();
  const [deleteCourseMutation, deleteResult] = useDeleteCourseMutation();
  const [enrollMutation, enrollResult] = useEnrollInCourseMutation();

  const createCourse = async (data: CreateCourseRequest) => {
    return createCourseMutation(data).unwrap();
  };

  const updateCourse = async (id: string, data: UpdateCourseRequest) => {
    return updateCourseMutation({ id, data }).unwrap();
  };

  const deleteCourse = async (id: string) => {
    return deleteCourseMutation(id).unwrap();
  };

  const enrollInCourse = async (courseId: string) => {
    return enrollMutation(courseId).unwrap();
  };

  return {
    // Data
    courses: coursesQuery.data?.data || [],
    featuredCourses: featuredCoursesQuery.data?.data || [],

    // Loading states
    isLoading: coursesQuery.isLoading,
    isFeaturedLoading: featuredCoursesQuery.isLoading,
    isCreating: createResult.isLoading,
    isUpdating: updateResult.isLoading,
    isDeleting: deleteResult.isLoading,
    isEnrolling: enrollResult.isLoading,

    // Error states
    error: coursesQuery.error,
    featuredError: featuredCoursesQuery.error,
    createError: createResult.error,
    updateError: updateResult.error,
    deleteError: deleteResult.error,
    enrollError: enrollResult.error,

    // Actions
    createCourse,
    updateCourse,
    deleteCourse,
    enrollInCourse,

    // Refetch
    refetch: coursesQuery.refetch,
    refetchFeatured: featuredCoursesQuery.refetch,
  };
};

export const useCourse = (id: string) => {
  const courseQuery = useGetCourseQuery(id, { skip: !id });

  return {
    course: courseQuery.data?.data || null,
    isLoading: courseQuery.isLoading,
    error: courseQuery.error,
    refetch: courseQuery.refetch,
  };
};
