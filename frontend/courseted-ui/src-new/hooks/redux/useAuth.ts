import { useState, useEffect } from 'react';
import {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useRefreshTokenMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyEmailMutation
} from '../../features/auth/authApi';
import type { LoginRequest, RegisterRequest, RefreshTokenRequest, ForgotPasswordRequest, ResetPasswordRequest } from '../../types/auth.types';
import { tokenUtils } from '../../utils/cookies';

export const useAuth = () => {
  const [loginMutation, loginResult] = useLoginMutation();
  const [registerMutation, registerResult] = useRegisterMutation();
  const [logoutMutation, logoutResult] = useLogoutMutation();
  const [refreshTokenMutation] = useRefreshTokenMutation();
  const [forgotPasswordMutation] = useForgotPasswordMutation();
  const [resetPasswordMutation] = useResetPasswordMutation();
  const [verifyEmailMutation] = useVerifyEmailMutation();

  // Handle SSR/hydration safely - use state to track client-side token
  const [hasToken, setHasToken] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Check for token after component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);
    setHasToken(Boolean(tokenUtils.getAccessToken()));
  }, []);

  // Only fetch current user if token exists and we're on client-side
  const currentUserQuery: any = useGetCurrentUserQuery(undefined, {
    skip: !hasToken || !isClient,
  });
  const login = async (credentials: LoginRequest) => {
    const result = await loginMutation(credentials).unwrap();
    // Update hasToken state after successful login
    setHasToken(Boolean(tokenUtils.getAccessToken()));
    return result;
  };

  const register = async (userData: RegisterRequest) => {
    const result = await registerMutation(userData).unwrap();
    // Update hasToken state after successful registration
    setHasToken(Boolean(tokenUtils.getAccessToken()));
    return result;
  };

  const logout = async () => {
    const result = await logoutMutation().unwrap();
    // Update hasToken state after logout
    setHasToken(false);
    return result;
  };

  const refreshToken = async (refreshTokenData: RefreshTokenRequest) => {
    return refreshTokenMutation(refreshTokenData).unwrap();
  };

  const forgotPassword = async (data: ForgotPasswordRequest) => {
    return forgotPasswordMutation(data).unwrap();
  };

  const resetPassword = async (data: ResetPasswordRequest) => {
    return resetPasswordMutation(data).unwrap();
  };

  const verifyEmail = async (token: string) => {
    return verifyEmailMutation({ token }).unwrap();
  };

  return {
    // Actions
    login,
    register,
    logout,
    refreshToken,
    forgotPassword,
    resetPassword,
    verifyEmail,

    // State
    user: currentUserQuery.currentData?.user || null,
    context: currentUserQuery.currentData || null,
    isAuthenticated: isClient && hasToken && !currentUserQuery.isError,

    // Loading states
    isLoggingIn: loginResult.isLoading,
    isRegistering: registerResult.isLoading,
    isLoggingOut: logoutResult.isLoading,
    isLoadingUser: currentUserQuery.isLoading || !isClient, // Show loading until client-side check is complete

    // Error states
    loginError: loginResult.error as any,
    registerError: registerResult.error,
    logoutError: logoutResult.error,
    userError: currentUserQuery.error,

    // Refetch user data
    refetchUser: currentUserQuery.refetch,
  };
};
