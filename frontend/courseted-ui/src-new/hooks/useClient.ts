import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if the component has mounted on the client side
 * This helps prevent hydration mismatches between server and client rendering
 *
 * @returns boolean indicating if the component has mounted on the client
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Custom hook that returns true only after the component has hydrated on the client
 * Useful for components that need to render differently on server vs client
 *
 * @returns boolean indicating if hydration is complete
 */
export function useHydrated(): boolean {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  return hydrated;
}
