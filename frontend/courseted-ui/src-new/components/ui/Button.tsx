import theme from '@/theme';
import { Button as BaseButton, CircularProgress, SxProps, Theme, useMediaQuery } from '@mui/material';
import React, { useMemo } from 'react';
import { CustomButtonProps } from '@/types/ui/button.types';

const grey = theme.palette.grey;

const baseStyles = {
  borderRadius: '100px',
  fontWeight: 500,
  transition: 'all 0.2s ease',
  textTransform: 'none',
};

const variantStyles = {
  primary: {
    backgroundColor: grey[900],
    color: 'white',
    border: 'none',
    '&:hover': { backgroundColor: grey[800] },
    '&:disabled': { backgroundColor: grey[300], color: grey[500] },
  },
  outlined: {
    backgroundColor: 'white',
    color: grey[900],
    border: `1px solid ${grey[300]}`,
    '&:hover': { backgroundColor: grey[100], color: grey[800] },
    '&:disabled': { backgroundColor: 'white', color: grey[500] },
  },
  text: {
    backgroundColor: 'transparent',
    color: grey[900],
    border: 'none',
    '&:hover': { backgroundColor: grey[100], color: grey[800] },
    '&:disabled': { color: grey[500] },
  },
};

const sizeStyles = {
  xl: {
    fontSize: '16px',
    padding: '16px 24px',
    height: '56px',
    '@media (max-width:600px)': {
      fontSize: '14px',
      padding: '12px 20px',
      height: '48px',
    },
  },
  lg: {
    fontSize: '14px',
    padding: '12px 16px',
    height: '44px',
    '@media (max-width:600px)': {
      fontSize: '13px',
      padding: '10px 14px',
      height: '40px',
    },
  },
  md: {
    fontSize: '14px',
    padding: '10px 12px',
    height: '40px',
    '@media (max-width:600px)': {
      fontSize: '13px',
      padding: '8px 12px',
      height: '36px',
    },
  },
  sm: {
    fontSize: '14px',
    padding: '6px 12px',
    height: '32px',
    '@media (max-width:600px)': {
      fontSize: '12px',
      padding: '6px 10px',
      height: '30px',
    },
  },
};

const Button: React.FC<CustomButtonProps> = ({
  leftIcon,
  rightIcon,
  loading = false,
  children,
  disabled = false,
  variant = 'primary',
  size = 'lg',
  responsive = true,
  fullWidthOnMobile = false,
  sx,
  ...props
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const componentStyles = useMemo(
    () => ({
      ...baseStyles,
      ...variantStyles[variant],
      ...sizeStyles[size],
      ...(responsive && fullWidthOnMobile && isMobile && { width: '100%' }),
    }),
    [variant, size, responsive, fullWidthOnMobile, isMobile]
  );

  return (
    <BaseButton
      disabled={disabled || loading}
      startIcon={!loading && leftIcon}
      endIcon={!loading && rightIcon}
      sx={{ ...componentStyles, ...sx } as SxProps<Theme>}
      {...props}
    >
      {loading ? <CircularProgress size={20} color="inherit" /> : children}
    </BaseButton>
  );
};

export default Button;
