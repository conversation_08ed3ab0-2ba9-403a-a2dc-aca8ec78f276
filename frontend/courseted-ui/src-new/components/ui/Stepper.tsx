import React from 'react';
import {
  Stepper as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  StepConnector,
  stepConnectorClasses,
  StepIconProps,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Check } from '@mui/icons-material';
import theme from '@/theme';
import { StepperProps } from '@/types/ui/stepper.types';

// Custom Connector
const CustomConnector = styled(StepConnector)(({ theme }) => ({
  [`& .${stepConnectorClasses.line}`]: {
    marginLeft: 8,
  },
  
  [`& .${stepConnectorClasses.active}`]: {
    borderColor: '#e0e0e0',
    marginLeft: 8,
  },
  [`& .${stepConnectorClasses.completed}`]: {
    borderColor: theme.palette.primary[600],
    marginLeft: 8,
  },
  [`& .${stepConnectorClasses.disabled}`]: {
    borderColor: '#e0e0e0',
    marginLeft: 8,
  },
}));

const StepIconRoot = styled('div')<{
  ownerState: { active: boolean; completed: boolean };
}>(({ theme, ownerState }) => ({
  backgroundColor: theme.palette.grey[300],
  zIndex: 1,
  color: theme.palette.grey[900],
  width: 40,
  height: 40,
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  ...(ownerState.active && {
    backgroundColor: theme.palette.grey[900],
    color: theme.palette.grey[100],
  }),
  ...(ownerState.completed && {
    backgroundColor: theme.palette.primary[600],
    color: theme.palette.grey[100],
  }),
}));

function CustomStepIcon(props: StepIconProps) {
  const { active, completed } = props;

  return (
    <StepIconRoot ownerState={{ completed, active }}>
      {completed ? <Check /> : props.icon}
    </StepIconRoot>
  );
}

const Stepper: React.FC<StepperProps> = ({
  activeStep,
  steps,
  orientation = 'vertical',
  showLastStepLabel = true,
  lastStepLabelText = 'Last step',
}) => {
  return (
    <MuiStepper activeStep={activeStep} orientation={orientation} connector={<CustomConnector />}>
      {steps.map((step, index) => (
        <Step key={step.label}>
          <StepLabel
            optional={
              showLastStepLabel && index === steps.length - 1 ? (
                <Typography variant="caption">{lastStepLabelText}</Typography>
              ) : null
            }
            StepIconComponent={CustomStepIcon}
            sx={{ 
              fontSize: '16px',
              '& .MuiStepLabel-label': {
                fontSize: '16px',
                color: theme.palette.grey[900],
              },
            }}
          >
            {step.label}
          </StepLabel>
          <StepContent sx={{ marginLeft: '20px' }}>
            <Typography
              variant="body2"
              sx={{
                color: theme.palette.grey[600],
                fontSize: '14px',
                lineHeight: 1.5,
                marginLeft: 2,
              }}
            >
              {step.description}
            </Typography>
          </StepContent>
        </Step>
      ))}
    </MuiStepper>
  );
};

export default Stepper;
