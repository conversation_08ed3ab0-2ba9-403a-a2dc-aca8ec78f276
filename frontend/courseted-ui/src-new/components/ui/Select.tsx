import React, { useId } from 'react';
import {
    FormControl,
    InputLabel,
    Select as MuiSelect,
    MenuItem,
    FormHelperText,
    InputAdornment,
    useTheme,
    useMediaQuery,
} from '@mui/material';
import { CustomSelectProps, SelectOption } from '@/types/ui/select.types';

const Select = ({
    label,
    helperText,
    leftIcon,
    rightIcon,
    width,
    height,
    options,
    ...props
}: CustomSelectProps) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const generatedId = useId();
    const selectId = `select-input-${generatedId}`;
    const labelId = `${selectId}-label`;

    const disabled = false, error = false, required = false

    return (
        <FormControl
            fullWidth
            variant="standard"
            required={required}
            disabled={disabled}
            error={error}
            sx={{
                width: width || '100%',
                '& label': {
                    color: 'grey.900 !important',
                },
            }}
        >
            <InputLabel
                htmlFor={selectId}
                id={labelId}
                shrink
                margin="dense"
                sx={{
                    color: 'grey.900',
                    fontSize: isMobile ? '16px' : '18px',
                    lineHeight: '20px',
                    fontWeight: 500,
                    '&.Mui-disabled': {
                        color: 'grey.900',
                    },
                    '&.Mui-focused': {
                        color: 'grey.900',
                    },
                    '&.Mui-error': {
                        color: 'red.500',
                    },
                }}
            >
                {label}
            </InputLabel>
            <MuiSelect
                variant='outlined'
                labelId={labelId}
                id={selectId}
                disableUnderline
                displayEmpty
                startAdornment={
                    leftIcon && (
                        <InputAdornment
                            position="start"
                            sx={{
                                height: '100%',
                                alignItems: 'center',
                                display: 'flex',
                                pl: 1.5,
                            }}
                        >
                            {leftIcon}
                        </InputAdornment>
                    )
                }
                endAdornment={
                    rightIcon && (
                        <InputAdornment
                            position="end"
                            sx={{
                                height: '100%',
                                alignItems: 'center',
                                display: 'flex',
                                pr: 1.5,
                            }}
                        >
                            {rightIcon}
                        </InputAdornment>
                    )
                }
                sx={{
                    border: '1px solid',
                    borderColor: disabled ? 'grey.100' : 'grey.300',
                    borderRadius: isMobile ? '8px' : '10px',
                    height: height || (isMobile ? '40px' : '44px'),
                    pl: leftIcon ? 0 : 0,
                    pr: rightIcon ? 0 : 0,
                    backgroundColor: disabled ? 'grey.100' : 'inherit',
                    '&:hover': {
                        borderColor: disabled ? 'grey.100' : 'grey.300',
                    },
                    '&.Mui-focused': {
                        borderColor: error ? 'red.500' : 'green.600',
                        borderWidth: '2px',
                    },
                    '.MuiSelect-select': {
                        display: 'flex',
                        alignItems: 'center',
                        padding:
                            leftIcon && rightIcon
                                ? '8px 0 8px 0'
                                : leftIcon
                                    ? '8px 12px 8px 0'
                                    : rightIcon
                                        ? '8px 0 8px 12px'
                                        : '8px 12px',
                        fontSize: isMobile ? '16px' : '14px', // 16px prevents zoom on iOS
                        lineHeight: '20px',
                        fontWeight: 400,
                        color: disabled ? 'grey.500' : 'grey.900',
                    },
                }}
                {...props}
            >
                {options.map((option: SelectOption) => (
                    <MenuItem key={option.value} value={option.value}>
                        {option.label}
                    </MenuItem>
                ))}
            </MuiSelect>
            {helperText && (
                <FormHelperText
                    sx={{
                        color: error ? 'red.500' : 'grey.600',
                        fontSize: '12px',
                        fontWeight: 400,
                        marginLeft: 0,
                    }}
                >
                    {helperText}
                </FormHelperText>
            )}
        </FormControl>
    );
};

export default Select;
