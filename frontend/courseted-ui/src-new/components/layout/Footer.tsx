"use client";
import Icon from '@/components/ui/Icon';
import { palette } from '@/theme/palette';
import { Divider, Grid, IconButton, Link, Stack, Typography } from '@mui/material';
import SectionLayout from '@/components/landing/components/SectionLayout';

const Footer = () => {
  const footerLinks = [
    { title: 'Academy', href: '#' },
    { title: 'Services', href: '#' },
    { title: 'Webinars', href: '#' },
    { title: 'Articles', href: '#' },
    { title: 'Contact us', href: '#' },
  ];

  const linkColumns = [footerLinks, footerLinks, footerLinks.slice(0, 3)];

  const socialIcons = [
    { name: 'Facebook', href: '#' },
    { name: 'TwitterX', href: '#' },
    { name: 'Instagram', href: '#' },
    { name: 'LinkedIn', href: '#' },
    { name: 'Youtube', href: '#' },
  ];

  return (
    <SectionLayout
      maxWidth="xl"
      backgroundColor={palette.grey[900]}
      py={{ xs: 4, sm: 6, md: 8, lg: 10 }}
      px={{ xs: 2, sm: 3, md: 4 }}
      color={palette.grey[400]}
    >
      <Grid container spacing={{ xs: 3, sm: 4, md: 5 }} sx={{ mb: { xs: 3, sm: 4, md: 6, lg: 8 } }}>
        <Grid item xs={12} sm={12} md={5} lg={4}>
          <Stack spacing={{ xs: 1.5, sm: 2 }} alignItems={{ xs: 'center', sm: 'center', md: 'flex-start' }}>
            <Icon
              name="LogoWhite"
              height="40px"
              width="196px"
              sx={{
                height: { xs: '32px', sm: '36px', md: '40px' },
                width: { xs: '157px', sm: '176px', md: '196px' }
              }}
            />
            <Typography
              variant="textMd"
              sx={{
                fontWeight: 'light',
                color: 'gray.500',
                textAlign: { xs: 'center', sm: 'center', md: 'left' },
                fontSize: { xs: '0.875rem', sm: '1rem' },
                lineHeight: { xs: 1.4, sm: 1.5 },
                maxWidth: { xs: '280px', sm: '320px', md: 'none' }
              }}
            >
              House 123, Street name, City name, Zip code (1100)
            </Typography>
          </Stack>
        </Grid>

        {linkColumns.map((column, index) => (
          <Grid item xs={6} sm={4} md={2} lg={2} key={`footer-col-${index}`}>
            <Stack spacing={{ xs: 1, sm: 1.5 }} alignItems={{ xs: 'center', sm: 'center', md: 'flex-start' }}>
              <Typography
                variant="textXl"
                color="white"
                sx={{
                  mb: { xs: 0.5, sm: 1 },
                  fontWeight: 'bold',
                  fontSize: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
                  textAlign: { xs: 'center', sm: 'center', md: 'left' }
                }}
              >
                About us
              </Typography>
              {column.map(link => (
                <Link
                  key={link.title}
                  href={link.href}
                  variant="textMd"
                  color="grey.400"
                  underline="hover"
                  sx={{
                    '&:hover': { color: 'white' },
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    textAlign: { xs: 'center', sm: 'center', md: 'left' },
                    transition: 'color 0.2s ease-in-out'
                  }}
                >
                  {link.title}
                </Link>
              ))}
            </Stack>
          </Grid>
        ))}

        <Grid item xs={12} sm={12} md={3} lg={2}>
          <Stack spacing={{ xs: 1, sm: 1.5 }} alignItems={{ xs: 'center', sm: 'center', md: 'flex-start' }}>
            <Typography
              variant="textXl"
              color="white"
              sx={{
                mb: { xs: 0.5, sm: 1 },
                fontWeight: 'medium',
                fontSize: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
                textAlign: { xs: 'center', sm: 'center', md: 'left' }
              }}
            >
              Our socials
            </Typography>
            <Stack
              direction="row"
              spacing={{ xs: 0.5, sm: 1 }}
              justifyContent={{ xs: 'center', sm: 'center', md: 'flex-start' }}
              flexWrap="wrap"
              sx={{ gap: { xs: 0.5, sm: 1 } }}
            >
              {socialIcons.map(social => (
                <IconButton
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: 'grey.400',
                    '&:hover': {
                      color: 'white',
                      transform: 'translateY(-2px)',
                      transition: 'all 0.2s ease-in-out'
                    },
                    p: { xs: 1, sm: 1.5 }
                  }}
                >
                  <Icon
                    name={
                      social.name as 'Facebook' | 'TwitterX' | 'Instagram' | 'LinkedIn' | 'Youtube'
                    }
                    sx={{ fontSize: { xs: 18, sm: 20, md: 22 } }}
                  />
                </IconButton>
              ))}
            </Stack>
          </Stack>
        </Grid>
      </Grid>

      <Divider sx={{ borderColor: 'grey.700', mb: { xs: 2, sm: 3, md: 4 } }} />

      <Stack
        direction={{ xs: 'column', sm: 'column', md: 'row' }}
        justifyContent="space-between"
        alignItems="center"
        spacing={{ xs: 2, sm: 2, md: 0 }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: 1, sm: 3 }}
          alignItems="center"
        >
          <Link
            href="#"
            variant="body2"
            color="grey.400"
            underline="hover"
            sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' },
              '&:hover': { color: 'white' },
              transition: 'color 0.2s ease-in-out'
            }}
          >
            Terms & conditions
          </Link>
          <Link
            href="#"
            variant="textMd"
            color="grey.400"
            underline="hover"
            sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' },
              '&:hover': { color: 'white' },
              transition: 'color 0.2s ease-in-out'
            }}
          >
            Privacy policy
          </Link>
        </Stack>
        <Typography
          variant="textMd"
          sx={{
            color: 'grey.500',
            fontSize: { xs: '0.875rem', sm: '1rem' },
            textAlign: 'center',
            mt: { xs: 1, sm: 0 }
          }}
        >
          courseted © {new Date().getFullYear()}
        </Typography>
      </Stack>
    </SectionLayout>
  );
};

export default Footer;
