'use client';
import { useState, useEffect } from 'react';
import theme from '@/theme';
import Button from '@/components/ui/Button';
import Icon from '@/components/ui/Icon';
import { Menu, MenuItem, useMediaQuery, Avatar, Divider } from '@mui/material';
import { Box, Stack, ButtonBase, Badge, IconButton, Typography } from '@mui/material';
import { MouseEvent, memo } from 'react';
import { useAuth } from '@/hooks/redux/useAuth';
import SearchDialog from './SearchDialog';

const NAV_LINKS = [
  { label: 'Home', href: '/' },
  { label: 'Academy', href: '/academy' },
  { label: 'Meetings', href: '/meetings' },
  { label: 'Talent hub', href: '/talent-hub' },
  { label: 'Webinars', href: '/webinars' },
  { label: 'Articles', href: '/articles' },
  { label: 'Contact us', href: '/contact' },
];

const navLinkStyles = {
  fontWeight: 500,
  fontSize: { xs: 14, sm: 15, md: 16 },
  color: 'grey.900',
  borderRadius: 2,
  px: { xs: 1, sm: 1.5, md: 2 },
  py: { xs: 0.75, sm: 0.5 },
  textDecoration: 'none',
  minHeight: { xs: '44px', sm: 'auto' }, // Better touch targets on mobile
  display: 'flex',
  alignItems: 'center',
  '&:hover': {
    backgroundColor: theme.palette.grey[300],
  },
  '&:focus': {
    backgroundColor: theme.palette.grey[300],
    outline: `2px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px',
  },
};

const badgeStyles = {
  '& .MuiBadge-badge': {
    background: theme.palette.green[600],
    fontSize: { xs: 10, sm: 12 },
    minWidth: { xs: 16, sm: 18 },
    height: { xs: 16, sm: 18 },
    top: { xs: 2, sm: 4 },
    right: { xs: 2, sm: 4 },
  },
};

const Header = () => {
  const [searchOpen, setSearchOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);
  const [cartAnchorEl, setCartAnchorEl] = useState<null | HTMLElement>(null);
  const [mounted, setMounted] = useState(false);

  const { isAuthenticated, user, logout } = useAuth();

  const isMobile = useMediaQuery(theme.breakpoints.down('lg'), { noSsr: true });
  const isTablet = useMediaQuery(theme.breakpoints.down('md'), { noSsr: true });
  const menuOpen = Boolean(anchorEl);
  const profileMenuOpen = Boolean(profileMenuAnchor);
  const cartMenuOpen = Boolean(cartAnchorEl);

  // Ensure component is mounted before rendering auth-dependent content
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleMenuClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleProfileMenuClick = (event: MouseEvent<HTMLElement>) => {
    setProfileMenuAnchor(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setProfileMenuAnchor(null);
  };

  const handleCartMenuClick = (event: MouseEvent<HTMLElement>) => {
    setCartAnchorEl(event.currentTarget);
  };

  const handleCartMenuClose = () => {
    setCartAnchorEl(null);
  };

  const handleSearchOpen = () => setSearchOpen(true);
  const handleSearchClose = () => setSearchOpen(false);

  const handleLogout = async () => {
    try {
      await logout();
      handleProfileMenuClose();
      window.location.href = '/';
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) { /* empty */ }
  };

  const renderNavLinks = () => (
    <>
      {NAV_LINKS.map(link => (
        <ButtonBase key={link.label} href={link.href} sx={navLinkStyles} disableRipple>
          {link.label}
        </ButtonBase>
      ))}
    </>
  );

  const renderCartMenu = () => (
    <Menu
      anchorEl={cartAnchorEl}
      open={cartMenuOpen}
      onClose={handleCartMenuClose}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      PaperProps={{
        sx: {
          minWidth: 300,
          mt: 1,
          boxShadow: theme.shadows[3],
        },
      }}
    >
      <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${theme.palette.grey[200]}` }}>
        <Typography variant="subtitle2" fontWeight={600}>
          Your Cart
        </Typography>
      </Box>
      <MenuItem sx={{ py: 1.5 }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Icon name="Lesson" sx={{ fontSize: 24 }} />
          <Box>
            <Typography variant="body2" fontWeight={500}>
              Course Title
            </Typography>
            <Typography variant="caption" color="text.secondary">
              $99.99
            </Typography>
          </Box>
        </Stack>
      </MenuItem>
      <MenuItem sx={{ py: 1.5 }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Icon name="Lesson" sx={{ fontSize: 24 }} />
          <Box>
            <Typography variant="body2" fontWeight={500}>
              Another Course
            </Typography>
            <Typography variant="caption" color="text.secondary">
              $49.99
            </Typography>
          </Box>
        </Stack>
      </MenuItem>
      <Divider sx={{ my: 1 }} />
      <MenuItem sx={{ py: 1.5 }}>
        <Typography variant="body2" fontWeight={600}>
          Total: $149.98
        </Typography>
      </MenuItem>
      <MenuItem
        component="a"
        href="/checkout"
        sx={{
          py: 1.5,
          color: theme.palette.primary.main,
          '&:hover': {
            backgroundColor: theme.palette.primary.light + '20',
          },
        }}
      >
        Proceed to Checkout
      </MenuItem>
    </Menu>
  );

  const renderAuthenticatedActions = () => (
    <>
      <IconButton
        onClick={handleSearchOpen}
        aria-label="search"
        size={isTablet ? 'small' : 'medium'}
      >
        <Icon name="Search" />
      </IconButton>

      <Badge badgeContent={3} color="success" sx={badgeStyles}>
        <IconButton
          onClick={handleCartMenuClick}
          aria-label="cart"
          size={isTablet ? 'small' : 'medium'}
        >
          <Icon
            name="ShoppingCart"
            sx={{
              cursor: 'pointer',
              fontSize: { xs: 20, sm: 24 },
            }}
          />
        </IconButton>
      </Badge>
      {renderCartMenu()}

      <Box>
        <IconButton
          onClick={handleProfileMenuClick}
          aria-label="profile menu"
          size={isTablet ? 'small' : 'medium'}
        >
          <Avatar
            src={user?.profile?.profilePicture}
            alt={`${user?.profile?.firstName || ''} ${user?.profile?.lastName || ''}`}
            sx={{
              width: { xs: 32, sm: 36 },
              height: { xs: 32, sm: 36 },
              fontSize: { xs: 14, sm: 16 },
              bgcolor: theme.palette.primary.main,
            }}
          >
            {user?.profile?.firstName?.charAt(0)?.toUpperCase() || 'U'}
          </Avatar>
        </IconButton>

        <Menu
          anchorEl={profileMenuAnchor}
          open={profileMenuOpen}
          onClose={handleProfileMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            sx: {
              minWidth: 200,
              mt: 1,
              boxShadow: theme.shadows[3],
            },
          }}
        >
          <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${theme.palette.grey[200]}` }}>
            <Typography variant="subtitle2" fontWeight={600} noWrap>
              {`${user?.profile?.firstName || ''} ${user?.profile?.lastName || ''}`}
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              {user?.email}
            </Typography>
          </Box>

          <MenuItem
            component="a"
            href="/profile"
            onClick={handleProfileMenuClose}
            sx={{ py: 1.5 }}
          >
            <Icon name="Students" sx={{ mr: 1.5, fontSize: 18 }} />
            My Profile
          </MenuItem>

          <MenuItem
            component="a"
            href="/my-courses"
            onClick={handleProfileMenuClose}
            sx={{ py: 1.5 }}
          >
            <Icon name="Lesson" sx={{ mr: 1.5, fontSize: 18 }} />
            My Courses
          </MenuItem>

          <MenuItem
            component="a"
            href="/meetings"
            onClick={handleProfileMenuClose}
            sx={{ py: 1.5 }}
          >
            <Icon name="VideoCameraIconLanding" sx={{ mr: 1.5, fontSize: 18 }} />
            Meetings
          </MenuItem>

          <MenuItem
            component="a"
            href="/settings"
            onClick={handleProfileMenuClose}
            sx={{ py: 1.5 }}
          >
            <Icon name="Clock" sx={{ mr: 1.5, fontSize: 18 }} />
            Settings
          </MenuItem>

          <Divider sx={{ my: 1 }} />

          <MenuItem
            onClick={handleLogout}
            sx={{
              py: 1.5,
              color: theme.palette.error.main,
              '&:hover': {
                backgroundColor: theme.palette.error.light + '20',
              },
            }}
          >
            <Icon name="ArrowLeft" sx={{ mr: 1.5, fontSize: 18 }} />
            Logout
          </MenuItem>
        </Menu>
      </Box>
    </>
  );

  const renderUnauthenticatedActions = () => (
    <>
      <IconButton
        onClick={handleSearchOpen}
        aria-label="search"
        size={isTablet ? 'small' : 'medium'}
      >
        <Icon name="Search" />
      </IconButton>

      <Button
        href="/auth/login"
        variant="outlined"
        size={isTablet ? 'sm' : 'md'}
        sx={{
          fontSize: { xs: 12, sm: 14 },
          py: { xs: 0.5, sm: 1 },
          px: { xs: 2, sm: 3 },
          mr: 1,
        }}
      >
        Login
      </Button>

      <Button
        href="/auth/register"
        variant="primary"
        size={isTablet ? 'sm' : 'md'}
        sx={{
          fontSize: { xs: 12, sm: 14 },
          py: { xs: 0.5, sm: 1 },
        }}
      >
        Register
      </Button>
    </>
  );

  return (
    <>
      <Box
        component="header"
        sx={{
          width: '100%',
          background: theme.palette.grey[200],
          py: { xs: 1.5, sm: 2, md: 2.5 },
          display: 'flex',
          justifyContent: 'center',
          px: { xs: 2, sm: 4, md: 6, lg: 20, xl: 20 },
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            width: '100%',
            maxWidth: { xs: '100%', sm: '100%', md: '1200px', lg: '1400px' },
          }}
        >
          <Icon
            name="LogoBlack"
            width={isMobile ? 140 : isTablet ? 156 : 172}
            height={isMobile ? 23 : isTablet ? 25 : 28}
          />

          {isMobile ? (
            <Box>
              <IconButton
                onClick={handleMenuClick}
                aria-label="menu"
                size={isTablet ? 'small' : 'medium'}
                sx={{
                  minWidth: '44px',
                  minHeight: '44px',
                  p: { xs: 1.5, sm: 1 },
                }}
              >
                <Icon name="Menu" sx={{ fontSize: { xs: 24, sm: 20 } }} />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={menuOpen}
                onClose={handleMenuClose}
                onClick={handleMenuClose}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                PaperProps={{
                  sx: {
                    minWidth: { xs: 200, sm: 220 },
                    mt: 1,
                  },
                }}
              >
                {NAV_LINKS.map(link => (
                  <MenuItem
                    component="a"
                    href={link.href}
                    key={link.label}
                    sx={{
                      fontSize: { xs: 14, sm: 15 },
                      py: { xs: 1, sm: 1.5 },
                    }}
                  >
                    {link.label}
                  </MenuItem>
                ))}

                {mounted && !isAuthenticated && (
                  <>
                    <Divider sx={{ my: 1 }} />
                    <MenuItem
                      component="a"
                      href="/login"
                      sx={{
                        fontSize: { xs: 14, sm: 15 },
                        py: { xs: 1, sm: 1.5 },
                      }}
                    >
                      Login
                    </MenuItem>
                    <MenuItem
                      component="a"
                      href="/register"
                      sx={{
                        fontSize: { xs: 14, sm: 15 },
                        py: { xs: 1, sm: 1.5 },
                      }}
                    >
                      Register
                    </MenuItem>
                  </>
                )}
              </Menu>
            </Box>
          ) : (
            <Stack
              component="nav"
              direction="row"
              spacing={{ md: 1.5, lg: 2 }}
              alignItems="center"
            >
              {renderNavLinks()}
            </Stack>
          )}

          <Stack
            direction="row"
            alignItems="center"
            spacing={{ xs: 1, sm: 2, md: 3 }}
          >
            {mounted ? (
              isAuthenticated ? renderAuthenticatedActions() : renderUnauthenticatedActions()
            ) : (
              // Render a minimal placeholder to prevent layout shift during hydration
              <IconButton aria-label="search" size={isTablet ? 'small' : 'medium'}>
                <Icon name="Search" />
              </IconButton>
            )}
          </Stack>
        </Stack>
      </Box>
      <SearchDialog open={searchOpen} onClose={handleSearchClose} />
    </>
  );
};

export default memo(Header);
