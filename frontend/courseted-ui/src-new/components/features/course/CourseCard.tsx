import Icon from '@/components/ui/Icon';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Avatar,
    Stack,
    CardMedia,
} from '@mui/material';
import { CourseCardProps } from '@/types/section.types';

export default function CourseCard({ course, sectionVisible, cardStyle }: CourseCardProps) {

    if (!course) {
        console.error('CourseCard: course prop is undefined');
        return null;
    }

    return (
        <Card
            elevation={0}
            sx={{
                borderRadius: '16px',
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                backgroundColor: cardStyle?.cardBackground || 'white',
            }}
        >
            <CardMedia
                sx={{ borderRadius: 6, p: 2 }}
                component="img"
                image={course?.image}
                alt={course?.title}
            />
            <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', pt: 0 }} >
                {sectionVisible?.rating && (
                    <Box
                        sx={{
                            position: 'absolute',
                            top: 25,
                            right: 25,
                            backgroundColor: 'white',
                            p: 1,
                            borderRadius: '100px',
                            px: 2,
                            display: 'flex',
                            alignItems: 'center',
                        }}
                    >
                        <Typography mr={0.5}>
                            {course?.rating}
                        </Typography>
                        <Icon name="RatingStar" sx={{ ml: 0.5 }} />
                    </Box>
                )}

                <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Box
                        sx={{
                            backgroundColor: '#DCF7E7',
                            p: 1,
                            borderRadius: '100px',
                            px: 2,
                            display: 'flex',
                            alignItems: 'center',

                        }}
                    >
                        <Typography
                            variant="textSm"
                            color="#0F9D46"
                            sx={{ fontWeight: 'fontWeightMedium', fontSize: '14px !important' }}>
                            {course?.category}
                        </Typography>
                    </Box>
                    <Typography variant="textSm">{course?.time}</Typography>
                </Stack>

                <Typography variant="header3xs" sx={{ fontWeight: 'fontWeightMedium' }} mt={2}>
                    {course?.title}
                </Typography>

                {sectionVisible?.description && (
                    <Typography
                        // variant="textMd"
                        color="grey.700"
                        sx={{ fontWeight: 'fontWeightLight' }}
                        mt={1}
                    >
                        {course?.description}
                    </Typography>
                )}

                {sectionVisible?.lessons && sectionVisible?.duration && sectionVisible?.students && (
                    <Stack
                        direction="row"
                        spacing={2}
                        alignItems="center"
                        sx={{
                            mt: 2,
                            height: '68px',
                            border: '1px solid #F2F2F7',
                            borderLeft: 0,
                            borderRight: 0,
                        }}
                    >
                        <Stack direction="row" spacing={1}>
                            <Icon name="Lesson" />
                            <Typography variant="textSm" sx={{ fontWeight: 'fontWeightLight' }}>
                                {course?.lessons} lessons
                            </Typography>
                        </Stack>
                        <Stack direction="row" spacing={1}>
                            <Icon name="Clock" />
                            <Typography sx={{ fontWeight: 'fontWeightLight' }}>
                                {course?.duration}
                            </Typography>
                        </Stack>

                        <Stack direction="row" spacing={1}>
                            <Icon name="Students" />
                            <Typography variant="textSm" sx={{ fontWeight: 'fontWeightLight' }}>
                                {course?.students} students
                            </Typography>
                        </Stack>
                    </Stack>
                )}

                <Stack direction="row" justifyContent="space-between" alignItems="center" mt={2}>
                    <Stack direction="row" spacing={1} alignItems="center">
                        <Avatar sx={{ width: 40, height: 40 }} src={course?.avatar} />
                        <Typography variant="textLg" sx={{ fontWeight: 'fontWeightMedium' }}>
                            {course?.instructor}
                        </Typography>
                    </Stack>
                    {sectionVisible?.price && (
                        <Typography
                            variant="header3xs"
                            sx={{ color: 'primary.600', fontWeight: 'fontWeightMedium' }}
                        >
                            {course?.price}
                        </Typography>
                    )}
                </Stack>
            </CardContent>
        </Card>
    );
};
