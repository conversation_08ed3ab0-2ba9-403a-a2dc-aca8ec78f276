interface Course {
  id: string;
  title: string;
  description: string;
  // Add other course properties as needed
}

interface CourseDetailsProps {
  course: Course;
}

export default function CourseDetails({ course }: CourseDetailsProps) {
  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold mb-4">{course.title}</h1>
      <p className="text-lg text-gray-600 mb-8">{course.description}</p>
      {/* Add more course details here */}
    </div>
  );
}
