'use client';

import { useFormik } from 'formik';
import * as yup from 'yup';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const contactSchema = yup.object({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  subject: yup.string().required('Subject is required'),
  message: yup.string().min(10, 'Message must be at least 10 characters').required('Message is required'),
});

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

interface ContactFormProps {
  onSubmit: (values: ContactFormData) => void;
  loading?: boolean;
}

export default function ContactForm({ onSubmit, loading = false }: ContactFormProps) {
  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
    validationSchema: contactSchema,
    onSubmit,
  });

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-4">
      <Input
        label="Name"
        name="name"
        value={formik.values.name}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.name && formik.errors.name}
        required
      />
      
      <Input
        label="Email"
        type="email"
        name="email"
        value={formik.values.email}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.email && formik.errors.email}
        required
      />
      
      <Input
        label="Subject"
        name="subject"
        value={formik.values.subject}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.subject && formik.errors.subject}
        required
      />
      
      <Input
        label="Message"
        name="message"
        multiline
        rows={4}
        value={formik.values.message}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.message && formik.errors.message}
        required
      />
      
      <Button
        type="submit"
        variant="contained"
        fullWidth
        loading={loading}
        disabled={!formik.isValid || loading}
      >
        Send Message
      </Button>
    </form>
  );
}
