{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@app/*": ["./src/app/*"], "@app": ["./src/app"], "@theme/*": ["./src/theme/*"], "@theme": ["./src/theme"], "@routes/*": ["./src/routes/*"], "@routes": ["./src/routes"], "@components/*": ["./src/components/*"], "@components": ["./src/components"], "@features/*": ["./src/features/*"], "@features": ["./src/features"], "@api/*": ["./src/api/*"], "@api": ["./src/api"], "@utils/*": ["./src/utils/*"], "@utils": ["./src/utils"], "@hooks/*": ["./src/hooks/*"], "@hooks": ["./src/hooks"], "@pages/*": ["./src/pages/*"], "@pages": ["./src/pages"], "@assets/*": ["./src/assets/*"], "@assets": ["./src/assets"], "@types/*": ["./src/types/*"], "@types": ["./src/types"]}, "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}