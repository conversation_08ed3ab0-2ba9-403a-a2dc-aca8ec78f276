# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
.next
out
build
dist

# Development files
.git
.gitignore
README.md
*.md
.env.local
.env.development
.env.production

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode
.idea

# Test files
__tests__
*.test.js
*.test.ts
*.spec.js
*.spec.ts
coverage

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github
.gitlab-ci.yml

# Logs
logs
*.log

# Terraform
*.tf
*.tfstate*
tfplan

# Other
*.tgz
*.tar.gz
