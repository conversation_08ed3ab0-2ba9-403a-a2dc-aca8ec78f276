import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  LinearProgress,
  Grid2,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { palette } from '@/theme/palette';
import Icon from '@/components/ui/Icon';
import Stepper from '@/components/ui/Stepper';
import { ProfileSetupLayoutProps } from '@/types/pages/profile.types';

const StyledContainer = styled(Container)(({ theme }) => ({
  // minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f8f9fa',
  padding: theme.spacing(4),
  height: '100vh',
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  // padding: theme.spacing(12, 45),
  borderRadius: theme.spacing(2),
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  // width: '100%',
  // maxWidth: 1200,
  display: 'flex',
  // flexDirection: 'column',
  // gap: theme.spacing(4),
  alignItems: 'center',
  height: '100%',
}));

const LogoSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  // justifyContent: 'center',
  marginBottom: theme.spacing(3),
}));

const StepperSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  width: '100%',
}));

const ContentSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(4),
  alignItems: 'flex-start',
}));

const ImageSection = styled(Box)(({ theme }) => ({
  flex: '0 0 200px',
  display: 'flex',
  // justifyContent: 'center',
  alignItems: 'flex-start',
  [theme.breakpoints.down('md')]: {
    display: 'none',
  },
  marginTop: theme.spacing(6),
}));

const FormSection = styled(Box)({
  flex: 1,
});



// const OnboardingPage = () => {
//   return (
//     <Box
//       sx={{
//         minHeight: '100vh',
//         backgroundColor: '#f5f7fb',
//         p: { xs: 2, md: 4 },
//       }}
//     >
//       <Grid container spacing={4} alignItems='center'>
//         {/* Left Panel */}
//         <Grid item xs={12} md={4}>
//           <Box
//             sx={{
//               maxWidth: 400,
//               mx: 'auto',
//               textAlign: { xs: 'center', md: 'left' },
//             }}
//           >
//             <Typography variant='h5' fontWeight={700} color='primary' mb={1}>
//               <Box component='span' color='success.main'>
//                 C
//               </Box>
//               ourseted
//             </Typography>
//             <Typography variant='h4' fontWeight={700} gutterBottom>
//               Setup your profile
//             </Typography>
//             <Typography variant='body1' color='text.secondary' mb={1}>
//               Fill out all the information to setup your profile. Once finished,
//               you can start learning automation.
//             </Typography>
//             <Box
//               component='img'
//               src='/images/onboarding-image.jpg'
//               alt='Video call'
//               sx={{ width: '100%', borderRadius: 2, mt: 3, mb: 2 }}
//             />
//           </Box>
//         </Grid>

//         {/* Right Panel */}
//         <Grid item xs={12} md={8}>
//           <Box
//             sx={{
//               // maxWidth: 480,
//               mx: 'auto',
//               p: 4,
//               border: '1px solid #e0e0e0',
//               borderRadius: 2,
//               backgroundColor: '#fff',
//               boxShadow: 1,
//             }}
//           >
//             {/* Top header */}
//             <Box
//               display='flex'
//               justifyContent='space-between'
//               alignItems='center'
//               mb={2}
//             >
//               <Typography variant='caption' color='success.main'>
//                 Step 1/3
//               </Typography>
//               <Typography variant='caption' sx={{ cursor: 'pointer' }}>
//                 Skip
//               </Typography>
//             </Box>

//             <Typography variant='h6' fontWeight={600} mb={3}>
//               Student Information
//             </Typography>

//             {/* Upload Box */}
//             <Box
//               display='flex'
//               flexDirection='column'
//               alignItems='center'
//               justifyContent='center'
//               border='2px dashed #ccc'
//               borderRadius={2}
//               height={120}
//               mb={3}
//               sx={{ cursor: 'pointer' }}
//             >
//               <IconButton></IconButton>
//               <Typography variant='body2'>Upload profile photo</Typography>
//             </Box>

//             {/* Form Fields */}
//             <Grid container spacing={2}>
//               <Grid item xs={12} sm={6}>
//                 {/* <TextField label='First name' fullWidth required /> */}
//                 <Input
//                   label="First name"
//                   placeholder="Enter first name"
//                   fullWidth
//                   id="firstName"
//                   name="firstName"
//                   type="text"
//                   // value={formData.email}
//                   // onChange={(e) => handleInputChange('email', e.target.value)}
//                   // error={!!errors.email}
//                   // helperText={errors.email}
//                   // disabled={isLoggingIn}
//                 />
//               </Grid>
//               <Grid item xs={12} sm={6}>
//                 <TextField label='Last name' fullWidth required />
//               </Grid>
//               <Grid item xs={12}>
//                 <TextField label='Phone number' fullWidth required />
//               </Grid>
//               <Grid item xs={12}>
//                 <FormControl fullWidth required>
//                   <InputLabel>Country</InputLabel>
//                   <Select defaultValue=''>
//                     <MenuItem value='bd'>Bangladesh</MenuItem>
//                     <MenuItem value='us'>USA</MenuItem>
//                     <MenuItem value='uk'>UK</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//             </Grid>

//             {/* Next Button */}
//             <Box mt={4} display='flex' justifyContent='flex-end'>
//               <Button
//                 variant='contained'
//                 sx={{ borderRadius: 10, px: 4 }}
//                 endIcon={<span>›</span>}
//               >
//                 Next
//               </Button>
//             </Box>
//           </Box>
//         </Grid>
//       </Grid>
//     </Box>
//   );
// };



export const ProfileSetupLayout: React.FC<ProfileSetupLayoutProps> = ({
  children,
  currentStep,
  totalSteps,
  stepLabels,
  title,
  // subtitle,
}) => {
  const progress = ((currentStep + 1) / totalSteps) * 100;


  return (
    <StyledContainer maxWidth={false}>
      {/* <Stack display="flex" alignItems="center" justifyContent="center"> */}

      <Grid2
        container
        spacing={8}
        alignItems="center"
        justifyContent="space-between"
        width={'100%'}
        height={'100%'}
      >
        <Grid xs={12} md={6} flexGrow={1}>
          <Box sx={{ display: 'flex', flexDirection: 'column', maxWidth: '464px', margin: 'auto' }}>

            <LogoSection sx={{ mb: 4 }}>
              <Icon name="LogoBlack" width={172} height={28} />
            </LogoSection>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                fontSize: '32px',
                display: 'flex',
                alignItems: 'center',
                mb: 2,
                // justifyContent: 'center',
              }}
            >
              Setup your profile
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#666',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                mb: 2,
                // justifyContent: 'center',
              }}
            >
              Fill out all the information to setup your profile. Once finished, you can start learning automation.
            </Typography>

            <ImageSection>
              <Stepper
                activeStep={currentStep}
                steps={stepLabels}
                orientation="vertical"
                showLastStepLabel={true}
                lastStepLabelText="Last step"
              />
            </ImageSection>
          </Box>
        </Grid>

        <Grid xs={12} md={6} flexGrow={3} height={'100%'}>
          <StyledPaper>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                maxWidth: 480,
                margin: 'auto',
                // py: 10,
                // px: 4
              }}
            >
              <StepperSection>
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: palette.primary[600],
                        fontSize: '14px',
                        mb: 1,
                      }}
                    >
                      Step {currentStep + 1}/{totalSteps}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: palette.grey[400],
                        fontSize: '14px',
                        mb: 1,
                        cursor: 'pointer',
                      }}
                    >
                      skip
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={progress}
                    sx={{
                      height: 4,
                      borderRadius: 2,
                      backgroundColor: '#e0e0e0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: palette.primary[600],
                        borderRadius: 2,
                      },
                    }}
                  />
                </Box>

                {/* <Stepper activeStep={currentStep} alternativeLabel>
                  {stepLabels.map((label, index) => (
                    <Step key={label}>
                      <StepLabel
                        sx={{
                          '& .MuiStepLabel-label': {
                            fontSize: '12px',
                            color: index <= currentStep ? palette.primary[600] : '#999',
                          },
                          '& .MuiStepIcon-root': {
                            color: index <= currentStep ? palette.primary[600] : '#e0e0e0',
                          },
                        }}
                      >
                        {label}
                      </StepLabel>
                    </Step>
                  ))}
                </Stepper> */}
              </StepperSection>

              <ContentSection>

                <FormSection>
                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="h5"
                      component="h2"
                      sx={{
                        fontWeight: 600,
                        color: '#1a1a1a',
                        fontSize: '20px',
                        mb: 1,
                      }}
                    >
                      {title}
                    </Typography>
                    {/* {subtitle && (
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#666',
                          fontSize: '14px',
                          lineHeight: 1.5,
                        }}
                      >
                        {subtitle}
                      </Typography>
                    )} */}
                  </Box>

                  {children}
                </FormSection>
              </ContentSection>
            </Box>
          </StyledPaper>
        </Grid>
      </Grid2>

      {/* <OnboardingPage /> */}
    </StyledContainer >
  );
};
