'use client'

import { Container, Grid } from "@mui/material";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import CheckBox from "@/components/ui/CheckBox";
import Link from "@/components/ui/Link";
import Icon from "@/components/ui/Icon";
// import Select from "@/components/ui/Select";
import Stepper from "@/components/ui/Stepper";
import StepperExample from "@/components/ui/Stepper.example";
import Chip from "@/components/ui/Chip";
import { ScanFaceIcon } from "lucide-react";
import RadioButtonsGroup from "@/components/ui/Radio";
import Switch from "@/components/ui/Switch";

export default async function ComponentsPage() {

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">

      <Container maxWidth="xl" sx={{ py: 8 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <h1> Input Component</h1>
            <Input label="Name" />
          </Grid>

          <Grid item xs={12}>
            <h1> Button Component</h1>
            <Button variant="primary">Submit</Button>
            <Button variant="outlined">Cancel</Button>
            <Button variant="text">Reset</Button>
          </Grid>

          <Grid item xs={12}>
            <h1> Chip Component</h1>
            <Chip label="Chip" icon={<ScanFaceIcon />} onDelete={(e) => console.log(e)} />
          </Grid>

          <Grid item xs={12}>
            <h1> CheckBox Component</h1>
            <CheckBox indeterminate={true} />
          </Grid>

          <Grid item xs={12}>
            <h1> Link Component</h1>
            <Link href="#" >Link</Link>
          </Grid>

          <Grid item xs={12}>
            <h1> Icon Component</h1>
            <Icon name="ArrowRightGreen" />
          </Grid>

          <Grid item xs={12}>
            <h1> Logo Component</h1>
            <Icon name="LogoWhite" />
          </Grid>

          <Grid item xs={12}>
            <h1> Select Component</h1>
            {/* <Select label="Select" options={[{ label: 'Option 1', value: 1 }, { label: 'Option 2', value: 2 }]} /> */}
          </Grid>

          <Grid item xs={12}>
            <h1> Stepper Component</h1>
            <Stepper activeStep={0} steps={[{ step: 1, label: 'Step 1', description: 'Step 1 description' }, { step: 2, label: 'Step 2', description: 'Step 2 description' }, { step: 3, label: 'Step 3', description: 'Step 3 description' }]} />
          </Grid>

          <Grid item xs={12}>
            <h1> Stepper Example Component</h1>
            <StepperExample />
          </Grid>

          <Grid item xs={12}>
            <h1> Radio Component</h1>
            <RadioButtonsGroup
              label="Gender"
              options={[{ label: 'Female', value: 'female' }, { label: 'Male', value: 'male' }, { label: 'Other', value: 'other' }]}
              onChange={(e) => e.target.value}
              value={'female'}
            />
          </Grid>

          <Grid item xs={12}>
            <h1> Switch Component</h1>
            <Switch label="Enable" />
          </Grid>


        </Grid>
      </Container>


      <br />
    </main>
  );
}
