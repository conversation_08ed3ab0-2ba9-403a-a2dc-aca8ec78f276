/**
 * Checks whether a given file is valid for upload.
 *
 * @param {File} file File to check.
 * @returns {boolean} Whether the file is valid.
 *
 * Checks the following conditions to determine if the file is valid:
 *   1. The file's MIME type is one of the allowed MIME types: application/pdf,
 *      application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document.
 *   2. The file's size is less than 5MB.
 */
export function isValidFile(file: File): boolean {
  const allowedMimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];
  const maxFileSizeInBytes = 5 * 1024 * 1024; // 5 MB

  return allowedMimeTypes.includes(file.type) && file.size <= maxFileSizeInBytes;
}
