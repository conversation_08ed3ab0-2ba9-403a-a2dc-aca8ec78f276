"use client";

import { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Switch,
} from '@mui/material';
import { Notifications, Security, Palette, Language } from '@mui/icons-material';
import WithAuth from '../../src/components/WithAuth';
import MainLayout from '../../src/components/MainLayout';
import withAuth from '../../src/components/withAuthHOC';

interface Settings {
  notifications: boolean;
  darkMode: boolean;
  twoFactor: boolean;
  language: string;
}

const SettingsPage = () => {
  const [settings, setSettings] = useState<Settings>({
    notifications: true,
    darkMode: false,
    twoFactor: false,
    language: 'en',
  });

  // Handler to toggle settings
  const handleToggle = (setting: keyof Settings) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  return (
    <MainLayout>
      <WithAuth errorBoundary={true}>
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" gutterBottom>
            Settings
          </Typography>
          <Paper sx={{ mt: 2 }}>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Notifications />
                </ListItemIcon>
                <ListItemText primary="Notifications" secondary="Enable push notifications" />
                <Switch
                  edge="end"
                  checked={settings.notifications}
                  onChange={() => handleToggle('notifications')}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <Palette />
                </ListItemIcon>
                <ListItemText primary="Dark Mode" secondary="Toggle dark/light theme" />
                <Switch
                  edge="end"
                  checked={settings.darkMode}
                  onChange={() => handleToggle('darkMode')}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <Security />
                </ListItemIcon>
                <ListItemText
                  primary="Two-Factor Authentication"
                  secondary="Enable 2FA for better security"
                />
                <Switch
                  edge="end"
                  checked={settings.twoFactor}
                  onChange={() => handleToggle('twoFactor')}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <Language />
                </ListItemIcon>
                <ListItemText primary="Language" secondary="Current: English" />
              </ListItem>
            </List>
          </Paper>
        </Box>
      </WithAuth>
    </MainLayout>
  );
};

export default withAuth(SettingsPage, {
  errorBoundary: true,
});
