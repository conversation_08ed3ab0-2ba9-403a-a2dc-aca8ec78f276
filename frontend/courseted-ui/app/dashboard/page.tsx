// use client components
'use client';

import { FC, memo } from 'react';
import {
  Box,
  Typography,
  Grid2,
  Paper,
  Card,
  CardContent,
  LinearProgress,
  Avatar,
  Stack,
  Chip
} from '@mui/material';
import {
  School,
  TrendingUp,
  AccessTime,
  EmojiEvents,
  PlayCircleOutline
} from '@mui/icons-material';
// import { withAuth } from '../components/withAuthHOC';
import MainLayout from '../../src/components/MainLayout';
// import { getDashboardData, getMockAuth } from '../lib/auth-server';
import WithAuth from '@/components/WithAuth';

// Generate metadata for the dashboard page
// export async function generateMetadata() {
//   return {
//     title: 'Dashboard - CourseTed',
//     description: 'Your personal learning dashboard with course progress, statistics, and recent activity.',
//     robots: 'noindex, nofollow', // Private page, don't index
//   };
// }

// Server-side data fetching with authentication
function getDashboardPageData() {
  try {
    return {
      enrolledCourses: [
        {
          id: '1',
          title: 'React Advanced Patterns',
          progress: 75,
          lastAccessed: '2024-01-15T10:30:00Z',
          instructor: '<PERSON>',
          thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop',
        },
        {
          id: '2',
          title: 'Node.js Masterclass',
          progress: 45,
          lastAccessed: '2024-01-14T15:20:00Z',
          instructor: 'Mike Johnson',
          thumbnail: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=300&h=200&fit=crop',
        },
      ],
      stats: {
        totalCourses: 12,
        completedCourses: 8,
        totalHours: 156,
        certificatesEarned: 6,
      },
      recentActivity: [
        {
          id: '1',
          type: 'course_completed',
          title: 'Completed "JavaScript Fundamentals"',
          date: '2024-01-14T09:00:00Z',
        },
        {
          id: '2',
          type: 'certificate_earned',
          title: 'Earned certificate for "CSS Grid Layout"',
          date: '2024-01-13T14:30:00Z',
        },
      ],
    };
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    return null;
  }
}

const DashboardPage: FC = () => {
  const dashboardData = getDashboardPageData();

  return (
    <WithAuth errorBoundary={true}>
      <MainLayout>
        <Box sx={{ p: 3 }}>
          <Box mb={4}>
            <Typography variant="h4" gutterBottom>
              Welcome back, test!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Here's your learning progress and recent activity.
            </Typography>
          </Box>

          {/* Stats Overview */}
          <Grid2 container spacing={3} mb={4}>
            <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <School color="primary" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="primary.main">
                    {dashboardData?.stats.totalCourses || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Courses
                  </Typography>
                </CardContent>
              </Card>
            </Grid2>
            <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <TrendingUp color="success" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="success.main">
                    {dashboardData?.stats.completedCourses || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                </CardContent>
              </Card>
            </Grid2>
            <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <AccessTime color="info" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="info.main">
                    {dashboardData?.stats.totalHours || 0}h
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Learning Hours
                  </Typography>
                </CardContent>
              </Card>
            </Grid2>
            <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <EmojiEvents color="warning" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="warning.main">
                    {dashboardData?.stats.certificatesEarned || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Certificates
                  </Typography>
                </CardContent>
              </Card>
            </Grid2>
          </Grid2>

          <Grid2 container spacing={3}>
            {/* Enrolled Courses */}
            <Grid2 size={{ xs: 12, md: 8 }}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Continue Learning
                </Typography>
                <Stack spacing={2}>
                  {dashboardData?.enrolledCourses.map((course) => (
                    <Card key={course.id} variant="outlined">
                      <CardContent>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar
                            src={course.thumbnail}
                            alt={course.title}
                            variant="rounded"
                            sx={{ width: 60, height: 60 }}
                          />
                          <Box flexGrow={1}>
                            <Typography variant="h6" gutterBottom>
                              {course.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              by {course.instructor}
                            </Typography>
                            <Box display="flex" alignItems="center" gap={1} mb={1}>
                              <LinearProgress
                                variant="determinate"
                                value={course.progress}
                                sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                              />
                              <Typography variant="body2" color="text.secondary">
                                {course.progress}%
                              </Typography>
                            </Box>
                            <Typography variant="caption" color="text.secondary">
                              Last accessed: {new Date(course.lastAccessed).toLocaleDateString()}
                            </Typography>
                          </Box>
                          <PlayCircleOutline color="primary" sx={{ fontSize: 32 }} />
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Stack>
              </Paper>
            </Grid2>

            {/* Recent Activity */}
            <Grid2 size={{ xs: 12, md: 4 }}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Recent Activity
                </Typography>
                <Stack spacing={2}>
                  {dashboardData?.recentActivity.map((activity) => (
                    <Box key={activity.id}>
                      <Typography variant="body2" gutterBottom>
                        {activity.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(activity.date).toLocaleDateString()}
                      </Typography>
                      <Chip
                        label={activity.type.replace('_', ' ')}
                        size="small"
                        variant="outlined"
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  ))}
                </Stack>
              </Paper>
            </Grid2>
          </Grid2>
        </Box>
      </MainLayout>
    </WithAuth>
  );
};

export default memo(DashboardPage); // Use memo to prevent unnecessary re-renders
