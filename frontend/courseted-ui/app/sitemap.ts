import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXTAUTH_URL || 'https://courseted.com';

  const routes = [
    '',
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/academy',
    '/articles',
    '/contact',
    '/webinars',
    '/talenthub',
  ];

  return routes.map((route) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: route === '' ? 'daily' : 'weekly',
    priority: route === '' ? 1 : 0.8,
  }));
}
