import React from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

const courses = [
  {
    title: 'Fundamentals of Web Development',
    subtitle: 'Master HTML, CSS, and JavaScript to build modern web apps.',
    lessons: 35,
    duration: '7h 15m',
    students: 45,
    author: '<PERSON>',
    price: '$39.99',
    rating: 4.8,
    image: '/images/course1.jpg',
  },
  {
    title: 'Advanced JavaScript Concepts',
    subtitle: 'Deep dive into ES6+, async programming, and JS patterns.',
    lessons: 28,
    duration: '6h 10m',
    students: 32,
    author: '<PERSON>',
    price: '$34.99',
    rating: 4.6,
    image: '/images/course2.jpg',
  },
  {
    title: 'React for Beginners',
    subtitle: 'Build UI with React and modern frontend tools.',
    lessons: 42,
    duration: '9h 00m',
    students: 27,
    author: '<PERSON>',
    price: '$44.99',
    rating: 4.9,
    image: '/images/course3.jpg',
  },
];

const PopularCoursesSlider = () => {
  return (
    <Box sx={{ px: 4, py: 6, position: 'relative' }}>
      <Typography variant="h5" fontWeight={700} mb={3}>
        Popular Courses
      </Typography>

      <Swiper
        modules={[Navigation]}
        spaceBetween={20}
        slidesPerView={1.1}
        breakpoints={{
          768: { slidesPerView: 2 },
          1024: { slidesPerView: 3 },
        }}
        navigation={{
          prevEl: '.prev-btn',
          nextEl: '.next-btn',
        }}
        style={{ paddingBottom: '40px' }}
      >
        {courses.map((course, idx) => (
          <SwiperSlide key={idx}>
            <Box
              sx={{
                backgroundColor: '#fff',
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: 2,
              }}
            >
              <Box component="img" src={course.image} alt={course.title} sx={{ width: '100%', height: 160, objectFit: 'cover' }} />
              <Box sx={{ p: 2 }}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Box
                    sx={{
                      px: 1.5,
                      py: 0.5,
                      bgcolor: 'success.light',
                      color: 'white',
                      borderRadius: '10px',
                      fontSize: 12,
                    }}
                  >
                    Web Development
                  </Box>
                  <Typography fontSize={12} color="text.secondary">
                    1h 30m
                  </Typography>
                </Box>
                <Typography fontWeight={600} fontSize={16} mb={0.5}>
                  {course.title}
                </Typography>
                <Typography fontSize={13} color="text.secondary" mb={2}>
                  {course.subtitle}
                </Typography>
                <Box display="flex" gap={2} fontSize={13} color="text.secondary">
                  <span>{course.lessons} lessons</span>
                  <span>· {course.duration}</span>
                  <span>· {course.students} students</span>
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <img src="/avatar.png" alt="avatar" width={24} height={24} style={{ borderRadius: '50%' }} />
                    <Typography fontSize={14}>{course.author}</Typography>
                  </Box>
                  <Typography fontWeight={600} color="green">
                    {course.price}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Navigation Arrows */}
      <IconButton className="prev-btn" sx={{ position: 'absolute', top: '50%', left: 0, transform: 'translateY(-50%)', zIndex: 2 }}>
        <ChevronLeft />
      </IconButton>
      <IconButton className="next-btn" sx={{ position: 'absolute', top: '50%', right: 0, transform: 'translateY(-50%)', zIndex: 2 }}>
        <ChevronRight />
      </IconButton>
    </Box>
  );
};

export default PopularCoursesSlider;
