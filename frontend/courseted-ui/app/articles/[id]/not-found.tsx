import { Box, Typography, Button } from '@mui/material';
import { ArrowBack } from '@mui/icons-material';
import Link from 'next/link';
import MainLayout from '@/components/MainLayout';

export default function NotFound() {
  return (
    <MainLayout>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="60vh"
        gap={3}
        textAlign="center"
        px={2}
      >
        <Typography variant="h1" component="h1" fontSize="6rem" fontWeight={700} color="primary.main">
          404
        </Typography>
        <Typography variant="h4" component="h2" fontWeight={600} gutterBottom>
          Article Not Found
        </Typography>
        <Typography variant="body1" color="text.secondary" maxWidth={400}>
          The article you're looking for doesn't exist or may have been moved.
        </Typography>
        
        <Button
          component={Link}
          href="/articles"
          variant="contained"
          startIcon={<ArrowBack />}
          size="large"
          sx={{ mt: 2 }}
        >
          Back to Articles
        </Button>
      </Box>
    </MainLayout>
  );
}
