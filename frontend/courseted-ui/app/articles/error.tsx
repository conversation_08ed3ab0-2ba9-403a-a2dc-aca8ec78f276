'use client';

import { Box, Typography, Button, Alert } from '@mui/material';
import { RefreshRounded } from '@mui/icons-material';
import MainLayout from '@/components/MainLayout';
import { ErrorPageProps } from '@/types/components/error.types';

export default function Error({ error, reset }: ErrorPageProps) {
  return (
    <MainLayout>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="60vh"
        gap={3}
        px={2}
      >
        <Alert severity="error" sx={{ maxWidth: 600 }}>
          <Typography variant="h6" gutterBottom>
            Something went wrong!
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {error.message || 'An unexpected error occurred while loading the articles.'}
          </Typography>
          {error.digest && (
            <Typography variant="caption" color="text.secondary">
              Error ID: {error.digest}
            </Typography>
          )}
        </Alert>
        
        <Button
          variant="contained"
          startIcon={<RefreshRounded />}
          onClick={reset}
          size="large"
        >
          Try Again
        </Button>
      </Box>
    </MainLayout>
  );
}
