import { FC } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Con<PERSON>er,
  <PERSON>rid2,
  Card,
  CardContent,
  Avatar,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@mui/material";
import {
  Edit,
  Email,
  Phone,
  LocationOn,
  CalendarToday,
  Verified,
  School
} from "@mui/icons-material";
import MainLayout from "../../src/components/MainLayout";
import {
  // requireAuth,
  getUserPreferences,
  getMockAuth
} from "../lib/auth-server";

// Generate metadata for the profile page
export async function generateMetadata() {
  return {
    title: 'Profile - CourseTed',
    description: 'Manage your profile information, preferences, and account settings.',
    robots: 'noindex, nofollow', // Private page, don't index
  };
}

// Server-side data fetching with authentication
async function getProfilePageData() {
  try {
    // In production, use: const user = await requireAuth();
    // For now, using mock auth for demonstration
    const { user, isAuthenticated } = await getMockAuth();

    if (!isAuthenticated || !user) {
      throw new Error('Authentication required');
    }

    const preferences = await getUserPreferences();

    // Mock additional profile data
    const profileData = {
      bio: "Passionate learner and technology enthusiast. Always eager to explore new technologies and share knowledge with the community.",
      location: "San Francisco, CA",
      phone: "+****************",
      website: "https://johndoe.dev",
      joinDate: "2023-06-15T00:00:00Z",
      skills: ["JavaScript", "React", "Node.js", "Python", "AWS", "Docker"],
      achievements: [
        { id: 1, title: "Full Stack Developer", date: "2024-01-15", type: "certification" },
        { id: 2, title: "React Expert", date: "2023-12-10", type: "badge" },
        { id: 3, title: "100 Hours Learned", date: "2023-11-20", type: "milestone" },
      ],
      stats: {
        coursesCompleted: 12,
        totalHours: 156,
        certificatesEarned: 6,
        skillsLearned: 15
      }
    };

    return {
      user,
      preferences,
      profileData,
    };
  } catch (error) {
    console.error('Failed to fetch profile data:', error);
    throw error;
  }
}

const ProfilePage: FC = async () => {
  const { user, preferences, profileData } = await getProfilePageData();

  return (
    <MainLayout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Grid2 container spacing={4}>
          {/* Profile Header */}
          <Grid2 size={{ xs: 12 }}>
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Box display="flex" alignItems="center" gap={3} mb={3}>
                  <Avatar
                    src={user.avatar}
                    alt={user.name}
                    sx={{ width: 120, height: 120 }}
                  />
                  <Box flexGrow={1}>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Typography variant="h4" component="h1">
                        {user.name}
                      </Typography>
                      {user.isVerified && (
                        <Verified color="primary" sx={{ fontSize: 24 }} />
                      )}
                    </Box>
                    <Typography variant="body1" color="text.secondary" gutterBottom>
                      {profileData.bio}
                    </Typography>
                    <Stack direction="row" spacing={2} flexWrap="wrap" gap={1}>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <Email fontSize="small" color="action" />
                        <Typography variant="body2">{user.email}</Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <Phone fontSize="small" color="action" />
                        <Typography variant="body2">{profileData.phone}</Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <LocationOn fontSize="small" color="action" />
                        <Typography variant="body2">{profileData.location}</Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <CalendarToday fontSize="small" color="action" />
                        <Typography variant="body2">
                          Joined {new Date(profileData.joinDate).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </Stack>
                  </Box>
                  <Button variant="outlined" startIcon={<Edit />}>
                    Edit Profile
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid2>

          {/* Stats Cards */}
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <School color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h4" color="primary.main">
                  {profileData.stats.coursesCompleted}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Courses Completed
                </Typography>
              </CardContent>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {profileData.stats.totalHours}h
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Learning Hours
                </Typography>
              </CardContent>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {profileData.stats.certificatesEarned}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Certificates
                </Typography>
              </CardContent>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">
                  {profileData.stats.skillsLearned}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Skills Learned
                </Typography>
              </CardContent>
            </Card>
          </Grid2>

          {/* Skills */}
          <Grid2 size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Skills
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                  {profileData.skills.map((skill) => (
                    <Chip key={skill} label={skill} variant="outlined" />
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid2>

          {/* Achievements */}
          <Grid2 size={{ xs: 12, md: 6 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Achievements
                </Typography>
                <Stack spacing={2}>
                  {profileData.achievements.map((achievement) => (
                    <Box key={achievement.id}>
                      <Typography variant="body1" fontWeight={500}>
                        {achievement.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(achievement.date).toLocaleDateString()} • {achievement.type}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid2>

          {/* Account Settings Preview */}
          <Grid2 size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Account Settings
                </Typography>
                <Grid2 container spacing={3}>
                  <Grid2 size={{ xs: 12, md: 6 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Preferences
                    </Typography>
                    <Stack spacing={1}>
                      <Typography variant="body2">
                        Theme: {preferences?.theme || 'Light'}
                      </Typography>
                      <Typography variant="body2">
                        Language: {preferences?.language || 'English'}
                      </Typography>
                      <Typography variant="body2">
                        Email Notifications: {preferences?.notifications?.email ? 'Enabled' : 'Disabled'}
                      </Typography>
                    </Stack>
                  </Grid2>
                  <Grid2 size={{ xs: 12, md: 6 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Privacy
                    </Typography>
                    <Stack spacing={1}>
                      <Typography variant="body2">
                        Profile Visibility: {preferences?.privacy?.profileVisible ? 'Public' : 'Private'}
                      </Typography>
                      <Typography variant="body2">
                        Show Email: {preferences?.privacy?.showEmail ? 'Yes' : 'No'}
                      </Typography>
                      <Typography variant="body2">
                        Show Phone: {preferences?.privacy?.showPhone ? 'Yes' : 'No'}
                      </Typography>
                    </Stack>
                  </Grid2>
                </Grid2>
                <Divider sx={{ my: 2 }} />
                <Button variant="outlined" href="/settings">
                  Manage Settings
                </Button>
              </CardContent>
            </Card>
          </Grid2>
        </Grid2>
      </Container>
    </MainLayout>
  );
};

export default ProfilePage;
