import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  LinearProgress,
  Grid,
} from '@mui/material';
import {
  CloudUpload,
  InsertDriveFile,
  Delete,
  CheckCircle,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import Icon from '@/components/ui/Icon';
import { palette } from '@/theme/palette';
import Button from '@/components/ui/Button';
import { isValidFile } from 'app/lib/helper-file';
import { ResumeUploadStepProps } from '@/types/pages/profile.types';

const UploadArea = styled(Paper)(({ theme, isDragOver }: { theme?: any; isDragOver?: boolean }) => ({
  border: `2px dashed ${isDragOver ? '#2196f3' : '#e0e0e0'}`,
  borderRadius: 12,
  padding: theme.spacing(4),
  textAlign: 'center',
  backgroundColor: isDragOver ? '#f3f8ff' : '#fafafa',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: '#2196f3',
    backgroundColor: '#f3f8ff',
  },
  boxShadow: 'none',
  width: '100%',
}));

const FilePreview = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2),
  border: '1px solid #e0e0e0',
  borderRadius: 8,
  backgroundColor: '#f9f9f9',
  marginTop: theme.spacing(2),
}));

const ButtonGroup = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginTop: theme.spacing(4),
}));

const PreviousButton = styled(Button)(() => ({
  backgroundColor: 'transparent',
  color: '#666',
  borderRadius: 32,
  padding: '12px 16px',
  textTransform: 'none',
  fontSize: '14px',
  fontWeight: 500,
  border: '1px solid #e0e0e0',
  '&:hover': {
    backgroundColor: '#f5f5f5',
    borderColor: '#ccc',
  },
  height: '44px',
}));

// const FinishButton = styled(Button)(({ theme }) => ({
//   backgroundColor: theme.palette.grey[900],
//   color: 'white',
//   borderRadius: 32,
//   padding: '12px 24px',
//   textTransform: 'none',
//   fontSize: '14px',
//   fontWeight: 500,
//   '&:hover': {
//     backgroundColor: '#333',
//   },
//   '&:disabled': {
//     // backgroundColor: '#e0e0e0',
//     color: '#999',
//   },
// }));



export const ResumeUploadStep: React.FC<ResumeUploadStepProps> = ({
  data,
  onDataChange,
  onFinish,
  onPrevious,
}) => {
  // const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // const handleDragOver = useCallback((e: React.DragEvent) => {
  //   e.preventDefault();
  //   setIsDragOver(true);
  // }, []);

  // const handleDragLeave = useCallback((e: React.DragEvent) => {
  //   e.preventDefault();
  //   setIsDragOver(false);
  // }, []);

  // const handleDrop = useCallback((e: React.DragEvent) => {
  //   e.preventDefault();
  //   setIsDragOver(false);

  //   const files = Array.from(e.dataTransfer.files);
  //   const file = files[0];

  //   if (file && isValidFile(file)) {
  //     handleFileUpload(file);
  //   }
  // }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && isValidFile(file)) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          onDataChange({ resumeFile: file });
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleRemoveFile = () => {
    onDataChange({ resumeFile: null });
    setUploadProgress(0);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {!data.resumeFile ? (
            <UploadArea
              // isDragOver={isDragOver}
              // onDragOver={handleDragOver}
              // onDragLeave={handleDragLeave}
              // onDrop={handleDrop}
              onClick={() => document.getElementById('resume-upload')?.click()}
            >
              <CloudUpload
                sx={{
                  fontSize: 48,
                  // color: isDragOver ? '#2196f3' : '#ccc',
                  mb: 2,
                }}
              />
              <Typography
                variant="h6"
                sx={{
                  color: '#333',
                  mb: 1,
                  fontSize: 16,
                  fontWeight: 600,
                }}
              >
                <Typography component="span" sx={{ color: palette.primary[600], fontSize: 16, fontWeight: 600 }}> Click to upload</Typography> or drag and drop
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: palette.grey[500],
                  fontSize: '14px',
                  mb: 2,
                }}
              >
                PDF, DOC, or DOCX (max 5MB)
              </Typography>

              <input
                id="resume-upload"
                type="file"
                accept=".pdf,.doc,.docx"
                style={{ display: 'none' }}
                onChange={handleFileSelect}
              />
            </UploadArea>
          ) : (
            <FilePreview>
              <InsertDriveFile sx={{ color: '#2196f3', mr: 2 }} />
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: 500,
                    fontSize: '14px',
                    color: '#333',
                  }}
                >
                  {data.resumeFile.name}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: '#666',
                    fontSize: '12px',
                  }}
                >
                  {formatFileSize(data.resumeFile.size)}
                </Typography>
              </Box>
              {uploadProgress === 100 && (
                <CheckCircle sx={{ color: '#4caf50', mr: 1 }} />
              )}
              <IconButton
                onClick={handleRemoveFile}
                size="small"
                sx={{ color: '#666' }}
              >
                <Delete />
              </IconButton>
            </FilePreview>
          )}
        </Grid>

        <Grid item xs={12}>
          {isUploading && (
            <Box sx={{ mt: 2 }}>
              <Typography
                variant="body2"
                sx={{
                  color: '#666',
                  fontSize: '12px',
                  mb: 1,
                }}
              >
                Uploading... {uploadProgress}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={uploadProgress}
                sx={{
                  height: 4,
                  borderRadius: 2,
                  backgroundColor: '#e0e0e0',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#2196f3',
                    borderRadius: 2,
                  },
                }}
              />
            </Box>
          )}

          <ButtonGroup>
            <PreviousButton onClick={onPrevious}>
              <Icon name="ArrowLeft" sx={{ mr: 1 }} /> Previous
            </PreviousButton>

            <Button
              type="submit"
              variant="primary"
              size="md"
              disabled={!data.resumeFile || isUploading}
              onClick={onFinish}
              sx={{ px: 4 }}
            >
              Finish
            </Button>
          </ButtonGroup>
        </Grid>
      </Grid>
    </Box>
  );
};
