'use client';

import React, { useState } from 'react';
import { ProfileSetupLayout } from '../layout/ProfileSetupLayout';
import { StudentInformationStep } from './StudentInformationStep';
import { EducationSkillsStep } from './EducationSkillsStep';
import { ResumeUploadStep } from './ResumeUploadStep';
import {
  StudentInformationData,
  EducationSkillsData,
  ResumeUploadData,
  ProfileSetupData
} from '@/types/pages/profile.types';

const initialData: ProfileSetupData = {
  studentInformation: {
    profilePhoto: null,
    firstName: '',
    lastName: '',
    phoneNumber: '',
    selectedCountry: null,
  },
  educationSkills: {
    linkedinUrl: '',
    githubUrl: '',
    educationLevel: '',
    major: '',
  },
  resumeUpload: {
    resumeFile: null,
  },
};

// const stepLabels = ['Student Information', 'Education and Skills', 'Upload Resume'];
const stepLabels = [
  {
    step: 1,
    label: 'Student Information',
    description: `Fill out all the information to setup your profile. Once finished, you can start learning automation.`,
  },
  {
    step: 2,
    label: 'Professional Information',
    description:
      'Fill out all the information to setup your profile. Once finished, you can start learning automation.',
  },
  {
    step: 3,
    label: 'Upload Resume',
    description: `Fill out all the information to setup your profile. Once finished, you can start learning automation.`,
  },
];

const stepTitles = [
  'Student Information',
  'Professional Information',
  'Upload Resume',
];

const stepSubtitles = [
  'One of the developers we work with profile. Once finished you can start looking for opportunities.',
  'One of the developers we work with profile. Once finished you can start looking for opportunities.',
  'One of the developers we work with profile. Once finished you can start looking for opportunities.',
];

export const ProfileSetupContainer: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<ProfileSetupData>(initialData);

  const handleStudentInformationChange = (data: StudentInformationData) => {
    setFormData(prev => ({
      ...prev,
      studentInformation: data,
    }));
  };

  const handleEducationSkillsChange = (data: EducationSkillsData) => {
    setFormData(prev => ({
      ...prev,
      educationSkills: data,
    }));
  };

  const handleResumeUploadChange = (data: ResumeUploadData) => {
    // console.log('handleResumeUploadChange: ', data);
    setFormData(prev => ({
      ...prev,
      resumeUpload: data,
    }));
  };

  const handleNext = () => {
    if (currentStep < stepLabels.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleFinish = async () => {
    try {
      // Here you would typically submit the form data to your API
      console.log('Profile setup completed:', formData);

      // For now, just show an alert
      alert('Profile setup completed successfully!');

      // You could redirect to dashboard or another page here
      // navigate('/dashboard');
    } catch (error) {
      console.error('Error submitting profile setup:', error);
      alert('Error submitting profile setup. Please try again.');
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <StudentInformationStep
            data={formData.studentInformation}
            onDataChange={handleStudentInformationChange}
            onNext={handleNext}
          />
        );
      case 1:
        return (
          <EducationSkillsStep
            data={formData.educationSkills}
            onDataChange={handleEducationSkillsChange}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        );
      case 2:
        return (
          <ResumeUploadStep
            data={formData.resumeUpload}
            onDataChange={handleResumeUploadChange}
            onFinish={handleFinish}
            onPrevious={handlePrevious}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ProfileSetupLayout
      currentStep={currentStep}
      totalSteps={stepLabels.length}
      stepLabels={stepLabels}
      title={stepTitles[currentStep]}
      subtitle={stepSubtitles[currentStep]}
    >
      {renderCurrentStep()}
    </ProfileSetupLayout>
  );
};
