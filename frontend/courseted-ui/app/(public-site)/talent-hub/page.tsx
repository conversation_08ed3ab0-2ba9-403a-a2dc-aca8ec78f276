import { FC } from "react";
import {
  <PERSON>,
  Typography,
  Container,
  Grid2,
  <PERSON>,
  CardContent,
  Button,
  Stack,
  Chip,
  Avatar
} from "@mui/material";
import { Work, TrendingUp, People, Star } from "@mui/icons-material";
import MainLayout from "../../src/components/MainLayout";

// Enable ISR with revalidation every 900 seconds (15 minutes)
export const revalidate = 900;

// Generate metadata for the talent hub page
export async function generateMetadata() {
  return {
    title: 'Talent Hub - CourseTed',
    description: 'Connect with top talent and find your next career opportunity. Explore job openings and showcase your skills.',
    keywords: 'talent hub, jobs, careers, hiring, recruitment, professionals',
    openGraph: {
      title: 'Talent Hub - CourseTed',
      description: 'Connect with top talent and find your next career opportunity. Explore job openings and showcase your skills.',
      type: 'website',
    },
  };
}

// Fetch talent hub data
async function getTalentHubData() {
  try {
    // Replace with your actual API endpoint
    // const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/talent-hub/featured`, {
    //   next: { revalidate: 900 }
    // });

    // For now, return mock data
    return {
      featuredJobs: [
        {
          id: 1,
          title: "Senior Full Stack Developer",
          company: "TechCorp Inc.",
          location: "Remote",
          salary: "$120k - $150k",
          type: "Full-time",
          skills: ["React", "Node.js", "TypeScript", "AWS"],
          logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center"
        },
        {
          id: 2,
          title: "Data Scientist",
          company: "DataFlow Solutions",
          location: "San Francisco, CA",
          salary: "$130k - $160k",
          type: "Full-time",
          skills: ["Python", "Machine Learning", "SQL", "TensorFlow"],
          logo: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=100&h=100&fit=crop&crop=center"
        },
        {
          id: 3,
          title: "UX/UI Designer",
          company: "Design Studio Pro",
          location: "New York, NY",
          salary: "$90k - $120k",
          type: "Full-time",
          skills: ["Figma", "Adobe Creative Suite", "Prototyping", "User Research"],
          logo: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=100&h=100&fit=crop&crop=center"
        }
      ],
      stats: {
        totalJobs: 1250,
        companiesHiring: 350,
        successfulPlacements: 2800,
        averageSalary: "$95k"
      },
      topCompanies: [
        { name: "TechCorp Inc.", openings: 15 },
        { name: "DataFlow Solutions", openings: 8 },
        { name: "Design Studio Pro", openings: 12 },
        { name: "CloudTech Systems", openings: 6 }
      ]
    };
  } catch (error) {
    console.error('Error fetching talent hub data:', error);
    return {
      featuredJobs: [],
      stats: {
        totalJobs: 1250,
        companiesHiring: 350,
        successfulPlacements: 2800,
        averageSalary: "$95k"
      },
      topCompanies: []
    };
  }
}

const TalentHubPage: FC = async () => {
  const data = await getTalentHubData();

  return (
    <MainLayout>
      <Container maxWidth="xl" sx={{ py: 8 }}>
        <Box textAlign="center" mb={6}>
          <Typography variant="h2" component="h1" gutterBottom fontWeight={600}>
            Talent Hub
          </Typography>
          <Typography variant="h6" color="text.secondary" maxWidth={600} mx="auto">
            Connect with top employers and discover your next career opportunity. Showcase your skills and find the perfect match.
          </Typography>
        </Box>

        <Grid2 container spacing={4} mb={8}>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Work color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.totalJobs.toLocaleString()}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Active Job Openings
              </Typography>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <People color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.companiesHiring}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Companies Hiring
              </Typography>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <TrendingUp color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.successfulPlacements.toLocaleString()}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Successful Placements
              </Typography>
            </Card>
          </Grid2>
          <Grid2 size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Star color="primary" sx={{ fontSize: 40, mb: 2 }} />
              <Typography variant="h4" color="primary.main" fontWeight={700}>
                {data.stats.averageSalary}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Average Salary
              </Typography>
            </Card>
          </Grid2>
        </Grid2>

        <Typography variant="h4" component="h2" gutterBottom mb={4}>
          Featured Job Opportunities
        </Typography>

        <Grid2 container spacing={4} mb={8}>
          {data.featuredJobs.map((job) => (
            <Grid2 key={job.id} size={{ xs: 12, md: 6, lg: 4 }}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" alignItems="center" gap={2} mb={2}>
                    <Avatar src={job.logo} alt={job.company} />
                    <Box>
                      <Typography variant="h6" component="h3">
                        {job.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {job.company}
                      </Typography>
                    </Box>
                  </Box>

                  <Stack spacing={1} mb={2}>
                    <Typography variant="body2" color="text.secondary">
                      📍 {job.location}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      💰 {job.salary}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      ⏰ {job.type}
                    </Typography>
                  </Stack>

                  <Box mb={3}>
                    <Typography variant="body2" color="text.secondary" mb={1}>
                      Required Skills:
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                      {job.skills.map((skill) => (
                        <Chip key={skill} label={skill} size="small" variant="outlined" />
                      ))}
                    </Stack>
                  </Box>

                  <Button variant="contained" fullWidth>
                    Apply Now
                  </Button>
                </CardContent>
              </Card>
            </Grid2>
          ))}
        </Grid2>

        <Card sx={{ p: 4, textAlign: 'center', bgcolor: 'primary.50' }}>
          <Typography variant="h5" gutterBottom>
            Ready to Find Your Dream Job?
          </Typography>
          <Typography variant="body1" color="text.secondary" mb={3}>
            Join thousands of professionals who have found their perfect career match through our platform.
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
            <Button variant="contained" size="large">
              Browse All Jobs
            </Button>
            <Button variant="outlined" size="large">
              Post Your Resume
            </Button>
          </Stack>
        </Card>
      </Container>
    </MainLayout>
  );
};

export default TalentHubPage;
