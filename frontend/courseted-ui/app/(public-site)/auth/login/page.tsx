'use client';

// Force dynamic rendering to avoid prerendering issues
export const dynamic = 'force-dynamic';

import React, { memo, useState, useMemo } from 'react';
import { Box, Typography, Stack, Checkbox, FormControlLabel, useTheme, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Icon from '@/components/ui/Icon';
import Link from '@/components/ui/Link';
import theme from '@/theme';
import { useAuth } from '@/hooks/redux/useAuth';
import { loginSchema } from '@/utils/validations/validationSchemas';
import ErrorBoundary from '../../../src/components/error-boundary/ErrorBoundary';
import { useFormValidation } from '@/hooks/useFormValidation';
import { getErrorMessage } from '@/utils/helpers';
import { LoginFormData } from '@/types/pages/auth.types';
import { tokenUtils } from '@/utils/cookies';

const LoginPage = () => {
  const router = useRouter();
  const { login, isLoggingIn, loginError } = useAuth();
  const muiTheme = useTheme();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('sm'));

  const { errors, validateField, validateForm, clearAllErrors } = useFormValidation(loginSchema);

  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form validation with useMemo
  const isFormValid = useMemo(() => {
    const requiredFields = [formData.email, formData.password];
    const allFieldsFilled = requiredFields.every(field => Boolean(field));
    const noErrors = !Object.values(errors).some(error => error);
    return allFieldsFilled && noErrors;
  }, [formData, errors]);

  // useEffect(() => {
  //   if (isAuthenticated) {
  //     router.push('/');
  //   }
  // }, [isAuthenticated, router]);

  const handleClickShowPassword = () => setShowPassword(show => !show);

  const handleRememberMeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRememberMe(event.target.checked);
  };

  const handleInputChange = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return async (fieldName: keyof LoginFormData, value: string) => {
      setFormData(prev => ({ ...prev, [fieldName]: value }));

      // Clear general error
      if (error) setError(null);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Validate field with debounce
      timeoutId = setTimeout(() => {
        validateField(fieldName as any, value, { ...formData, [fieldName]: value });
      }, 300);
    };
  }, [formData, error, validateField]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    clearAllErrors();

    const { isValid } = await validateForm(formData);

    if (!isValid) {
      return;
    }

    try {
      console.log('Login attempt with:', formData, 'Remember me:', rememberMe);

      // Use the useAuth hook's login function
      const loginResult = await login({
        email: formData.email,
        password: formData.password,
        rememberMe,
      });

      if (loginResult.data?.accessToken) {
        const decodedToken = tokenUtils.getDecodedToken();
        console.log('Decoded token:::', decodedToken);
        console.log('includes(decodedToken?.role):::', ['student', 'user', 'instructor'].includes(decodedToken?.role), decodedToken?.role);

        if (['student', 'user', 'instructor'].includes(decodedToken?.role) && loginResult.data?.user?.isEmailVerified === false) {  // && loginResult.data?.user?.isEmailVerified === false) {
          router.push('/auth/verify-email');
        }
        else {
          router.push('/');
        }
      }
    } catch (apiError) {
      setError(getErrorMessage(apiError, loginError, 'Login failed. Please check your credentials and try again.'));
    }
  };

  // Get current error message
  const currentError = error || (loginError ? getErrorMessage(null, loginError, 'Login failed. Please try again.') : null);

  return (
    <ErrorBoundary>
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: theme.palette.grey[100],
          p: 4,
        }}
      >
        <Stack spacing={4} alignItems="center" sx={{ maxWidth: 400, width: '100%' }}>
          <Icon name="LogoBlack" width={'122px'} height={'20px'} />

          <Stack spacing={1.5} alignItems="center" sx={{ width: '100%' }}>
            <Typography
              variant="header3xs"
              sx={{ fontWeight: 'bold', textAlign: 'center', mt: '24px' }}
            >
              Login to continue
            </Typography>
            <Typography variant="textSm" color="text.secondary" sx={{ textAlign: 'center' }}>
              Welcome back, user! Log back in and catch up with what you have missed.
            </Typography>
          </Stack>

          <Stack spacing={3} sx={{ width: '100%', mt: 2 }}>
            <Input
              label="Email"
              placeholder="Enter email address"
              fullWidth
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={!!errors.email}
              helperText={errors.email}
              disabled={isLoggingIn}
            />

            <Input
              label="Password"
              placeholder="Enter password"
              fullWidth
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              error={!!errors.password}
              helperText={errors.password}
              disabled={isLoggingIn}
              rightIcon={
                <Icon
                  name={showPassword ? 'ViewOn' : 'ViewOff'}
                  onClick={handleClickShowPassword}
                  sx={{
                    cursor: 'pointer',
                    color: theme.palette.grey[600],
                    pointerEvents: isLoggingIn ? 'none' : 'auto',
                    opacity: isLoggingIn ? 0.6 : 1,
                  }}
                />
              }
            />

            {currentError && (
              <Typography
                color="error"
                variant="caption"
                sx={{
                  textAlign: 'center',
                  mt: -1,
                  mb: 1,
                  backgroundColor: 'error.50',
                  padding: 1,
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'error.200',
                }}
              >
                {currentError}
              </Typography>
            )}

            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ width: '100%', mt: 1 }}
            >
              <FormControlLabel
                control={
                  <Checkbox
                    checked={rememberMe}
                    onChange={handleRememberMeChange}
                    name="rememberMe"
                    disabled={isLoggingIn}
                    sx={{
                      padding: '4px',
                      '& .MuiSvgIcon-root': {
                        fontSize: 20,
                        fill: rememberMe ? theme.palette.grey[900] : theme.palette.grey[400],
                      },
                    }}
                  />
                }
                label={<Typography variant="textSm">Remember me</Typography>}
                sx={{
                  '& .MuiFormControlLabel-label': { fontSize: '14px' },
                  opacity: isLoggingIn ? 0.6 : 1,
                }}
              />
              <Link
                href="/auth/forgot-password"
                customSize="md"
                customType="secondary"
                sx={{
                  pointerEvents: isLoggingIn ? 'none' : 'auto',
                  opacity: isLoggingIn ? 0.6 : 1,
                }}
              >
                Forgot password?
              </Link>
            </Stack>

            {/* <Button
              type="submit"
              variant="primary"
              size="md"
              fullWidth
              loading={isLoggingIn}
              disabled={isLoggingIn || !isFormValid}
              sx={{
                opacity: !isFormValid ? 0.6 : 1,
                transition: 'opacity 0.2s ease-in-out',
              }}
            >
              {isLoggingIn ? 'Signing in...' : 'Login'}
            </Button> */}

            <Button
              type="submit"
              variant="primary"
              size={isMobile ? 'lg' : 'md'}
              fullWidth
              fullWidthOnMobile
              loading={isLoggingIn}
              disabled={isLoggingIn || !isFormValid}
              sx={{
                opacity: !isFormValid ? 0.6 : 1,
                transition: 'opacity 0.2s ease-in-out',
                mt: { xs: 1, sm: 0 },
              }}
            >
              {isLoggingIn ? 'Signing in...' : 'Login'}
            </Button>
            {!isFormValid && !isLoggingIn && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{
                  textAlign: 'center',
                  mt: -2,
                  fontSize: '0.75rem',
                  fontStyle: 'italic'
                }}
              >
                Please fill in all required fields to continue
              </Typography>
            )}

            <Typography variant="textSm" sx={{ textAlign: 'center', mt: 2 }}>
              Don't have an account?{' '}
              <Link href="/auth/register" customSize="md" customType="primary">
                Create account
              </Link>
            </Typography>
          </Stack>
        </Stack>
      </Box>
    </ErrorBoundary>
  );
};

export default memo(LoginPage);
