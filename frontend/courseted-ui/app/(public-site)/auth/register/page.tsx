'use client';

// Force dynamic rendering to avoid prerendering issues
export const dynamic = 'force-dynamic';

import { Input as BaseInput, Box, Typography, Stack, Divider, Autocomplete, TextField, Grid, InputLabel } from '@mui/material';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Icon from '@/components/ui/Icon';
import Link from '@/components/ui/Link';
import theme from '@/theme';
import React, { memo, useState, useMemo, useEffect } from 'react';
import { useGetCountriesQuery } from '@/features/countries/countriesApi';
import type { Country } from '@/types/countries.types';
import { registerSchema, type RegisterFormData } from '@/utils/validations/validationSchemas';
import { useFormValidation } from '@/hooks/useFormValidation';
import { useAuth } from '@/hooks/redux/useAuth';
import type { RegisterRequest } from '@/types/auth.types';
import { getErrorMessage } from '@/utils/helpers';
import { SignUpFormData } from '@/types/pages/auth.types';

const RegisterPage = () => {
  const { register, isRegistering, isAuthenticated, registerError } = useAuth();
  const { data: countriesResponse, isLoading: isCountriesLoading } = useGetCountriesQuery();
  const countries = countriesResponse?.data || [];

  const [formData, setFormData] = useState<SignUpFormData>({
    email: '',
    password: '',
    correctPassword: '',
    phoneNumber: '',
    selectedCountry: null,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use Yup validation hook
  const { errors, validateField, validateForm, clearAllErrors } = useFormValidation(registerSchema);

  // Optimized form validation with useMemo
  const isFormValid = useMemo(() => {
    const requiredFields = [
      formData.email,
      formData.password,
      formData.correctPassword,
      formData.phoneNumber,
      formData.selectedCountry
    ];

    const allFieldsFilled = requiredFields.every(field => Boolean(field));
    const noErrors = !Object.values(errors).some(error => error);

    return allFieldsFilled && noErrors;
  }, [formData, errors]);

  const handleClickShowPassword = () => setShowPassword(show => !show);
  const handleClickShowConfirmPassword = () => setShowConfirmPassword(show => !show);

  const handleChange = useMemo(() => {
    let timeoutId: NodeJS.Timeout;

    return (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;

      setFormData(prev => ({ ...prev, [name]: value }));

      // Clear general error
      if (error) setError(null);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Validate field with debounce
      timeoutId = setTimeout(() => {
        validateField(name as keyof RegisterFormData, value, { ...formData, [name]: value });
      }, 300);
    };
  }, [formData, error, validateField]);

  const handleCountryChange = useMemo(() =>
    async (_event: any, newValue: Country | null) => {
      setFormData(prev => ({ ...prev, selectedCountry: newValue }));
      await validateField('selectedCountry', newValue, { ...formData, selectedCountry: newValue });
    }, [formData, validateField]
  );

  const handleBlur = useMemo(() =>
    async (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      await validateField(name as keyof RegisterFormData, value, formData);
    }, [formData, validateField]
  );

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    clearAllErrors();

    // Double-check validation before submission
    const { isValid, errors: formErrors } = await validateForm(formData);

    if (!isValid) {
      console.log('Form validation failed:', formErrors);
      return;
    }

    try {
      // Prepare registration data
      const registrationData: RegisterRequest = {
        name: '', // Add name field or remove from RegisterRequest type
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.correctPassword,
        phoneNumber: `${formData.selectedCountry?.phoneCode}${formData.phoneNumber}`,
        countryId: formData.selectedCountry?.id || '',
      };

      console.log('Attempting to register with:', registrationData);

      // Call the register API
      const response = await register(registrationData);

      console.log('Registration successful:', response);
      // Redirect to login or email verification page
      window.location.href = '/auth/login';

    } catch (apiError: any) {
      setError(getErrorMessage(apiError, registerError, 'Registration failed. Please try again.'));
    }
  };

  const formatPhoneNumberDisplay = useMemo(() => {
    if (!formData.selectedCountry || !formData.phoneNumber) return formData.phoneNumber || '';
    return `${formData.selectedCountry.phoneCode} ${formData.phoneNumber}`;
  }, [formData.selectedCountry, formData.phoneNumber]);

  useEffect(() => {
    if (isAuthenticated) {
      window.location.href = '/';
    }
  }, [isAuthenticated]);

  // Get current error message
  const currentError = error || (registerError ? getErrorMessage(null, registerError, 'Registration failed. Please try again.') : null);

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: theme.palette.grey[100],
        p: 4,
      }}
    >
      <Stack spacing={4} alignItems="center" sx={{ maxWidth: 400, width: '100%' }}>
        <Icon name="LogoBlack" width={'122px'} height={'20px'} />

        <Stack spacing={1.5} alignItems="center" sx={{ width: '100%' }}>
          <Typography
            variant="header3xs"
            sx={{ fontWeight: 'bold', textAlign: 'center', mt: '24px' }}
          >
            Create an account. It's free!
          </Typography>
          <Typography variant="textSm" color="text.secondary" sx={{ textAlign: 'center' }}>
            Create a free account and join us with 500+ professionals and build your career by
            unlocking your fullest potential.
          </Typography>
        </Stack>

        <Stack spacing={3} sx={{ width: '100%', mt: 2 }}>
          <Input
            label="Email"
            placeholder="Enter email address"
            fullWidth
            required
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            onBlur={handleBlur}
            autoComplete="email"
            error={!!errors.email}
            helperText={errors.email}
            disabled={isRegistering}
          />

          <Input
            label="Password"
            placeholder="Enter password"
            fullWidth
            required
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={handleChange}
            onBlur={handleBlur}
            autoComplete="new-password"
            rightIcon={
              <Icon
                name={showPassword ? 'ViewOn' : 'ViewOff'}
                onClick={handleClickShowPassword}
                sx={{ cursor: 'pointer', color: theme.palette.grey[600] }}
              />
            }
            error={!!errors.password}
            helperText={errors.password}
            disabled={isRegistering}
          />

          <Input
            label="Confirm Password"
            placeholder="Enter password again"
            fullWidth
            required
            id="correct-password"
            name="correctPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            value={formData.correctPassword}
            onChange={handleChange}
            onBlur={handleBlur}
            autoComplete="new-password"
            rightIcon={
              <Icon
                name={showConfirmPassword ? 'ViewOn' : 'ViewOff'}
                onClick={handleClickShowConfirmPassword}
                sx={{ cursor: 'pointer', color: theme.palette.grey[600] }}
              />
            }
            error={!!errors.correctPassword}
            helperText={errors.correctPassword}
            disabled={isRegistering}
          />

          {/* Country Selector */}
          {/* <Box>
            <Typography
              variant="textSm"
              sx={{
                fontWeight: 500,
                color: theme.palette.text.primary,
                mb: 1,
              }}
            >
              Country <span style={{ color: theme.palette.error.main }}>*</span>
            </Typography>
            <Autocomplete
              options={countries}
              getOptionLabel={(option) => option.name}
              value={formData.selectedCountry}
              onChange={handleCountryChange}
              loading={isCountriesLoading}
              disabled={isRegistering}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Search and select your country"
                  error={!!errors.selectedCountry}
                  helperText={errors.selectedCountry}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: errors.selectedCountry ? theme.palette.error.main : theme.palette.grey[300],
                      },
                      '&:hover fieldset': {
                        borderColor: errors.selectedCountry ? theme.palette.error.main : theme.palette.grey[400],
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: errors.selectedCountry ? theme.palette.error.main : theme.palette.primary.main,
                      },
                    },
                  }}
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography sx={{ fontSize: '1.2rem' }}>{option.flag}</Typography>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2">{option.name}</Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {option.phoneCode}
                  </Typography>
                </Box>
              )}
              filterOptions={(options, { inputValue }) => {
                return options.filter(
                  (option) =>
                    option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                    option.code.toLowerCase().includes(inputValue.toLowerCase()) ||
                    option.phoneCode.includes(inputValue)
                );
              }}
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          </Box> */}
          <Grid item xs={12}>
            <Box>
              <InputLabel
                htmlFor={'selectedCountry'}
                id={'selectedCountry-label'}
                shrink
                margin="dense"
                sx={{
                  color: 'grey.900',
                  fontSize: '18px',
                  lineHeight: '20px',
                  fontWeight: 500,
                  '&.Mui-disabled': {
                    color: 'grey.900',
                  },
                  '&.Mui-focused': {
                    color: 'grey.900',
                  },
                  '&.Mui-error': {
                    color: 'red.500',
                  },
                  mb: 0.5,
                }}
              >
                Country
              </InputLabel>
              <Autocomplete
                options={countries}
                getOptionLabel={(option) => option.name}
                value={formData?.selectedCountry}
                onChange={handleCountryChange}
                loading={isCountriesLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Search and select your country"
                    variant="outlined"
                  />
                )}
                renderOption={(props, option) => (
                  <Box
                    component="li"
                    {...props}
                    key={`option-${option.id}`}
                    sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                  >
                    <Typography key={`typography1-${option.id}`} sx={{ fontSize: '1.2rem' }}>{option.flag}</Typography>
                    <Box key={`box-${option.id}`} sx={{ flexGrow: 1 }}>
                      <Typography variant="body2">{option.name}</Typography>
                    </Box>
                    <Typography key={`typography2-${option.id}`} variant="caption" color="text.secondary">
                      {option.phoneCode}
                    </Typography>
                  </Box>
                )}
                filterOptions={(options, { inputValue }) =>
                  options.filter(
                    (option) =>
                      option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                      option.code.toLowerCase().includes(inputValue.toLowerCase()) ||
                      option.phoneCode.includes(inputValue)
                  )
                }
                isOptionEqualToValue={(option, value) => option.id === value.id}
                sx={{
                  backgroundColor: 'white',
                  "& .MuiOutlinedInput-root": {
                    height: '44px',
                    borderRadius: "10px",
                    paddingRight: "8px",

                    // Focused state
                    "&.Mui-focused fieldset": {
                      borderColor: "green.600", // or use theme.palette.success.main
                      borderWidth: "2px",
                    },

                    // Default state
                    "& fieldset": {
                      borderColor: "grey.300",
                      transition: "border-color 0.3s ease, border-width 0.3s ease",
                    },
                  },

                  "& .MuiAutocomplete-inputRoot": {
                    padding: "4px 12px",
                    fontSize: "14px",
                  },
                }}
              />

            </Box>
          </Grid>

          {/* Phone Number Input */}
          <Box>
            <Typography
              variant="textSm"
              sx={{
                fontWeight: 500,
                color: theme.palette.text.primary,
                mb: 1,
              }}
            >
              Phone Number <span style={{ color: theme.palette.error.main }}>*</span>
            </Typography>
            <Box sx={{ display: 'flex', gap: 0, mt: 1 }}>
              {/* Country Code Display */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  px: 2,
                  py: 1.5,
                  border: `1px solid ${theme.palette.grey[300]}`,
                  borderRadius: '10px 0 0 10px',
                  backgroundColor: '#fff',
                  minWidth: 80,
                  height: '44px',
                }}
              >
                {formData.selectedCountry ? (
                  <>
                    <Typography sx={{ fontSize: '1rem' }}>{formData.selectedCountry.flag}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formData.selectedCountry.phoneCode}
                    </Typography>
                  </>
                ) : (
                  <Typography variant="body2" color="text.disabled">
                    +--
                  </Typography>
                )}
              </Box>

              {/* Phone Number Input */}
              {/* <Input
                placeholder="Enter phone number"
                fullWidth
                id="phone-number"
                name="phoneNumber"
                type="tel"
                label=''
                value={formData.phoneNumber}
                onChange={handleChange}
                onBlur={handleBlur}
                autoComplete="tel"
                error={!!errors.phoneNumber}
                helperText={errors.phoneNumber}
                // disabled={isRegistering || !formData.selectedCountry}
                disabled={isRegistering}
                sx={{ flex: 1, mt: 0 }}
              /> */}
              <BaseInput
                fullWidth
                disableUnderline
                sx={{
                  border: '1px solid',
                  borderColor: 'grey.300',
                  borderRadius: '0 10px 10px 0',
                  height: '44px',
                  px: 0,
                  py: 0,
                  backgroundColor: isRegistering || !formData.selectedCountry ? 'grey.100' : '#fff',
                  '&:hover': {
                    borderColor: 'grey.300',
                  },
                  '&.Mui-focused': {
                    borderColor: error ? 'red.500' : 'green.600',
                    borderWidth: '2px',
                  },
                  input: {
                    padding: '8px 12px',
                    color: 'grey.900',
                    fontSize: '14px', // 16px prevents zoom on iOS
                    lineHeight: '20px',
                    fontWeight: 400,
                    '&::placeholder': {
                      color: 'grey.600',
                      opacity: 1,
                    },
                  },
                  // marginTop: '24px !important',
                }}
                id="phone-number"
                name="phoneNumber"
                type="tel"
                placeholder="Enter phone number"
                value={formData.phoneNumber}
                onChange={handleChange}
                onBlur={handleBlur}
                autoComplete="tel"
                error={!!errors.phoneNumber}
                // helperText={errors.phoneNumber}
                disabled={isRegistering || !formData.selectedCountry}
              />

            </Box>
            {formData.selectedCountry && formData.phoneNumber && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                Complete number: {formatPhoneNumberDisplay}
              </Typography>
            )}
          </Box>

          {currentError && (
            <Typography
              color="error"
              variant="caption"
              sx={{
                textAlign: 'center',
                mt: -1,
                mb: 1,
                backgroundColor: 'error.50',
                padding: 1,
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'error.200',
              }}
            >
              {currentError}
            </Typography>
          )}

          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={isRegistering}
            disabled={isRegistering || !isFormValid}
            sx={{
              opacity: !isFormValid ? 0.6 : 1,
              transition: 'opacity 0.2s ease-in-out',
            }}
          >
            {isRegistering ? 'Creating account...' : 'Create account'}
          </Button>

          {!isFormValid && !isRegistering && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                textAlign: 'center',
                mt: -2,
                fontSize: '0.75rem',
                fontStyle: 'italic'
              }}
            >
              Please fill in all required fields to continue
            </Typography>
          )}
        </Stack>

        <Typography variant="textXs" color="text.secondary" sx={{ textAlign: 'center', mt: 2 }}>
          Already have an account?{' '}
          <Link
            href="/auth/login"
            customType="primary"
            customSize="md"
            sx={{
              fontWeight: 600,
              pointerEvents: isRegistering ? 'none' : 'auto',
              opacity: isRegistering ? 0.6 : 1,
            }}
          >
            Login
          </Link>
        </Typography>

        <Divider sx={{ width: '100%', my: 3 }}>
          <Typography variant="caption" color="text.secondary">
            Or
          </Typography>
        </Divider>

        <Stack spacing={1.5} sx={{ width: '100%' }}>
          <Button
            variant="outlined"
            size="md"
            fullWidth
            leftIcon={<Icon name="Google" sx={{ mr: 1 }} />}
            sx={{ justifyContent: 'center' }}
            disabled={isRegistering}
          >
            <Typography variant="textSm" fontWeight={600}>
              Continue with Google
            </Typography>
          </Button>
          <Button
            variant="outlined"
            size="md"
            fullWidth
            leftIcon={<Icon name="LinkedInColored" sx={{ mr: 1 }} />}
            sx={{ justifyContent: 'center' }}
            disabled={isRegistering}
          >
            <Typography variant="textSm" fontWeight={600}>
              Continue with LinkedIn
            </Typography>
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};

export default memo(RegisterPage);
