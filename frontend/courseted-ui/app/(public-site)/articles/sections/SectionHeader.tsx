import { palette } from '@/theme/palette';
import { Box, Typography } from '@mui/material';

const SectionHeader = () => {
    return (
        <Box
            alignItems="center"
            sx={{ backgroundColor: palette.grey[200] }}
            py={12}
        >
            <Typography variant="h1" align="center" mb={2} fontSize={'56px !important'} fontWeight={600} >
                Take your career to the next level<br />with an online degree
            </Typography>
            <Typography variant="h5" align="center" color="text.secondary" fontSize={'18px !important'} fontWeight={400}>
                Having trained over 300,000 QA professionals, we share our extensive knowledge <br /> and experience through a wide variety of testing courses.
            </Typography>
        </Box>
    );
};

export default SectionHeader;
