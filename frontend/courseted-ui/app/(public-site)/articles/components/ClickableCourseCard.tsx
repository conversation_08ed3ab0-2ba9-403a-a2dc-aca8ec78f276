'use client';

import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import CourseCard from '@/components/landing/components/CourseCard';
import { CourseCardProps } from '@/types/section.types';

export default function ClickableCourseCard({ course, sectionVisible }: Omit<CourseCardProps, 'cardStyle'>) {
    const router = useRouter();

    const handleClick = () => {
        router.push(`/articles/${course.id}`);
    };

    return (
        <Box
            onClick={handleClick}
            sx={{
                cursor: 'pointer',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    '& .MuiCard-root': {
                        boxShadow: '0 8px 25px rgba(0,0,0,0.12)',
                    },
                },
                '&:active': {
                    transform: 'translateY(-2px)',
                },
            }}
        >
            <CourseCard course={course} sectionVisible={sectionVisible} cardStyle={{ cardBackground: '#F2F2F7' }} />
        </Box>
    );
}
