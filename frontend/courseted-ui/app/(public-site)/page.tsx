import { FC } from 'react';
import TabSection from '@/components/landing/sections/TabSection';
import CtaSection from '@/components/landing/sections/CtaSection';
import CoursesSection from '@/components/landing/sections/CoursesSection';
import ArticleSection from '@/components/landing/sections/ArticleSection';
import HeroSection from '@/components/landing/sections/HeroSection';
import MainLayout from '../src/components/MainLayout';
import { Box } from '@mui/material';

// Enable ISR with revalidation every 300 seconds (5 minutes)
export const revalidate = 300;

// Generate metadata for the homepage
export async function generateMetadata() {
  return {
    title: 'CourseTed - Learn, Grow, Excel',
    description: 'Discover the best online courses and educational content. Join thousands of learners and advance your career with CourseTed.',
    keywords: 'online courses, education, learning, professional development, skills training',
    openGraph: {
      title: 'CourseTed - Learn, Grow, Excel',
      description: 'Discover the best online courses and educational content. Join thousands of learners and advance your career with CourseTed.',
      type: 'website',
      url: 'https://courseted.com',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'CourseTed - Learn, Grow, Excel',
      description: 'Discover the best online courses and educational content. Join thousands of learners and advance your career with CourseTed.',
    },
  };
}

// Fetch homepage data (this will be cached and revalidated)
async function getHomepageData() {
  try {
    // Fetch courses data
    // const coursesRes = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/courses?featured=true`, {
    //   next: { revalidate: 300 }
    // });

    // Fetch articles data
    // const articlesRes = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/articles?featured=true`, {
    //   next: { revalidate: 300 }
    // });

    // For now, return mock data
    const { courses, articles } = await import('@/data/mockData');

    return {
      courses: courses.slice(0, 6), // Featured courses
      articles: articles.slice(0, 4), // Featured articles
      stats: {
        totalStudents: 30000,
        totalCourses: 150,
        totalInstructors: 50,
        successRate: 95
      }
    };
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    // Return fallback data
    return {
      courses: [],
      articles: [],
      stats: {
        totalStudents: 30000,
        totalCourses: 150,
        totalInstructors: 50,
        successRate: 95
      }
    };
  }
}

const Home: FC = async () => {
  const data = await getHomepageData();

  return (
    <MainLayout>
      <Box>
        <HeroSection />
        <TabSection courses={data.courses} />
        <CoursesSection courses={data.courses} />
        <ArticleSection articles={data.articles} />
        <CtaSection />
      </Box>
    </MainLayout>
  );
};

export default Home;
