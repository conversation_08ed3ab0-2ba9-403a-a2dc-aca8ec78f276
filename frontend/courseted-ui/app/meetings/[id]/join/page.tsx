'use client';

import React, { useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Box, Typography, CircularProgress, <PERSON><PERSON>, Button } from '@mui/material';
import WithAuth from '@/components/WithAuth';
import { useGetMeetingByIdQuery } from '@/services/zoom/meetingApi';
import { useAuth } from '@/hooks/redux/useAuth';
import dynamic from 'next/dynamic';
import Script from 'next/script';


const MeetingRoom = dynamic(() => import('@/components/zoom/MeetingRoom'), {
  ssr: false,
});

// const ZoomMeetingSimple = dynamic(() => import('@/components/zoom/ZoomMeetingSimple'), {
//   ssr: false,
// });

// const ZoomMeetingComponent = dynamic(() => import('@/components/zoom/ZoomMeeting'), {
//   ssr: false,
// });

interface JoinMeetingPageProps {
  params: Promise<{
    id: string;
  }>;
}

const Join<PERSON>eetingPage: React.FC<JoinMeetingPageProps> = ({ params }) => {
  const router = useRouter();
  const { user } = useAuth();
  const { id } = use(params);
  console.log('params id:::', id);

  const { data: meetingData, isLoading, error } = useGetMeetingByIdQuery(id, {
    skip: !id || !user, // Skip query if no id or no user
  });

  console.log('meetingData:::', meetingData);
  console.log('error:::', error);
  console.log('isLoading:::', isLoading);
  console.log('user:::', user);

  // Meeting data is returned directly from the API
  const meeting = meetingData;

  useEffect(() => {
    // Check if meeting exists and user has permission to join
    if (meeting && meeting.status === 'cancelled') {
      router.push('/meetings');
    }
  }, [meeting, router]);

  const handleLeaveMeeting = () => {
    router.push('/meetings');
  };

  if (isLoading) {
    return (
      <WithAuth>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          p={3}
        >
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Loading Meeting...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Please wait while we prepare your meeting
          </Typography>
        </Box>
      </WithAuth>
    );
  }

  if (error || !meeting) {
    // console.error('Meeting error:', error);
    return (
      <WithAuth>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          p={3}
        >
          <Alert severity="error" sx={{ mb: 2, maxWidth: 400 }}>
            {error ? `Failed to load meeting details: ${JSON.stringify(error)}` : 'Meeting not found'}
          </Alert>
          <Button variant="outlined" onClick={() => router.push('/meetings')}>
            Back to Meetings
          </Button>
          <Button variant="text" onClick={() => window.location.reload()} sx={{ mt: 1 }}>
            Retry
          </Button>
        </Box>
      </WithAuth>
    );
  }

  if (meeting.status === 'cancelled') {
    return (
      <WithAuth>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          p={3}
        >
          <Alert severity="warning" sx={{ mb: 2, maxWidth: 400 }}>
            This meeting has been cancelled
          </Alert>
          <Button variant="outlined" onClick={() => router.push('/meetings')}>
            Back to Meetings
          </Button>
        </Box>
      </WithAuth>
    );
  }

  if (meeting.status === 'ended') {
    return (
      <WithAuth>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          p={3}
        >
          <Alert severity="info" sx={{ mb: 2, maxWidth: 400 }}>
            This meeting has ended
          </Alert>
          <Button variant="outlined" onClick={() => router.push('/meetings')}>
            Back to Meetings
          </Button>
        </Box>
      </WithAuth>
    );
  }

  return (
    <WithAuth>
      <MeetingRoom
        meeting={meeting}
        currentUser={user!}
        onLeave={handleLeaveMeeting}
      />

      {/* <ZoomMeetingComponent
        meeting={meeting}
        currentUser={user!}
        onLeave={handleLeaveMeeting}
      /> */}
      <Script src="/coi-serviceworker.js" strategy="beforeInteractive" />
    </WithAuth>
  );
};

export default JoinMeetingPage;
