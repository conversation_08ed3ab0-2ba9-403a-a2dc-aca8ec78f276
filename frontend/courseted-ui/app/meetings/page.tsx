'use client';

import React, { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Tabs,
  Tab,
  Fab,
  useTheme,
  useMediaQuery,
  Alert,
  Snackbar
} from '@mui/material';
import { Add, VideoCall } from '@mui/icons-material';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import WithAuth from '@/components/WithAuth';
import MeetingList from '@/components/zoom/MeetingList';
import CreateMeetingDialog from '@/components/zoom/CreateMeetingDialog';
import JoinMeetingDialog from '@/components/zoom/JoinMeetingDialog';
import DeleteConfirmationDialog from '@/components/zoom/DeleteConfirmationDialog';
import ShareMeetingDialog from '@/components/zoom/ShareMeetingDialog';
import EditMeetingDialog from '@/components/zoom/EditMeetingDialog';
import { useGetMeetingsQuery, useGetUpcomingMeetingsQuery, useDeleteMeetingMutation } from '@/services/zoom/meetingApi';
import { useAuth } from '@/hooks/redux/useAuth';
import { ZoomMeeting } from '@/types/zoom/meeting.types';
// import { ZoomMeeting } from '@/types/zoom';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`meetings-tabpanel-${index}`}
      aria-labelledby={`meetings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const MeetingsPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const router = useRouter();
  const { user } = useAuth();

  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [joinDialogOpen, setJoinDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedMeeting, setSelectedMeeting] = useState<ZoomMeeting | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  const { data: allMeetingsData, isLoading: allMeetingsLoading, error: allMeetingsError, refetch: refetchAllMeetings } = useGetMeetingsQuery({
    page: 1,
    limit: 20
  }, {
    skip: !user || !user.id, // Skip query if user is not authenticated
  });

  const { data: upcomingMeetingsData, isLoading: upcomingLoading, error: upcomingError, refetch: refetchUpcomingMeetings } = useGetUpcomingMeetingsQuery(undefined, {
    skip: !user || !user.id, // Skip query if user is not authenticated
  });

  useEffect(() => {
    console.log('upcoming meetings:::', upcomingMeetingsData);

  }, [upcomingMeetingsData]);

  const [deleteMeeting, { isLoading: deleteLoading, error: deleteError }] = useDeleteMeetingMutation();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    console.log('tab event:::', event);
    setTabValue(newValue);
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleJoinMeeting = (meeting: ZoomMeeting) => {
    router.push(`/meetings/${meeting.id}/join`);
  };

  const handleEditMeeting = (meeting: ZoomMeeting) => {
    setSelectedMeeting(meeting);
    setEditDialogOpen(true);
  };

  const handleDeleteMeeting = (meeting: ZoomMeeting) => {
    setSelectedMeeting(meeting);
    setDeleteDialogOpen(true);
  };

  const handleShareMeeting = (meeting: ZoomMeeting) => {
    setSelectedMeeting(meeting);
    setShareDialogOpen(true);
  };

  const handleCopyMeetingLink = async (meeting: ZoomMeeting) => {
    try {
      await navigator.clipboard.writeText(meeting.joinUrl);
      showSnackbar('Meeting link copied to clipboard!');
    } catch (err) {
      showSnackbar('Failed to copy link', 'error');
    }
  };

  const handleConfirmDelete = async (meeting: ZoomMeeting) => {
    try {
      await deleteMeeting(meeting.id).unwrap();
      showSnackbar('Meeting deleted successfully!');
      setDeleteDialogOpen(false);
      setSelectedMeeting(null);
      // Refresh meetings list
      refetchAllMeetings();
      refetchUpcomingMeetings();
    } catch (err) {
      showSnackbar('Failed to delete meeting', 'error');
    }
  };

  const handleEditSuccess = () => {
    showSnackbar('Meeting updated successfully!');
    setEditDialogOpen(false);
    setSelectedMeeting(null);
    // Refresh meetings list
    refetchAllMeetings();
    refetchUpcomingMeetings();
  };

  const handleJoinMeetingSuccess = (joinUrl: string) => {
    window.open(joinUrl, '_blank');
  };

  const canCreateMeeting = user?.role === 'admin' || user?.role === 'instructor';

  return (
    <WithAuth>
      <MainLayout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
            <Typography variant="h4" component="h1">
              Meetings
            </Typography>
            <Box display="flex" gap={2}>
              <Button
                variant="outlined"
                startIcon={<VideoCall />}
                onClick={() => setJoinDialogOpen(true)}
              >
                Join Meeting
              </Button>
              {canCreateMeeting && (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => setCreateDialogOpen(true)}
                >
                  Create Meeting
                </Button>
              )}
            </Box>
          </Box>

          {!canCreateMeeting && (
            <Alert severity="info" sx={{ mb: 3 }}>
              You can join meetings but cannot create them. Contact an administrator for meeting creation access.
            </Alert>
          )}

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="Upcoming" />
              <Tab label="All Meetings" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <MeetingList
              meetings={upcomingMeetingsData?.data || []}
              currentUser={user!}
              loading={upcomingLoading}
              error={upcomingError ? 'Failed to load upcoming meetings' : undefined}
              onJoinMeeting={handleJoinMeeting}
              onEditMeeting={handleEditMeeting}
              onDeleteMeeting={handleDeleteMeeting}
              onShareMeeting={handleShareMeeting}
              onCopyMeetingLink={handleCopyMeetingLink}
            />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <MeetingList
              meetings={allMeetingsData?.data || []}
              currentUser={user!}
              loading={allMeetingsLoading}
              error={allMeetingsError ? 'Failed to load meetings' : undefined}
              onJoinMeeting={handleJoinMeeting}
              onEditMeeting={handleEditMeeting}
              onDeleteMeeting={handleDeleteMeeting}
              onShareMeeting={handleShareMeeting}
              onCopyMeetingLink={handleCopyMeetingLink}
            />
          </TabPanel>

          {/* Floating Action Button for Mobile */}
          {isMobile && canCreateMeeting && (
            <Fab
              color="primary"
              aria-label="create meeting"
              sx={{
                position: 'fixed',
                bottom: 16,
                right: 16,
              }}
              onClick={() => setCreateDialogOpen(true)}
            >
              <Add />
            </Fab>
          )}

          {/* Dialogs */}
          <CreateMeetingDialog
            open={createDialogOpen}
            onClose={() => setCreateDialogOpen(false)}
            onSuccess={() => {
              // Refresh meetings list
              refetchAllMeetings();
              refetchUpcomingMeetings();
            }}
          />

          <JoinMeetingDialog
            open={joinDialogOpen}
            onClose={() => setJoinDialogOpen(false)}
            onSuccess={handleJoinMeetingSuccess}
          />

          <DeleteConfirmationDialog
            open={deleteDialogOpen}
            meeting={selectedMeeting}
            onClose={() => {
              setDeleteDialogOpen(false);
              setSelectedMeeting(null);
            }}
            onConfirm={handleConfirmDelete}
            loading={deleteLoading}
            error={deleteError ? 'Failed to delete meeting. Please try again.' : undefined}
          />

          <ShareMeetingDialog
            open={shareDialogOpen}
            meeting={selectedMeeting}
            onClose={() => {
              setShareDialogOpen(false);
              setSelectedMeeting(null);
            }}
            onCopyLink={handleCopyMeetingLink}
          />

          <EditMeetingDialog
            open={editDialogOpen}
            meeting={selectedMeeting}
            onClose={() => {
              setEditDialogOpen(false);
              setSelectedMeeting(null);
            }}
            onSuccess={handleEditSuccess}
          />

          {/* Snackbar for notifications */}
          <Snackbar
            open={snackbarOpen}
            autoHideDuration={4000}
            onClose={() => setSnackbarOpen(false)}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert
              onClose={() => setSnackbarOpen(false)}
              severity={snackbarSeverity}
              sx={{ width: '100%' }}
            >
              {snackbarMessage}
            </Alert>
          </Snackbar>
        </Container>
      </MainLayout>
    </WithAuth>
  );
};

export default MeetingsPage;
